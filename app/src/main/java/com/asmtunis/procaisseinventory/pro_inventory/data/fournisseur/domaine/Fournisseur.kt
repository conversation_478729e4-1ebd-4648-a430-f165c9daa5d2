package com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProInventoryConstants.FOURNISSEUR_TABLE)
@Serializable
data class Fournisseur  (
    @PrimaryKey
    @ColumnInfo(name = "FRS_codef")
    @SerialName("FRS_codef")
    
    var fRSCodef: String = "",

    @ColumnInfo(name = "FRS_Nomf")
    @SerialName("FRS_Nomf")
    
    var fRSNomf: String  = "",

    @ColumnInfo(name = "FRS_adrf")
    @SerialName("FRS_adrf")
    
    var fRSAdrf: String  = "",

    @ColumnInfo(name = "FRS_villef")
    @SerialName("FRS_villef")
    
    var fRSVillef: String = "",

    @ColumnInfo(name = "FRS_paysf")
    @SerialName("FRS_paysf")
    
    var fRSPaysf: String = "",

    @ColumnInfo(name = "FRS_Codepf")
    @SerialName("FRS_Codepf")
    
    var fRSCodepf: String = "",

    @ColumnInfo(name = "FRS_telf")
    @SerialName("FRS_telf")
    
    var fRSTelf: String = "",

    @ColumnInfo(name = "FRS_Gsm")
    @SerialName("FRS_Gsm")
    
    var fRSGsm: String = "",

    @ColumnInfo(name = "FRS_faxf")
    @SerialName("FRS_faxf")
    
    var fRSFaxf: String = "",

    @ColumnInfo(name = "FRS_codeTVAf")
    @SerialName("FRS_codeTVAf")
    
    var fRSCodeTVAf: String = "",

    @ColumnInfo(name = "FRS_assujette")
    @SerialName("FRS_assujette")
    
    var fRSAssujette: String = "",

    @ColumnInfo(name = "FRS_fodec")
    @SerialName("FRS_fodec")
    
    var fRSFodec: String = "",

    @ColumnInfo(name = "FRS_DC")
    @SerialName("FRS_DC")
    
    var fRSDC: String = "",

    @ColumnInfo(name = "FRS_forfetaire")
    @SerialName("FRS_forfetaire")
    
    var fRSForfetaire: String = "",

    @ColumnInfo(name = "FRS_timber")
    @SerialName("FRS_timber")
    
    var fRSTimber: String = "",

    @ColumnInfo(name = "FRS_remarque")
    @SerialName("FRS_remarque")
    
    var fRSRemarque: String = "",

    @ColumnInfo(name = "FRS_User")
    @SerialName("FRS_User")
    
    var fRSUser: String = "",

    @ColumnInfo(name = "FRS_Station")
    @SerialName("FRS_Station")
    
    var fRSStation: String = "",

    @ColumnInfo(name = "FRS_Solde")
    @SerialName("FRS_Solde")
    
    var fRSSolde: String = "",

    @ColumnInfo(name = "FRS_export")
    @SerialName("FRS_export")
    
    var fRSExport: String = "",

    @ColumnInfo(name = "FRS_DDm")
    @SerialName("FRS_DDm")
    var fRSDDm: String = "",

    @ColumnInfo(name = "FRS_Atelier")
    @SerialName("FRS_Atelier")
    var fRSAtelier: String = "",

    @ColumnInfo(name = "FRS_Debit")
    @SerialName("FRS_Debit")
    var fRSDebit: String = "",

    @ColumnInfo(name = "FRS_Credit")
    @SerialName("FRS_Credit")
    var fRSCredit: String = "",

    @ColumnInfo(name = "FRS_EX")
    @SerialName("FRS_EX")
    var fRSEX: String = "",

    @ColumnInfo(name = "Type")
    @SerialName("Type")
    var type: String = "",


    ): BaseModel()