package com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProInventoryConstants.TICKET_RAYON_TABLE)
@Serializable
data class TicketRayon(
    @SerialName("ART_Code")
    @ColumnInfo(name = "ART_Code")
    @PrimaryKey
    var aRTCode: String ="",


    @SerialName("ART_Designation")
   @ColumnInfo(name = "ART_Designation")
   var aRTDesignation: String="",

    @SerialName("ddm")
    @ColumnInfo(name = "ddm")
    var ddm: String="",
    @SerialName("ART_SYNC")
    @ColumnInfo(name = "ART_SYNC")
    var artSync: String="",


    @ColumnInfo(name = "timestamp")
    var timestamp: Long = 0,


    ): BaseModel()
