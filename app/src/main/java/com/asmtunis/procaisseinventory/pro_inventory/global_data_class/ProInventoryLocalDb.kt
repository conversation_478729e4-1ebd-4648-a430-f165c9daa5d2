package com.asmtunis.procaisseinventory.pro_inventory.global_data_class

import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
import com.asmtunis.procaisseinventory.data.famille.local.repository.FamilleLocalRepository
import com.asmtunis.procaisseinventory.data.marque.local.repository.MarqueLocalRepository
import com.asmtunis.procaisseinventory.data.parametrages.local.repository.ParametrageLocalRepository
import com.asmtunis.procaisseinventory.data.station.local.station.repository.StationLocalRepository
import com.asmtunis.procaisseinventory.data.station.local.station_stock_article.repository.StationStockArticleLocalRepository
import com.asmtunis.procaisseinventory.data.tva.local.repository.TvaLocalRepository
import com.asmtunis.procaisseinventory.data.unite.local.repository.UniteLocalRepository
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.bn_entree.repository.BonEntreeLocalRepository
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.ligne_bn_entree.repository.LigneBonEntreeLocalRepository
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.bn_livraison.repository.BonLivraisonLocalRepository
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.ligne_bn_livraison.repository.LigneBonLivraisonLocalRepository
import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.local.repository.FournisseurLocalRepository
import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.local.repository.TypePrixUnitaireHTLocalRepository
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.inv.repository.InventaireLocalRepository
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.ligne_inv.repository.LigneInventaireLocalRepository
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.local.repository.TicketRayonLocalRepository
import javax.inject.Inject
import javax.inject.Named


data class ProInventoryLocalDb @Inject constructor(

    val dataStore: DataStoreRepository,
    @Named("Station") val stations: StationLocalRepository,
    @Named("Parametrage") val parametrage: ParametrageLocalRepository,
    @Named("StationArticle") val stationsArticle: StationStockArticleLocalRepository,
    @Named("Unite") val unite: UniteLocalRepository,
    @Named("TypePrix") val typePrix: TypePrixUnitaireHTLocalRepository,
    @Named("Tva") val tva: TvaLocalRepository,
    @Named("Famille") val famille: FamilleLocalRepository,
    @Named("Fournisseur") val fournisseur: FournisseurLocalRepository,
    @Named("Marque") val marque: MarqueLocalRepository,
    @Named("BonEntree") val bonEntree: BonEntreeLocalRepository,
    @Named("LigneBonEntree") val ligneBonEntree: LigneBonEntreeLocalRepository,
    @Named("BonLivraison") val bonLivraison: BonLivraisonLocalRepository,
    @Named("LigneBonLivraison") val ligneBonLivraison: LigneBonLivraisonLocalRepository,
    @Named("Inventaire") val inventaire: InventaireLocalRepository,
    @Named("LigneInventaire") val ligneInventaire: LigneInventaireLocalRepository,
    @Named("TicketRayon") val ticketRayon: TicketRayonLocalRepository,
)