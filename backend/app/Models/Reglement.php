<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Reglement extends Model
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'Reglement';

    /**
     * The primary key for the model.
     * 
     * @var string
     */
    protected $primaryKey = 'REG_Code';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['REG_Exercice', 'REG_CodeClient', 'REG_NomPrenom', 'REG_MatFiscale', 'REG_NumCIN', 'REG_NumPiece', 'REG_TypePiece', 'REG_ModeReg', 'REG_DateReg', 'REG_Montant', 'REG_NumCarteBancaire', 'REG_Etat', 'REG_MntChéque', 'REG_MntEspece', 'REG_MntCarte', 'REGTT_TauxInteret', 'REGTT_Total', 'REGTT_NbrEchance', 'REGTT_Date1Echan', 'REGTT_Periode', 'REGTT_Remarque', 'REGTT_NPeriode', 'REGTT_MontantTraites', 'REG_RetenuSource', 'REG_Station', 'REG_User', 'REG_TauxRetenu', 'REG_Total', 'REG_MntVirement', 'REG_MntBonCommande', 'REG_export', 'REG_DDm', 'REG_Type', 'REG_TransactionCB', 'REG_Fournisseur', 'REG_Recu', 'REG_NumBord'];

    /**
     * @return HasMany
     */
    public function bonCommandes()
    {
        return $this->hasMany('App\BonCommande', 'Reglement', 'REG_Code');
    }

    /**
     * @return HasMany
     */
    public function reglementAvoirs()
    {
        return $this->hasMany('App\ReglementAvoir', 'REGA_NumReg', 'REG_Code');
    }

    /**
     * @return HasMany
     */
    public function reglementPieces()
    {
        return $this->hasMany('App\ReglementPiece', 'REGP_NumReg', 'REG_Code');
    }
}
