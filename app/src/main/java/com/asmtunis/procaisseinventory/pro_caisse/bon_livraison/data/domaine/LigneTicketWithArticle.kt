package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class LigneTicketWithArticle (
    @Embedded
    @SerialName("ligneTicket")
    var ligneTicket: LigneTicket? = null,

    @Relation(
        parentColumn = "LT_CodArt",
        entityColumn = "ART_Code"
    )
    @SerialName("article")
    var article: Article? = null,
    )