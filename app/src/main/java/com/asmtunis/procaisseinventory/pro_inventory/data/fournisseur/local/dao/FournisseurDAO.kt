package com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.FOURNISSEUR_TABLE
import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.domaine.Fournisseur
import kotlinx.coroutines.flow.Flow


@Dao
interface FournisseurDAO {
    @get:Query("SELECT * FROM $FOURNISSEUR_TABLE")
    val all: Flow<List<Fournisseur>>

    @get:Query("SELECT FRS_Nomf FROM $FOURNISSEUR_TABLE")
    val list: Flow<List<String>>

    @Query("SELECT FRS_Nomf FROM $FOURNISSEUR_TABLE where FRS_codef =:code")
    fun getNameByCode(code: String): Flow<String>

    @Query("SELECT COUNT(*) FROM $FOURNISSEUR_TABLE")
    fun count(): Flow<Int>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: Fournisseur)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<Fournisseur>)

    @Query("DELETE FROM $FOURNISSEUR_TABLE")
    fun deleteAll()
}
