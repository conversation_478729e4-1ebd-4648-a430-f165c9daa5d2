package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.ligne_bn_livraison.repository

import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.ligne_bn_livraison.dao.LigneBonLivraisonDAO
import kotlinx.coroutines.flow.Flow


class LigneBonLivraisonLocalRepositoryImpl(
        private val ligneBonLivraisonDAO: LigneBonLivraisonDAO
    ) : LigneBonLivraisonLocalRepository {
    override fun upsertAll(value: List<LigneBonLivraison>) =
        ligneBonLivraisonDAO.insertAll(value)

    override fun upsert(value: LigneBonLivraison) =
        ligneBonLivraisonDAO.insert(value)

    override fun deleteAll()  =
        ligneBonLivraisonDAO.deleteAll()

    override fun deleteList(bonLivraisonList: List<LigneBonLivraison>) = ligneBonLivraisonDAO.deleteList(bonLivraisonList = bonLivraisonList)
    override fun updateState(code: String, codeM:String, exercice: String) = ligneBonLivraisonDAO.updateState(code = code, codeM = codeM, exercice = exercice)


    override fun deleteByCodeAndCodeArticle(num: String, exercice: String,codeArticle : String) =
        ligneBonLivraisonDAO.deleteByCodeAndCodeArticle(num = num, exercice = exercice, codeArticle = codeArticle)

    override fun getByCode(num: String, exercice: String): Flow<List<LigneBonLivraison>> =
        ligneBonLivraisonDAO.getByCode(num = num, exercice = exercice)


}