package com.asmtunis.procaisseinventory.shared_ui_components.image_pager

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.twotone.ExitToApp
import androidx.compose.material.icons.outlined.ImageNotSupported
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import coil3.compose.SubcomposeAsyncImage
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.utils.ImageUtils.getImageModel
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.simapps.ui_kit.ZoomableImage


@Composable
fun PageContent(
    showExitIcon: Boolean = true,
    page: Int,
    canModify: Boolean,
    imageList: List<ImagePieceJoint>,
    onDismissRequest: () -> Unit,
    onDeleteClick: (ImagePieceJoint) -> Unit,
) {

//Column (
//    verticalArrangement = Arrangement.Center,
//    horizontalAlignment = Alignment.End,
//) {




    Box(
        modifier = Modifier.padding(12.dp),
    ) {
        ZoomableImage {
            SubcomposeAsyncImage(
                model = getImageModel(image = imageList[page]),
                modifier = Modifier.fillMaxHeight(),
                contentDescription = "",
                loading = { LottieAnim(lotti = R.raw.loading) },
                error = {
                    Icon(
                        modifier = Modifier.size(250.dp),
                        imageVector = Icons.Outlined.ImageNotSupported,
                        contentDescription = null,
                    )
                }
            )
        }
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Start,
            verticalAlignment = Alignment.Top
        ) {


            CustomBadgeBox(
                modifier = Modifier.padding(start = 9.dp),
                page = page,
                totalPages = imageList.size,
                showIcon = canModify && !imageList[page].isSync,
                onDeleteClick = onDeleteClick,
                image = imageList[page],
            )

            if(!imageList[page].isSync) {
                LottieAnim(lotti = R.raw.waiting_document, size = 40.dp, modifier = Modifier.padding(start = 16.dp))
            }



            if(showExitIcon) {
                Spacer(modifier = Modifier.weight(1F))
                Icon(
                    modifier = Modifier
                        //.padding(start = 12.dp)
                        .size(35.dp)
                        .clickable {
                            onDismissRequest()
                        },
                    tint = MaterialTheme.colorScheme.error,
                    imageVector= Icons.AutoMirrored.TwoTone.ExitToApp,
                    contentDescription = "Delete Image Number"+ (page + 1).toString()
                )
            }
        }




    }
//}


}


