package com.asmtunis.procaisseinventory.auth.login.data.local.di

import com.asmtunis.procaisseinventory.auth.login.data.local.dao.AuthorizationDAO
import com.asmtunis.procaisseinventory.auth.login.data.local.dao.UtilisateurDAO
import com.asmtunis.procaisseinventory.auth.login.data.local.repository.AuthorizationLocalRepository
import com.asmtunis.procaisseinventory.auth.login.data.local.repository.AuthorizationLocalRepositoryImpl
import com.asmtunis.procaisseinventory.auth.login.data.local.repository.UtilisateurRoomRepository
import com.asmtunis.procaisseinventory.auth.login.data.local.repository.UtilisateurRoomRepositoryImpl
import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class UserLocalModule {

    @Provides
    @Singleton
    fun provideClientsDao(
        utilisateurProCaisseDataBase: ProCaisseDataBase
    ) = utilisateurProCaisseDataBase.utilisateurDAO()

    @Provides
    @Singleton
    @Named("Utilisateur")
    fun provideClientsRepository(
        utilisateurDAO: UtilisateurDAO
    ): UtilisateurRoomRepository = UtilisateurRoomRepositoryImpl(utilisateurDAO = utilisateurDAO)




    @Provides
    @Singleton
    fun provideAuthorizationDao(
        utilisateurProCaisseDataBase: ProCaisseDataBase
    ) = utilisateurProCaisseDataBase.authorizationDAO()

    @Provides
    @Singleton
    @Named("Authorization")
    fun provideAuthorizationRepository(
        authorizationDAO: AuthorizationDAO
    ): AuthorizationLocalRepository = AuthorizationLocalRepositoryImpl(authorizationDAO = authorizationDAO)


}