package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.IMMOBILISATION_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import kotlinx.coroutines.flow.Flow


@Dao
interface ImmobilisationDAO {
    @get:Query("SELECT * FROM $IMMOBILISATION_TABLE order by CLI_NomPren asc")
    val all: Flow<List<Immobilisation>?>

    @get:Query("SELECT * FROM $IMMOBILISATION_TABLE where TyEmpImNom= 'Societe' and Clt_Immo = 1 order by CLI_NomPren asc")
    val allSociete: Flow<List<Immobilisation>?>

    @Query("SELECT * FROM $IMMOBILISATION_TABLE where TyEmpImNom= 'Site' and Clt_Immo =1 and Clt_ImoCodeP = :codeSociete order by CLI_NomPren asc")
    fun getAllSite(codeSociete: String): Flow<List<Immobilisation>?>


    @get:Query("SELECT * FROM $IMMOBILISATION_TABLE WHERE isSync=0 and  Status='INSERTED_CODE_BARE'")
    val getNotSync: Flow<List<Immobilisation>>

    @Query("SELECT * FROM $IMMOBILISATION_TABLE where TyEmpImNom= 'Bloc' and Clt_Immo =1 and Clt_ImoCodeP = :codeSite order by CLI_NomPren asc")
    fun getAllBloc(codeSite: String): Flow<List<Immobilisation>?>

    @Query("SELECT * FROM $IMMOBILISATION_TABLE where TyEmpImNom= 'Batiment' and Clt_Immo =1 and Clt_ImoCodeP = :codeBatiment order by CLI_NomPren asc")
    fun getAllBatiment(codeBatiment: String): Flow<List<Immobilisation>?>



    @Query("SELECT * FROM $IMMOBILISATION_TABLE where Clt_Immo =1 and CLI_Code =:codeParent order by CLI_NomPren asc")
    fun getParent(codeParent: String): Flow<Immobilisation?>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: Immobilisation)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<Immobilisation>)

    @Query("DELETE FROM $IMMOBILISATION_TABLE")
    fun deleteAll()


    @Query("UPDATE $IMMOBILISATION_TABLE SET Clt_ImoCB = :immoCB, isSync= :isSynced,  Status= :status where CLI_Code = :cliCode")
    fun setZoneConsomationImmoCB(immoCB: String, cliCode: String, isSynced: Boolean, status: String)



    @Query("UPDATE $IMMOBILISATION_TABLE SET isBatimentByUser = 1 where CLI_Code = :cliCode")
    fun setIsBatimentUser(cliCode: String)


    @Query("SELECT * FROM $IMMOBILISATION_TABLE where TyEmpImNom= 'ZONE DE CONSOMMATION' and Clt_Immo = 1 and Clt_ImoCB = :imoCB order by CLI_NomPren asc")
    fun getAllZoneConsomationByImoCB(imoCB: String): Flow<List<Immobilisation>?>


    @Query(
        "SELECT * FROM $IMMOBILISATION_TABLE " +
                "WHERE CLI_NomPren LIKE '%' || :filterString || '%' " +
                "and  TyEmpImNom =:tyEmpImNom " +
                "and  isBatimentByUser = :byUser " +
                " ORDER BY " +

                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 1 THEN CLI_NomPren END ASC, " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 2 THEN CLI_NomPren END DESC, " +
                "CASE WHEN :sortBy = 'CLI_Code'  AND :isAsc = 1 THEN CLI_Code END ASC, " +
                "CASE WHEN :sortBy = 'CLI_Code'  AND :isAsc = 2 THEN CLI_Code END DESC, " +
                "CASE WHEN :sortBy = 'Clt_ImoCB'  AND :isAsc = 1 THEN Clt_ImoCB END ASC, " +
                "CASE WHEN :sortBy = 'Clt_ImoCB'  AND :isAsc = 2 THEN Clt_ImoCB END DESC "

    )
    fun filterByName(filterString: String, sortBy: String, isAsc: Int, byUser: Boolean, tyEmpImNom: String): Flow<List<Immobilisation>>

    @Query(
        "SELECT * FROM $IMMOBILISATION_TABLE " +
                "WHERE CLI_Code LIKE '%' || :filterString || '%' " +
                "and  TyEmpImNom =:tyEmpImNom " +
                "and  isBatimentByUser = :byUser " +
                " ORDER BY " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 1 THEN CLI_NomPren END ASC, " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 2 THEN CLI_NomPren END DESC, " +
                "CASE WHEN :sortBy = 'CLI_Code'  AND :isAsc = 1 THEN CLI_Code END ASC, " +
                "CASE WHEN :sortBy = 'CLI_Code'  AND :isAsc = 2 THEN CLI_Code END DESC, " +
                "CASE WHEN :sortBy = 'Clt_ImoCB'  AND :isAsc = 1 THEN Clt_ImoCB END ASC, " +
                "CASE WHEN :sortBy = 'Clt_ImoCB'  AND :isAsc = 2 THEN Clt_ImoCB END DESC "
    )
    fun filterByCLICode(filterString: String, sortBy: String, isAsc: Int, byUser: Boolean, tyEmpImNom: String): Flow<List<Immobilisation>>

    @Query(
        "SELECT * FROM $IMMOBILISATION_TABLE " +
                "WHERE Clt_ImoCB LIKE '%' || :filterString || '%'" +
                "and  isBatimentByUser = :byUser " +
                "and  TyEmpImNom =:tyEmpImNom " +
                " ORDER BY " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 1 THEN CLI_NomPren END ASC, " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 2 THEN CLI_NomPren END DESC, " +
                "CASE WHEN :sortBy = 'CLI_Code'  AND :isAsc = 1 THEN CLI_Code END ASC, " +
                "CASE WHEN :sortBy = 'CLI_Code'  AND :isAsc = 2 THEN CLI_Code END DESC, " +
                "CASE WHEN :sortBy = 'Clt_ImoCB'  AND :isAsc = 1 THEN Clt_ImoCB END ASC, " +
                "CASE WHEN :sortBy = 'Clt_ImoCB'  AND :isAsc = 2 THEN Clt_ImoCB END DESC "
    )
    fun filterByCltImoCB(filterString: String, sortBy: String, isAsc: Int, byUser: Boolean, tyEmpImNom: String): Flow<List<Immobilisation>>

    @Query(
        "SELECT * FROM $IMMOBILISATION_TABLE " +
                "WHERE TyEmpImNom  =:tyEmpImNom " +
                "and  isBatimentByUser = :byUser " +
                " ORDER BY " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 1 THEN CLI_NomPren END ASC, " +
                "CASE WHEN :sortBy = 'CLI_NomPren'  AND :isAsc = 2 THEN CLI_NomPren END DESC, " +
                "CASE WHEN :sortBy = 'CLI_Code'  AND :isAsc = 1 THEN CLI_Code END ASC, " +
                "CASE WHEN :sortBy = 'CLI_Code'  AND :isAsc = 2 THEN CLI_Code END DESC, " +
                "CASE WHEN :sortBy = 'Clt_ImoCB'  AND :isAsc = 1 THEN Clt_ImoCB END ASC, " +
                "CASE WHEN :sortBy = 'Clt_ImoCB'  AND :isAsc = 2 THEN Clt_ImoCB END DESC "
    )
    fun getAllFiltred(isAsc: Int, sortBy: String, byUser: Boolean, tyEmpImNom: String): Flow<List<Immobilisation>>


}

