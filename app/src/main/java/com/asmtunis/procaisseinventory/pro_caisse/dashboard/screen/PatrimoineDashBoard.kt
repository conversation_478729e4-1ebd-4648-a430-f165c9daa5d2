package com.asmtunis.procaisseinventory.pro_caisse.dashboard.screen

import android.graphics.Bitmap
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel

@Composable
fun PatrimoineDashBoard(
    navDrawerViewModel: NavigationDrawerViewModel,
    exerciceCode: String,
    utilisateur: Utilisateur,
    logo: Bitmap?,
    haveClotureSessionAutoAuthorisation: <PERSON><PERSON><PERSON>,
) {
    AsyncImage(
        model = logo?.asImageBitmap()?: "",
        contentDescription = "logo",
        modifier = Modifier
            .padding(top = 30.dp)
            .size(150.dp),
        error = painterResource(id = R.drawable.ic_asm),
    )
    Spacer(modifier = Modifier.height(12.dp))
    Text(
        text = utilisateur.Nom + " " + utilisateur.Prenom,
        fontWeight = MaterialTheme.typography.titleMedium.fontWeight,
        fontSize = MaterialTheme.typography.titleMedium.fontSize,
        color = MaterialTheme.colorScheme.outline,
    )

    Spacer(modifier = Modifier.height(6.dp))
    Text(text = stringResource(R.string.station_value, utilisateur.Station))
    Spacer(modifier = Modifier.height(6.dp))

    if (navDrawerViewModel.sessionCaisse.sCIdSCaisse != "" && haveClotureSessionAutoAuthorisation) {
        Text(
            maxLines = 2,
            text = navDrawerViewModel.sessionCaisse.sCIdSCaisse + " (" + exerciceCode.ifBlank { "N/A" } + ")",
        )
    }
}