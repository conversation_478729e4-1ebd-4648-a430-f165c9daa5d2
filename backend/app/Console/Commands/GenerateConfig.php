<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class GenerateConfig extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:config';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

      /*  if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            $bat_path = sprintf('%s %s %s %s',
                env('CONFIG_BATCH_FILE'),
                env('SERVER_PORT'),
                env('PROJECT_PATH'),
                env('PROJECT_ALIAS'));
            return system(sprintf("start /b %s", $bat_path));
        } else {
            return true;
        }*/
    }
}
