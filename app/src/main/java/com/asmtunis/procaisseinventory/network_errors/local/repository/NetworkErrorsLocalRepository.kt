package com.asmtunis.procaisseinventory.network_errors.local.repository

import com.asmtunis.procaisseinventory.network_errors.domaine.NetworkError
import kotlinx.coroutines.flow.Flow


interface NetworkErrorsLocalRepository {

        fun getAll(): Flow<List<NetworkError>>

        fun getOneByUrlAndExtraInfo(url: String, extraInfo: String): Flow<NetworkError>



        fun insert(item: NetworkError)

        fun deleteByUrlAndExtraInfo(url: String, extraInfo: String)

        fun deleteAll()
}