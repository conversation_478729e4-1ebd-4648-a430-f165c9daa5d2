package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals.TICKET_FACTURE_DEJA
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.AUTO_FACTURE_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.sync.SyncEntity
import com.asmtunis.procaisseinventory.core.sync.SyncManager
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils.timbersValueSum
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.timbre.domaine.Timbre
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject

@HiltViewModel
class SyncBonLivraisonViewModel
@Inject
constructor(
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val syncManager: SyncManager,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val listenNetwork: ListenNetwork,
    private val dataStoreRepository: DataStoreRepository,
    // app: Application
) : ViewModel() {


    // Sync state from SyncManager
    val syncState = syncManager.syncState
    val bonLivraisonSyncState = syncManager.entitySyncStates
        .map { it[SyncEntity.BON_LIVRAISON] }

    private var autoSyncState  by mutableStateOf(false)
    private var autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(key = PROCAISSE_AUTO_SYNC_AUTHORISATION, default = true).distinctUntilChanged()
    private var listActifTimberFlow = proCaisseLocalDb.timbre.getActif().distinctUntilChanged()
    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()

    private var  connected  by mutableStateOf(false)

    var responseAddBatchTicketWithLignesState: RemoteResponseState<List<TicketUpdate>> by mutableStateOf(RemoteResponseState())
        private set
    var notSyncAddBatchTicketWithLignesObj : String by mutableStateOf("")
        private set
    var responseFactureTicket: RemoteResponseState<List<TicketUpdate>> by mutableStateOf(RemoteResponseState())
        private set

    var ticketsWithLinesAndPaymentsNotSync: List<TicketWithFactureAndPayments> by mutableStateOf(emptyList())
        private set
    init {
        getNotSyncBonLivraison()
    }


    var listActifTimber by  mutableStateOf(emptyList<Timbre>())
        private set


    private fun getNotSyncBonLivraison() {
        viewModelScope.launch {
            val bonLivraisonNotSyncFlow = proCaisseLocalDb.bonLivraison.notSynced().distinctUntilChanged()


            combine(networkFlow, bonLivraisonNotSyncFlow, autoSyncFlow, listActifTimberFlow) { isConnected, inventaireNotSyncList, autoSync, listActifTimberFlow ->
                listActifTimber = listActifTimberFlow.ifEmpty { emptyList() }
                connected = isConnected
                autoSyncState = autoSync
                inventaireNotSyncList?.ifEmpty { emptyList() }?: emptyList()
            }.collect {

                if (it.isEmpty()) {
                    ticketsWithLinesAndPaymentsNotSync = emptyList()
                    return@collect
                }
                ticketsWithLinesAndPaymentsNotSync = it
                if(connected && autoSyncState) syncBonLivraison()
            }
        }
    }


    private fun setFactureTimbreAndRevImp() {

        val timberValue = timbersValueSum(listActifTimber)

        ticketsWithLinesAndPaymentsNotSync.forEach {ticketsWithLinesAndPayments->
            val client = ticketsWithLinesAndPayments.client
            val ticket = ticketsWithLinesAndPayments.ticket

//                 if ((client?.cltMntRevImp ?: 0.0) > 0.0) {
//
//                     var mntTTC = stringToDouble(ticket?.tIKMtTTC) * (1 + (client?.cltMntRevImp ?: 0.0) / 100)
//
//                     if(stringToDouble(client?.cLITimbre) == 0.0) {
//                         mntTTC = timberValue
//                     }
//
//                    ticketsWithLinesAndPayments.ticket = ticket?.copy(
//                       mntRevImp = stringToDouble(ticket.tIKMtTTC) * ((client?.cltMntRevImp ?: 0.0) / 100),
//                        tIKMtTTC = convertDoubleToDoubleFormat(mntTTC),
//                        tIKTimbre =  if (stringToDouble(client?.cLITimbre?:"0") == 0.0) "0"
//                        else listActifTimber.first().tIMBCode
//                    )
//                }


            ticketsWithLinesAndPayments.ticket = ticket?.copy(
                tIKTimbre =  if (stringToDouble(client?.cLITimbre?:"0") == 0.0) "0" else listActifTimber.first().tIMBCode
            )
        }

    }

    /**
     * 🚀 SYNCMANAGER INTEGRATION: Main sync method with SyncManager support
     */
    fun syncBonLivraison(selectedTicket: TicketWithFactureAndPayments = TicketWithFactureAndPayments()) {
        println("🎯 SYNC ENTRY POINT: syncBonLivraison called")
        println("📦 SELECTED BON LIVRAISON: ${selectedTicket.ticket?.tIKNumTicketM ?: "EMPTY/BULK SYNC"}")

        viewModelScope.launch {
            try {
                println("🔄 SETTING LOADING STATE...")
                responseAddBatchTicketWithLignesState = RemoteResponseState(data = null, loading = true, error = null)

                if (selectedTicket != TicketWithFactureAndPayments()) {
                    println("📝 INDIVIDUAL BON LIVRAISON SYNC: Using original logic")
                    // For individual ticket sync, use the original logic
                    syncBonLivraisonOriginal(selectedTicket)
                } else {
                    println("📦 BULK SYNC: Using SyncManager")
                    println("🔍 BULK SYNC TICKETS TO PROCESS:")
                    ticketsWithLinesAndPaymentsNotSync.forEachIndexed { index, ticket ->
                        val isFromClient = ticket.ticket?.tIKNumTicketM?.contains("BL_M_") == true
                        val isFromBC = ticket.ticket?.tIKNumTicketM?.contains("BCC") == true
                        val type = if (isFromClient) "CLIENT-CREATED" else if (isFromBC) "BC-TRANSFERRED" else "UNKNOWN"
                        println("   [$index] $type BL: ${ticket.ticket?.tIKNumTicketM} (${ticket.ligneTicket?.size ?: 0} lines)")
                    }

                    // Use SyncManager for bulk sync
                    val result = syncManager.syncEntity(SyncEntity.BON_LIVRAISON)

                    if (result.isSuccess) {
                        println("✅ BULK SYNC SUCCESS")
                        responseAddBatchTicketWithLignesState = RemoteResponseState(
                            data = emptyList(), // SyncManager doesn't return TicketUpdate list
                            loading = false,
                            error = null
                        )
                    } else {
                        println("❌ BULK SYNC FAILED: ${result.error}")
                        responseAddBatchTicketWithLignesState = RemoteResponseState(
                            data = null,
                            loading = false,
                            error = result.error ?: "Sync failed"
                        )
                    }
                }
            } catch (exception: Exception) {
                println("💥 SYNC EXCEPTION: ${exception.message}")
                exception.printStackTrace()
                responseAddBatchTicketWithLignesState = RemoteResponseState(
                    data = null,
                    loading = false,
                    error = exception.message ?: "Unknown error"
                )
            }
        }
    }

    /**
     * 🔄 ORIGINAL SYNC LOGIC: Your exact original implementation
     */
    private fun syncBonLivraisonOriginal(selectedTicket: TicketWithFactureAndPayments = TicketWithFactureAndPayments()) {
        viewModelScope.launch {
            val autoFacture = proCaisseLocalDb.dataStore.getBoolean(AUTO_FACTURE_AUTHORISATION).first()

            if(autoFacture) {
                setFactureTimbreAndRevImp()
            }

            // 🚀 CRITICAL FIX: Clean data before sending to prevent server validation errors
            val cleanedTickets = if(selectedTicket != TicketWithFactureAndPayments()) {
                listOf(cleanTicketData(selectedTicket))
            } else {
                ticketsWithLinesAndPaymentsNotSync.map { cleanTicketData(it) }
            }

            val baseConfigObj =
                GenericObject(
                    proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                        ?.let { Json.decodeFromString(it) }?: BaseConfig(),
                    Json.encodeToJsonElement(cleanedTickets),
                )


            notSyncAddBatchTicketWithLignesObj = Json.encodeToString(baseConfigObj)

            // 🔍 DETAILED DEBUG: Log the exact data being sent to help debug "ticket n'existe pas"
            println("🎯 USING ORIGINAL TICKET ENDPOINT: addBatchTicketWithLignesTicketAndPayment")
            println("📦 SELECTED TICKET: ${selectedTicket.ticket?.tIKNumTicketM ?: "BULK_SYNC"}")

            if (selectedTicket != TicketWithFactureAndPayments()) {
                val ticket = selectedTicket.ticket
                val isFromClient = ticket?.tIKNumTicketM?.contains("BL_M_") == true
                val isFromBC = ticket?.tIKNumTicketM?.contains("BCC") == true

                println("🔍 INDIVIDUAL TICKET DEBUG:")
                println("   - Type: ${if (isFromClient) "CLIENT-CREATED BL" else if (isFromBC) "BC-TRANSFERRED BL" else "UNKNOWN"}")
                println("   - tIKNumTicketM: ${ticket?.tIKNumTicketM}")
                println("   - tIKNumTicket: ${ticket?.tIKNumTicket}")
                println("   - tIKExerc: ${ticket?.tIKExerc}")
                println("   - tIKIdCarnet: ${ticket?.tIKIdCarnet}")
                println("   - tIKCodClt: ${ticket?.tIKCodClt}")
                println("   - tIKNumeroBL: ${ticket?.tIKNumeroBL}")
                println("   - tIKSource: ${ticket?.tIKSource}")
                println("   - tIKStation: ${ticket?.tIKStation}")
                println("   - tIKUser: ${ticket?.tIKUser}")
                println("   - Line Items: ${selectedTicket.ligneTicket?.size ?: 0}")

                if (isFromClient) {
                    println("🎯 CLIENT-CREATED BL SPECIFIC DEBUG:")
                    println("   - tIKNomClient: ${ticket?.tIKNomClient}")
                    println("   - tIKMtTTC: ${ticket?.tIKMtTTC}")
                    println("   - tIKMtHT: ${ticket?.tIKMtHT}")
                    println("   - tIKEtat: ${ticket?.tIKEtat}")
                    println("   - tIKDateHeureTicket: ${ticket?.tIKDateHeureTicket}")
                }
            } else {
                println("🔍 BULK SYNC DEBUG: ${ticketsWithLinesAndPaymentsNotSync.size} tickets")
                ticketsWithLinesAndPaymentsNotSync.forEachIndexed { index, ticket ->
                    val isFromClient = ticket.ticket?.tIKNumTicketM?.contains("BL_M_") == true
                    val isFromBC = ticket.ticket?.tIKNumTicketM?.contains("BCC") == true
                    val type = if (isFromClient) "CLIENT" else if (isFromBC) "BC" else "UNKNOWN"
                    println("   [$index] $type: ${ticket.ticket?.tIKNumTicketM} → ${ticket.ticket?.tIKNumTicket} (${ticket.ligneTicket?.size ?: 0} lines)")
                }
            }
            println("🔍 AUTO_FACTURE: $autoFacture")
            println("🔍 JSON LENGTH: ${notSyncAddBatchTicketWithLignesObj.length} chars")

            // 🚨 CRITICAL DEBUG: Log the actual JSON being sent to server
            println("🚨 REQUEST PAYLOAD DEBUG:")
            println("📤 JSON BEING SENT TO SERVER:")
            if (notSyncAddBatchTicketWithLignesObj.length < 2000) {
                println(notSyncAddBatchTicketWithLignesObj)
            } else {
                println("${notSyncAddBatchTicketWithLignesObj.take(1000)}... [TRUNCATED - ${notSyncAddBatchTicketWithLignesObj.length} total chars]")
            }
            println("📤 END OF REQUEST PAYLOAD")

            proCaisseRemote.ticket.addBatchTicketWithLignesTicketAndPayment(
                baseConfig = notSyncAddBatchTicketWithLignesObj,
                autoFacture = autoFacture,
            ).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        responseAddBatchTicketWithLignesState = RemoteResponseState(data = result.data, loading = false, error = null)

                        println("✅ TICKET SYNC SUCCESS: ${result.data?.size ?: 0} items")

                        // 🚨 CRITICAL DEBUG: Analyze why server returned empty array
                        if (result.data.isNullOrEmpty()) {
                            println("🚨 SERVER RETURNED EMPTY ARRAY - DEBUGGING:")
                            println("   📤 Request was sent successfully (200 OK)")
                            println("   📥 Server response: [] (empty array)")
                            println("   🤔 Possible causes:")
                            println("      - Server validation rejected tickets")
                            println("      - Missing required fields")
                            println("      - Tickets already exist on server")
                            println("      - Business logic filtered out tickets")
                            println("   🔍 Check server logs for validation errors")
                            println("   💡 Try individual ticket sync for detailed error info")
                        }

                        for (i in result.data!!.indices) {
                            val ticketUpdate = result.data[i]
                            println("🔍 PROCESSING TICKET UPDATE [$i]:")
                            println("   - tIKNumTicketM: ${ticketUpdate.tIKNumTicketM}")
                            println("   - Code: ${ticketUpdate.code}")
                            println("   - Message: ${ticketUpdate.message}")

                            if (ticketUpdate.code == "10201") {
                                println("✅ SUCCESS CODE 10201: Updating local data")
                                updateLocalData(ticketUpdate = ticketUpdate, insertFacture = autoFacture)
                            }
                            else if (ticketUpdate.code == "10200") {
                                println("✅ SUCCESS CODE 10200: Updating local data")
                                updateLocalData(ticketUpdate = ticketUpdate, insertFacture = autoFacture)
                            }
                            else {
                                println("❌ ERROR CODE ${ticketUpdate.code}: ${ticketUpdate.message}")
                                println("🚨 TICKET N'EXISTE PAS DEBUG:")
                                println("   - Failed Ticket: ${ticketUpdate.tIKNumTicketM}")
                                println("   - Error Code: ${ticketUpdate.code}")
                                println("   - Error Message: ${ticketUpdate.message}")
                                println("   - Auto Facture: $autoFacture")

                                responseAddBatchTicketWithLignesState = RemoteResponseState(data = null, loading = false, error = ticketUpdate.message)

                                proCaisseLocalDb.bonLivraison.updateSyncErrorMsg(
                                    tikNumTicketM = ticketUpdate.tIKNumTicketM!!,
                                    errorMsg = ticketUpdate.message ?: "UnknownError",
                                )
                            }
                        }
                    }

                    is DataResult.Loading -> {
                        responseAddBatchTicketWithLignesState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        responseAddBatchTicketWithLignesState = RemoteResponseState(data = null, loading = false, error = result.message, message = selectedTicket.ticket?.tIKNumTicket)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }

    fun facturerBL(ticketWithFactureAndPayments: TicketWithFactureAndPayments) {

        ticketsWithLinesAndPaymentsNotSync = emptyList()
        ticketsWithLinesAndPaymentsNotSync = listOf(ticketWithFactureAndPayments)
        setFactureTimbreAndRevImp()

        viewModelScope.launch(dispatcherIO) {
            val baseConfigObj =
                GenericObject(
                    proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                        ?.let { Json.decodeFromString(it) }?: BaseConfig(),
                    Json.encodeToJsonElement(ticketsWithLinesAndPaymentsNotSync),
                )

            proCaisseRemote.ticket.addBatchFactureWithLines(baseConfig = Json.encodeToString(baseConfigObj)).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        responseFactureTicket = RemoteResponseState(data = result.data, loading = false, error = null)
                        ticketsWithLinesAndPaymentsNotSync = emptyList()
                        for (i in result.data!!.indices) {
                            val ticketUpdate = result.data[i]
                            if (!ticketUpdate.code.equals("10200") && !ticketUpdate.code.equals("10201") && !ticketUpdate.code.equals("10304")) {
                                // TODO HANDLE OTHER CASES HERE
                                return@onEach
                            }

                            updateLocalData(ticketUpdate = ticketUpdate, insertFacture = true)
                        }
                    }

                    is DataResult.Loading -> {
                        responseFactureTicket = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        ticketsWithLinesAndPaymentsNotSync = emptyList()
                        responseFactureTicket = RemoteResponseState(data = null, loading = false, error = result.message)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }

    private fun updateClientSold(
        cLICode: String,
        ticketUpdate: TicketUpdate,
    )  {
        viewModelScope.launch(dispatcherIO) {
            proCaisseLocalDb.clients.updateMoneyClient(
                codeClt = cLICode,
                soldClient = ticketUpdate.soldeClient?: "",
                cliCredit = ticketUpdate.credit?: "",
                cliDebit = ticketUpdate.debit?: "",
            )
        }
    }




    private fun updateLocalData(ticketUpdate: TicketUpdate, insertFacture: Boolean) {
        val ddm = getCurrentDateTime()

        if(insertFacture) {
            ticketUpdate.facture?.let { proCaisseLocalDb.facture.upsert(it) }
        }
        proCaisseLocalDb.ligneBonLivraison.updateNumTicket(
            codeM = ticketUpdate.tIKNumTicketM?: "",
            newCode = ticketUpdate.tIKNumTicket,
            exercice = ticketUpdate.tIKExerc?: "",
            carnet = ticketUpdate.tIKIdCarnet?: "",
        )
        if (ticketUpdate.tIKNumeroBL == null) {
            // Auto facture == true

            proCaisseLocalDb.bonLivraison.updateBLNumber(
                tikNumTicket = ticketUpdate.tIKNumTicket,
                tikNumTicketM = ticketUpdate.tIKNumTicketM?: "",
                tikDdm = ddm,
            )
        } else {
            // Auto facture == false
            proCaisseLocalDb.bonLivraison.updateTicketNumber(
                tikNumBl = ticketUpdate.tIKNumeroBL?: "",
                tikNumTicket = ticketUpdate.tIKNumTicket,
                tikNumTicketM = ticketUpdate.tIKNumTicketM!!,
                tikDdm = ddm,
            )
        }

        if (ticketUpdate.observation != null) {
            if (!ticketUpdate.observation.equals("")) {
                proCaisseLocalDb.bonCommande.updateObservation(
                    devObservation = ticketUpdate.observation?: "",
                    devNum = ticketUpdate.tIKNumTicketM?: "",
                    exercie = ticketUpdate.tIKExerc?: "",
                )
            }
        }

        if (ticketUpdate.message != null) {
            if (ticketUpdate.message.equals(TICKET_FACTURE_DEJA)) {
                proCaisseLocalDb.bonCommande.updateObservation(
                    devObservation = ticketUpdate.message?: "",
                    devNum = ticketUpdate.tIKNumTicketM?: "",
                    exercie = ticketUpdate.tIKExerc?: "",
                )
            }
        }

        updateClientSold(
            cLICode = ticketUpdate.codeClient?: "",
            ticketUpdate = ticketUpdate,
        )

        // TODO ONLE EXC WHEN IS NOT CREDIT
        proCaisseLocalDb.reglementCaisse.updateRegCodeAndState(
            regCode = ticketUpdate.tIKNumTicketM?: "",
            regCodeM = ticketUpdate.tIKNumTicketM?: "",
        )

        proCaisseLocalDb.chequeCaisse.updateRegCodeAndState(
            regCode = ticketUpdate.tIKNumTicketM?: "",
            regCodeM = ticketUpdate.tIKNumTicketM?: "",
        )

        proCaisseLocalDb.ticketResto.updateRegCodeAndState(
            regCode = ticketUpdate.tIKNumTicketM?: "",
            regCodeM = ticketUpdate.tIKNumTicketM?: "",
        )
    }






    // ========================================
    // 🚀 SYNCMANAGER INTEGRATION METHODS
    // ========================================

    /**
     * Trigger manual sync for BonLivraison entities
     */
    fun triggerManualSync() {
        syncBonLivraison()
    }

    /**
     * Check if sync is currently in progress
     */
    val isSyncing: Boolean
        get() = responseAddBatchTicketWithLignesState.loading ||
                syncState.value.isNetworkConnected

    /**
     * Get the current sync error if any
     */
    val syncError: String?
        get() = responseAddBatchTicketWithLignesState.error

    /**
     * Get the count of unsynchronized items
     */
    val unsyncedCount: Int
        get() = ticketsWithLinesAndPaymentsNotSync.size

    /**
     * Force sync using SyncManager only
     */
    fun forceSyncManagerSync() {
        viewModelScope.launch {
            val result = syncManager.syncEntity(SyncEntity.BON_LIVRAISON)
            println("🔄 FORCE SYNC RESULT: ${if (result.isSuccess) "SUCCESS" else "FAILED - ${result.error}"}")
        }
    }

    /**
     * 🚀 CRITICAL FIX: Clean ticket data to prevent server validation errors
     */
    private fun cleanTicketData(ticketWithPayments: TicketWithFactureAndPayments): TicketWithFactureAndPayments {
        val ticket = ticketWithPayments.ticket
        val ligneTickets = ticketWithPayments.ligneTicket

        // Fix ticket data issues
        val cleanedTicket = ticket?.copy(
            tIKDDm = if (ticket.tIKDDm == "empty") "" else ticket.tIKDDm,
            tIKMtHT = if (ticket.tIKMtHT == "0.0") "0" else ticket.tIKMtHT,
            tIKMtTTC = if (ticket.tIKMtTTC?.isEmpty() == true) "0" else ticket.tIKMtTTC
        )

        // Fix ligne ticket data issues
        val cleanedLigneTickets = ligneTickets?.map { ligneTicket ->
            val mtHT = ligneTicket.lTMtHT?.toDoubleOrNull() ?: 0.0
            val mtTTC = ligneTicket.lTMtTTC?.toDoubleOrNull() ?: 0.0

            // Calculate proper TVA to avoid "Infinity"
            val tva = when {
                mtHT == 0.0 && mtTTC > 0.0 -> "19.0" // Default TVA rate
                mtHT == 0.0 -> "0.0"
                else -> {
                    val calculatedTva = ((mtTTC - mtHT) / mtHT) * 100
                    if (calculatedTva.isFinite()) calculatedTva.toString() else "19.0"
                }
            }

            ligneTicket.copy(
                lTTVA = tva,
                lTPACHAT = if (ligneTicket.lTPACHAT == ".000000") "0.000000" else ligneTicket.lTPACHAT,
                lTTarif = if (ligneTicket.lTTarif == ".000000") "0.000000" else ligneTicket.lTTarif,
                lTMtHT = if (mtHT == 0.0) "0.0" else ligneTicket.lTMtHT,
                lTMtTTC = if (mtTTC == 0.0) "0.0" else ligneTicket.lTMtTTC
            )
        }

        println("🔧 CLEANED TICKET DATA:")
        println("   - Fixed TVA values from 'Infinity' to proper rates")
        println("   - Fixed decimal format issues (.000000 → 0.000000)")
        println("   - Fixed empty/null value issues")

        return ticketWithPayments.copy(
            ticket = cleanedTicket,
            ligneTicket = cleanedLigneTickets
        )
    }

}