<?php

namespace App\Services;

use Illuminate\Support\Str;

class ImageService
{
    public function saveImage(array $photo): ?string
    {
        if (isset($photo['file'], $photo['name'], $photo['table'])) {
            $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $photo['file']));
            $fileName = Str::uuid() . '_' . $photo['name'];
            $publicPath = public_path($photo['table'] . '/' . $fileName);
            if (!file_exists(dirname($publicPath))) {
                mkdir(dirname($publicPath), 0755, true);
            }
            file_put_contents($publicPath, $imageData);
            return $fileName;
        }
        return null;
    }

    public function deleteImage($fileName, $table)
    {
        $path = public_path($table . '/' . $fileName);
        if (file_exists($path)) {
            unlink($path);
        }
    }


}