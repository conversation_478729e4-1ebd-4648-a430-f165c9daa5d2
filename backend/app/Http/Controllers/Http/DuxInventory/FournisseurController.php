<?php

namespace App\Http\Controllers\Http\DuxInventory;

use App\Http\Requests\P_tierRequest;
use App\Models\DuxInventory\E_TypeTier;
use App\Models\DuxInventory\P_tier;



class FournisseurController extends BaseController
{

    public function __construct()
    {
        $this->modelClass = P_tier::class;
        $this->requestClass = P_tierRequest::class;
    }

    public function getFournisseurs()
    {
        try {
            $fournisseurType = E_TypeTier::where('Debit', '=', '1')->pluck('id');
            $fournisseurs = P_tier::whereIn('idTypeTier', $fournisseurType)->get();
            return response()->success($fournisseurs);
        } catch (\Exception $e) {
            return response()->error($e);
        }
    }


}
