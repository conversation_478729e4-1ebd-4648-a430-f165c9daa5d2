package com.asmtunis.procaisseinventory.pro_inventory.achat.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROINVENTORY_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.sync.SyncEntity
import com.asmtunis.procaisseinventory.core.sync.SyncManager
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.BonEntree
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.BonEntreeResponse
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.LigneBonEntree
import com.asmtunis.procaisseinventory.pro_inventory.data.NestedItem
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryRemote
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject

/**
 * ViewModel for synchronizing BonEntree (Inventory Entry) data
 * Handles sync operations for purchase orders and inventory entries
 */
@HiltViewModel
class SyncBonEntreeViewModel @Inject constructor(
    private val syncManager: SyncManager,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    private val proInventoryRemote: ProInventoryRemote,
    private val proInventoryLocalDb: ProInventoryLocalDb,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val listenNetwork: ListenNetwork,
) : ViewModel() {

    // Sync state from SyncManager
    val syncState = syncManager.syncState

    // Auto-sync configuration
    private var autoSyncState by mutableStateOf(false)
    private val autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(
        key = PROINVENTORY_AUTO_SYNC_AUTHORISATION,
        default = true
    ).distinctUntilChanged()
    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()
    private var connected by mutableStateOf(false)

    // Sync response state
    var responseAddBonEntreeState: RemoteResponseState<List<BonEntreeResponse>> by mutableStateOf(RemoteResponseState())
        private set

    // Unsynced data
    var bonEntreeNotSync: Map<BonEntree, List<LigneBonEntree>> by mutableStateOf(emptyMap())
        private set
    var notSyncBonEntreeObj: String by mutableStateOf("")
        private set

    init {
        observeUnsyncedBonEntree()
    }

    /**
     * Observe unsynced BonEntree data and trigger auto-sync when conditions are met
     */
    private fun observeUnsyncedBonEntree() {
        viewModelScope.launch {
            val bonEntreeNotSyncFlow = proInventoryLocalDb.bonEntree.noSynced().distinctUntilChanged()

            combine(networkFlow, bonEntreeNotSyncFlow, autoSyncFlow) { isConnected, bonEntreeNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                bonEntreeNotSyncList.ifEmpty { emptyMap() }
            }.collectLatest { bonEntreeMap ->
                if (bonEntreeMap.isEmpty()) {
                    bonEntreeNotSync = emptyMap()
                    return@collectLatest
                }
                bonEntreeNotSync = bonEntreeMap
                if (connected && autoSyncState) {
                    syncBonEntree()
                }
            }
        }
    }

    /**
     * Synchronize BonEntree data with the server
     * @param selectedBonEntree Optional specific BonEntree to sync, if null syncs all unsynced items
     */
    fun syncBonEntree(selectedBonEntree: BonEntree = BonEntree()) {
        viewModelScope.launch(dispatcherIO) {
            try {
                responseAddBonEntreeState = RemoteResponseState(data = null, loading = true, error = null)

                // Try SyncManager first
                if (selectedBonEntree == BonEntree() && bonEntreeNotSync.isNotEmpty()) {
                    val result = syncManager.syncEntity(SyncEntity.BON_ENTREE)
                    if (result.isSuccess) {
                        responseAddBonEntreeState = RemoteResponseState(
                            data = emptyList(),
                            loading = false,
                            error = null
                        )
                        return@launch
                    }
                }

                // Fallback to legacy sync method
                syncBonEntreeLegacy(selectedBonEntree)

            } catch (exception: Exception) {
                responseAddBonEntreeState = RemoteResponseState(
                    data = null,
                    loading = false,
                    error = exception.message ?: "Unknown error"
                )
            }
        }
    }

    /**
     * Legacy sync method for BonEntree
     */
    private suspend fun syncBonEntreeLegacy(selectedBonEntree: BonEntree) {
        val nestedItems: ArrayList<NestedItem<BonEntree, List<LigneBonEntree>>> = ArrayList()

        bonEntreeNotSync.forEach { (key, value) ->
            nestedItems.add(
                NestedItem(
                    parent = key,
                    children = value
                )
            )
        }

        if (selectedBonEntree != BonEntree()) {
            nestedItems.removeIf { it.parent != selectedBonEntree }
        }

        val baseConfigObj = GenericObject(
            proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                ?.let { Json.decodeFromString(it) } ?: BaseConfig(),
            Json.encodeToJsonElement(nestedItems),
        )

        notSyncBonEntreeObj = Json.encodeToString(baseConfigObj)

        proInventoryRemote.bonEntree.addBatchBonEntreesWithLines(notSyncBonEntreeObj).onEach { result ->
            when (result) {
                is DataResult.Success -> {
                    result.data?.let { responses ->
                        for (response in responses) {
                            if (response.code == 10200) {
                                proInventoryLocalDb.bonEntree.updateBonEntreeStatus(
                                    bonEntNum = response.bONENTNum,
                                    bonEntNumM = response.bONENTNumM,
                                    exercice = response.bONENTExer,
                                )
                                proInventoryLocalDb.ligneBonEntree.updateLigneBonEntreeStatus(
                                    bonEntNum = response.bONENTNum,
                                    bonEntNumM = response.bONENTNumM,
                                    exercice = response.bONENTExer,
                                )
                            }
                        }
                    }
                    responseAddBonEntreeState = RemoteResponseState(
                        data = result.data,
                        loading = false,
                        error = null
                    )
                }

                is DataResult.Loading -> {
                    responseAddBonEntreeState = RemoteResponseState(
                        data = null,
                        loading = true,
                        error = null
                    )
                }

                is DataResult.Error -> {
                    responseAddBonEntreeState = RemoteResponseState(
                        data = null,
                        loading = false,
                        error = result.message,
                        message = selectedBonEntree.bONENTNum
                    )
                }
            }
        }.flowOn(dispatcherIO).launchIn(viewModelScope)
    }

    // ========================================
    // SYNCMANAGER INTEGRATION & CONVENIENCE METHODS
    // ========================================

    /**
     * Trigger manual sync for all BonEntree entities using SyncManager
     */
    fun triggerManualSyncAll() {
        syncBonEntree()
    }

    /**
     * Check if sync is currently in progress
     */
    val isSyncing: Boolean
        get() = responseAddBonEntreeState.loading

    /**
     * Get the current sync error if any
     */
    val syncError: String?
        get() = responseAddBonEntreeState.error

    /**
     * Get the count of unsynchronized items
     */
    val unsyncedCount: Int
        get() = bonEntreeNotSync.size

    /**
     * Reset all sync states to initial state
     */
    fun resetAllSyncStates() {
        responseAddBonEntreeState = RemoteResponseState()
    }

    /**
     * Get sync status summary for UI display
     */
    val syncStatusSummary: String
        get() = when {
            isSyncing -> "Synchronisation bons d'entrée en cours..."
            syncError != null -> "Erreur de synchronisation: $syncError"
            unsyncedCount > 0 -> "$unsyncedCount bons d'entrée non synchronisés"
            else -> "Tous les bons d'entrée sont synchronisés"
        }

    /**
     * Get detailed sync information
     */
    val detailedSyncInfo: Map<String, Any>
        get() = mapOf(
            "totalItems" to bonEntreeNotSync.size,
            "isLoading" to isSyncing,
            "error" to (syncError ?: ""),
            "lastSyncObject" to notSyncBonEntreeObj,
            "networkConnected" to connected,
            "autoSyncEnabled" to autoSyncState
        )
}
