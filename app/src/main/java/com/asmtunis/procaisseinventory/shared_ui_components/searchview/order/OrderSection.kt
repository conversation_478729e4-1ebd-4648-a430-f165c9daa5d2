package com.asmtunis.procaisseinventory.shared_ui_components.searchview.order

import android.content.Context
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.ArrowDropDown
import androidx.compose.material.icons.twotone.ArrowDropUp
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SegmentedButton
import androidx.compose.material3.SegmentedButtonDefaults
import androidx.compose.material3.SingleChoiceSegmentedButtonRow
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OrderSection(
    listOrder: ListOrder = ListOrder.Date(OrderType.Descending),
    onOrderChange: (ListOrder) -> Unit,
    orderList: Array<String>,
) {
    val context = LocalContext.current
    val scrollState = rememberScrollState()
    var showCustomOrderSection by rememberSaveable { mutableStateOf(false) }
    val density = LocalDensity.current

    Row(
        modifier = Modifier.fillMaxWidth().clickable {
            showCustomOrderSection = !showCustomOrderSection
        },
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        Text(
          //  modifier = Modifier.W(),
            text =
                stringResource(
                    id = R.string.trie_par,
                    getSelectedOrderSectionLabel(
                        listOrder = listOrder,
                        orderList = orderList,
                    ),
                    getSelectedOrderTypeLabel(
                        context = context,
                        listOrder = listOrder,
                    ),
                ),
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.outline,
            fontWeight = MaterialTheme.typography.titleMedium.fontWeight,
        )
        Spacer(modifier = Modifier.weight(1F))
        Icon(
            imageVector = if(showCustomOrderSection) Icons.TwoTone.ArrowDropUp else Icons.TwoTone.ArrowDropDown,
            contentDescription = stringResource(
                id = R.string.cd_toggle_drawer

            )
        )
        Spacer(modifier = Modifier.padding(end = 12.dp))
    }

  //  Spacer(modifier = Modifier.height(12.dp))
    AnimatedVisibility(
        visible = showCustomOrderSection,
        enter = slideInVertically { with(density) { 40.dp.roundToPx() } } + fadeIn(),
        exit = fadeOut(animationSpec = keyframes { this.durationMillis = 120 }),
    ) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            SingleChoiceSegmentedButtonRow(
                modifier = Modifier.fillMaxWidth()
            ) {

                SegmentedButton(
                    shape = SegmentedButtonDefaults.itemShape(index = 0, count = orderList.size),
                    onClick = { onOrderChange(ListOrder.Title(listOrder.orderType)) },
                    selected = listOrder is ListOrder.Title
                ) {
                    Text(
                        modifier = Modifier.horizontalScroll(rememberScrollState()),
                        text= orderList.first(),
                        style = MaterialTheme.typography.bodySmall,
                        softWrap = false,
                        maxLines = 1
                    )
                }

                SegmentedButton(
                    shape = SegmentedButtonDefaults.itemShape(index = 1, count = orderList.size),
                    onClick = { onOrderChange(ListOrder.Date(listOrder.orderType)) },
                    selected = listOrder is ListOrder.Date
                ) {
                    Text(
                        modifier = Modifier.horizontalScroll(rememberScrollState()),
                        text= orderList[1],
                        style = MaterialTheme.typography.bodySmall,
                        softWrap = false,
                        maxLines = 1
                    )
                }

                if (orderList.lastIndex == 2) {

                    if (orderList[2] != "") {
                        SegmentedButton(
                            shape = SegmentedButtonDefaults.itemShape(index = 2, count = orderList.size),
                            onClick = { onOrderChange(ListOrder.Third(listOrder.orderType)) },
                            selected = listOrder is ListOrder.Third
                        ) {
                            Text(
                                modifier = Modifier.horizontalScroll(rememberScrollState()),
                                text= orderList[2],
                                style = MaterialTheme.typography.bodySmall,
                                softWrap = false,
                                maxLines = 1
                            )
                        }
                    }


                    }


            }


            Spacer(modifier = Modifier.height(6.dp))

            SingleChoiceSegmentedButtonRow {

                SegmentedButton(
                    shape = SegmentedButtonDefaults.itemShape(index = 0, count = 2),
                    onClick = { onOrderChange(listOrder.copy(OrderType.Ascending)) },
                    selected = listOrder.orderType is OrderType.Ascending
                ) {
                    Text(
                        text= stringResource(id = R.string.ascendant),
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                SegmentedButton(
                    shape = SegmentedButtonDefaults.itemShape(index = 1, count = 2),
                    onClick = { onOrderChange(listOrder.copy(OrderType.Descending)) },
                    selected = listOrder.orderType is OrderType.Descending
                ) {
                    Text(
                        text= stringResource(id = R.string.descendant),
                        style = MaterialTheme.typography.bodySmall
                    )
                }


            }

        }
    }

}

fun getSelectedOrderSectionLabel(
    listOrder: ListOrder = ListOrder.Date(OrderType.Descending),
    orderList: Array<String>,
): String =
    when (listOrder) {
        is ListOrder.Title -> orderList.first()
        is ListOrder.Date -> orderList[1]
        is ListOrder.Third -> orderList[2]
    }

fun getSelectedOrderTypeLabel(
    context: Context,
    listOrder: ListOrder = ListOrder.Date(OrderType.Descending),
): String =
    if (listOrder.orderType is OrderType.Descending) {
        context.resources.getString(R.string.descendant)
    } else {
        context.resources.getString(R.string.ascendant)
    }
