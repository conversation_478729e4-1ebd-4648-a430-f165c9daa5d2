<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use DB;

class MarqueWSMobile extends Controller
{


    public function getMarqueByCode(Request $request)
    {
        return response()->json(DB::table('marque')->where('MAR_Code', $request->id)->first());

    }


    /*fonction get data from database*/

    public function getMarques(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $marques = $connection->table('marque')->get(["MAR_Code", "MAR_Designation", "Mar_Station", "Mar_User"]);
        $marque = collect([
            'MAR_Code' => "Autre",
            'MAR_Designation' => "Autre",
            'Mar_Station' => "",
            'Mar_User' => ""
        ]);
        return $marques->merge($marque);
    }


    public function getMarqueByX(Request $request)
    {
        return response()->json(DB::table('marque')->where($request->field, $request->value)->get());

    }


    public function addMarque(Request $request)
    {
        $data = $request->json()->all();


        if (!empty ($data)) {
            $m = DB::table('marque')->insert($data);

            return response()->json($m);

        } else {
            return (false);
        }


    }


    public function updateMarque(Request $request)
    {
        $data = $request->json()->all();


        DB::table('marque')->where('MAR_Code', $data["MAR_Code"])->update($data);

        return ("Data modified");

    }


    public function deleteMarque(Request $request)
    {

        DB::table('marque')->where('MAR_Code', $request->id)->delete();
        return ("Data Deleted");
    }


}





