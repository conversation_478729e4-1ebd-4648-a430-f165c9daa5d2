package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.remote.api

import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executeDeleteApiCall
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJointAddResponse
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain.AutreVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ConcurrentVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.DeleteDataResponseVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine.PrixVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ResponseBatchDataVc
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.TypeCommunicationVC
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class VeilleConcurentielApiImpl(private val client: HttpClient) : VeilleConcurentielApi {
    override suspend fun getVCListeConcurrent(baseConfig: String): Flow<DataResult<List<ConcurrentVC>>> = flow {
        val result = executePostApiCall<List<ConcurrentVC>>(
            client = client,
            endpoint = Urls.GET_VC_LISTE_CONCURRENT,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getVCNewProduct(baseConfig: String): Flow<DataResult<List<NewProductVC>>> = flow {
        val queryParams = mapOf("user" to Globals.USER_ID)

        val result = executePostApiCall<List<NewProductVC>>(
            client = client,
            endpoint = Urls.GET_VC_NEW_PRODUCT,
            queryParams = queryParams,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getVCPromo(baseConfig: String): Flow<DataResult<List<PromoVC>>> = flow {

        val queryParams = mapOf("user" to Globals.USER_ID)
        val result = executePostApiCall<List<PromoVC>>(
            client = client,
            endpoint = Urls.GET_VC_PROMO,
            queryParams = queryParams,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getVCPrix(baseConfig: String): Flow<DataResult<List<PrixVC>>> = flow {

        val queryParams = mapOf("user" to Globals.USER_ID)
        val result = executePostApiCall<List<PrixVC>>(
            client = client,
            endpoint = Urls.GET_VC_PRIX,
            queryParams = queryParams,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getVCAutre(baseConfig: String): Flow<DataResult<List<AutreVC>>> = flow {


        val queryParams = mapOf("user" to Globals.USER_ID)
        val result = executePostApiCall<List<AutreVC>>(
            client = client,
            endpoint = Urls.GET_VC_AUTRE,
            queryParams = queryParams,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getVCTypeCommunication(baseConfig: String): Flow<DataResult<List<TypeCommunicationVC>>> = flow {

        val result = executePostApiCall<List<TypeCommunicationVC>>(
            client = client,
            endpoint = Urls.GET_VC_TYPE_COMMUNICATION,
            baseConfig = baseConfig
        )
        emitAll(result)
    }

    override suspend fun getVCImage(baseConfig: String): Flow<DataResult<List<ImagePieceJoint>>> = flow {

        val result = executePostApiCall<List<ImagePieceJoint>>(
            client = client,
            endpoint = Urls.GET_VC_IMAGE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun addBatchVCImage(baseConfig: String): Flow<DataResult<List<ImagePieceJointAddResponse>>> = flow {

        val result = executePostApiCall<List<ImagePieceJointAddResponse>>(
            client = client,
            endpoint = Urls.ADD_BATCH_VC_IMAGE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun addBatchDataVConcu(
        baseConfig: String,
        table: String
    ): Flow<DataResult<List<ResponseBatchDataVc>>> = flow {

        val queryParams = mapOf("table" to table)
        val result = executePostApiCall<List<ResponseBatchDataVc>>(
            client = client,
            endpoint = Urls.ADD_BATCH_DATA_VC_CONCU,
            queryParams = queryParams,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun deleteDataVConcu(
        baseConfig: String,
        table: String
    ): Flow<DataResult<List<DeleteDataResponseVC>>> = flow {


        val queryParams = mapOf("table" to table)
        val result = executeDeleteApiCall<List<DeleteDataResponseVC>>(
            client = client,
            endpoint = Urls.DELETE_DATA_VC_CONCU,
            queryParams = queryParams,
            baseConfig = baseConfig
        )

        emitAll(result)
    }


}