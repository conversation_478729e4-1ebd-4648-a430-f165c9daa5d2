<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use DB;

class CouleurWSMobile extends Controller
{


    public function getCouleurByCode(Request $request)
    {
        return response()->json(DB::table('couleur')->where('COU_Code', $request->id)->first());

    }


    public function getCouleur()
    {
        $couleur = DB::table('couleur')->get();
        //afficher tous les lignes
        foreach ($couleur as $c) {

            $data[] = ['COU_Code' => $c->COU_Code,
                'COU_Designation' => $c->COU_Designation,
                'COU_User' => $c->COU_User,
                'COU_Station' => $c->COU_Station
            ];

        }

        return $data;
    }


    public function getCouleurByX(Request $request)
    {
        return response()->json(DB::table('couleur')->where($request->field, $request->value)->get());

    }


    public function addCouleur(Request $request)
    {
        $data = $request->json()->all();

        if (!empty ($data)) {
            $c = DB::table('couleur')->insert($data);

            return response()->json($c);

        } else {
            return (false);
        }


    }


    public function updateCouleur(Request $request)
    {
        $data = $request->json()->all();


        DB::table('couleur')->where('COU_Code', $data["COU_Code"])->update($data);

        return ("Data modified");

    }


    public function deleteCouleur(Request $request)
    {

        DB::table('couleur')->where('COU_Code', $request->id)->delete();
        return ("Data Deleted");
    }


}





