<?php

namespace App\Http\Controllers\Http\DuxInventory;

use App\Http\Requests\BaseRequest;
use App\Models\DuxInventory\P_Famille;
use App\Models\DuxInventory\P_tier;

class P_tierController extends BaseController
{
    public function __construct()
    {
        $this->modelClass = P_tier::class;
    }


    public function findByTypeTier($idTypeTier)
    {
        try {
            $data = P_tier::from('P_tier')
                ->where('idTypeTier', '=', $idTypeTier)
                ->where('active', '=', '1')
                ->orderBy('dateCreation', 'desc')->get();
            return response()->success($data);
        } catch (\Exception $e) {
            return response()->error($e);
        }
    }


    public function getAllTierApporteur()
    {
        try {
            $res = P_tier::from('P_tier as t')
                ->leftJoin('E_TypeTier as e', 't.idTypeTier', '=', 'e.id')
                ->where('e.isapporteur', '=', true)
                ->orderBy('t.j_ddm', 'desc')
                ->select('t.nomPrenom', 't.id')
                ->get();

            return response()->success($res);
        } catch (\Exception $e) {
            return response()->error($e);
        }
    }

}