package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.LISTE_CONCURRENT_TABLE)
@Serializable
data class ConcurrentVC (
   /* @PrimaryKey(autoGenerate = true)
    @Transient
    val id: Long = 0,*/

    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = "Codeconcurrent")
    @SerialName("Codeconcurrent")
    var codeconcurrent: String = "",

    @ColumnInfo(name = "concurrent")
    @SerialName("concurrent")
    var concurrent: String = "",

    @ColumnInfo(name = "Note")
    @SerialName("Note")
    var note: String? =  "",

    @ColumnInfo(name = "Etatconcurrent")
    @SerialName("Etatconcurrent")
    var etatconcurrent : Int = 0,

    @ColumnInfo(name = "Info1")
    @SerialName("Info1")
    var info1: String? = "",

    @ColumnInfo(name = "Info2")
    @SerialName("Info2")
    var info2: String? = ""


)
