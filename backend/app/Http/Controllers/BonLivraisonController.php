<?php

namespace App\Http\Controllers;

use App\Helpers\AppHelper;
use App\Helpers\Enum;
use App\Helpers\DatabaseConnection;
use Carbon\Carbon;
use Illuminate\Http\Request;
use DateTime;
use DB;
use Exception;
use Illuminate\Support\Str;

class BonLivraisonController extends Controller
{
    public function getBonLivraison(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);

        $exercice = (new ExerciceWSMobile())->getExercice($request)->original;

        return response()->json($connection->table('bon_transfert')
            ->where('BON_Trans_Exerc','=',$exercice->Exercice_Code)->get());
    }

    public function getBonCommande(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Devis')->get());
    }

    public function getBonRetour(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Bon_Retour')->get());
    }

    public function addBatchBonLivraison(Request $request)
    {
        $items = $request->json()->all();

        if (!empty($items)) {
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {
                $c = $connection->table('bon_transfert')->insert($data);

                if ($c) {
                    $result[] = $data;
                } else {
                    $result[] = null;
                }
            }
        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }

    // Insertion ou modification des plusieurs dans les tables bon_transfert et ligne_bon_transfert
    public function addBatchBonLivraisonWithLines(Request $request)
    {
        $items = $request->json()->all();
        $result = array();
        $ligneBonLivraisonController = new LigneBonLivraisonController();
        // verifier si l'objet à parcourir n'est pas null
        if (!empty($items["object"])) {
            $connection = DatabaseConnection::setConnection($items);
            if (!empty($items)) {
                foreach ($items["object"] as $data) {
                    try {
                        $connection->beginTransaction();

                        if (array_key_exists("BON_ENT_SYNC", $data["parent"])) {
                            unset($data["parent"]["BON_ENT_SYNC"]);
                        }
                        $data["parent"]['BON_Trans_DDm'] = AppHelper::setDateFormat($data["parent"]['BON_Trans_DDm']);

                    //    $data["parent"]["BON_Trans_Etat"] = "Validée";

                        if (!empty($data["parent"]['BON_Trans_Date'])) {
                            $data["parent"]['BON_Trans_Date'] = AppHelper::setDateFormat($data["parent"]['BON_Trans_Date']);
                        }
                        $data["parent"]['DDm'] = Carbon::now();
                        $data["parent"]['BON_Trans_DDm'] = Carbon::now();

                        if ($data["parent"]["BON_Trans_StatSource"] == $data["parent"]["BON_Trans_StatDest"]) {

                            throw new Exception("Station Duplique");
                        }

                        $exists = $connection->table("bon_transfert")
                            ->where("BON_Trans_Exerc", $data["parent"]["BON_Trans_Exerc"])
                            // ->where("BON_Trans_Num",$data["parent"]["BON_Trans_Num"])
                            ->where("BON_Trans_Date", $data["parent"]["BON_Trans_Date"])
                            ->where("BON_Trans_User", $data["parent"]["BON_Trans_User"])
                            ->where("BON_Trans_StatDest", $data["parent"]["BON_Trans_StatDest"])
                            ->where("BON_Trans_StatSource", $data["parent"]["BON_Trans_StatSource"])
                            ->first();



                        if (!($exists)) {
                            $prefixController = new PrefixWSMobile();
                            $code = $prefixController->getBonTransferPrefix($connection,
                                $data["parent"]["BON_Trans_Exerc"], $data["parent"]["BON_Trans_StatSource"],
                                $data["parent"]["BON_Trans_User"]);
                            $data["parent"]["BON_Trans_Num"] = $code;

                            // Modifier le num bon transfert dans les ligne bon transfert
                            for ($i = 0; $i < count($data["children"]); $i++) {
                                $data["children"][$i]["LG_BonTrans_NumBon"] = $code;
                            }

                            $succesInsertBL = $connection->table("bon_transfert")
                                ->updateOrInsert(
                                    [
                                        "BON_Trans_Num" => $data["parent"]["BON_Trans_Num"],
                                        "BON_Trans_Exerc" => $data["parent"]["BON_Trans_Exerc"],
                                        "BON_Trans_DDm" => $data["parent"]['BON_Trans_DDm'],
                                        "BON_Trans_Date" => $data["parent"]['BON_Trans_Date'],
                                        "BON_Trans_Etat" => $data["parent"]['BON_Trans_Etat'],
                                        "BON_Trans_Etat_Saisie" => $data["parent"]['BON_Trans_Etat_Saisie'],
                                        "BON_Trans_Exerc" => $data["parent"]['BON_Trans_Exerc'],
                                        "BON_Trans_export" => $data["parent"]['BON_Trans_export'],
                                        "BON_Trans_Mnt_HT" => floatval($data["parent"]['BON_Trans_Mnt_HT']),
                                        "BON_Trans_Mnt_TTC" => floatval($data["parent"]['BON_Trans_Mnt_TTC']),
                                        "BON_Trans_obs" => $data["parent"]['BON_Trans_obs'],
                                        "BON_Trans_StatDest" => $data["parent"]["BON_Trans_StatDest"],
                                        "BON_Trans_StatSource" => $data["parent"]["BON_Trans_StatSource"],
                                        "BON_Trans_Stat" => $data["parent"]['BON_Trans_Stat'],
                                        "BON_Trans_Transporteur" => $data["parent"]['BON_Trans_Transporteur'],
                                        "BON_Trans_User" => $data["parent"]['BON_Trans_User'],
                                        "BON_Trans_Vehicule" => $data["parent"]['BON_Trans_Vehicule'],
                                        "DDm" => $data["parent"]['DDm'],
                                        "Declaree" => $data["parent"]['Declaree'],
                                        "Exportation" => $data["parent"]['Exportation'],
                                        "Observation" => $data["parent"]['Observation'],
                                    ]
                                );
                            //  }
                            /*else {
                                $succesInsertBL = $connection->table("bon_transfert")
                                    ->where("BON_Trans_Num", $data["parent"]["BON_Trans_Num"])
                                    ->where("BON_Trans_Exerc", $data["parent"]["BON_Trans_Exerc"])
                                    ->update(
                                        [
                                            "BON_Trans_DDm" => $data["parent"]['BON_Trans_DDm'],
                                            "BON_Trans_Date" => $data["parent"]['BON_Trans_Date'],
                                            "BON_Trans_Etat" => $data["parent"]['BON_Trans_Etat'],
                                            "BON_Trans_Etat_Saisie" => $data["parent"]['BON_Trans_Etat_Saisie'],
                                            "BON_Trans_Exerc" => $data["parent"]['BON_Trans_Exerc'],
                                            "BON_Trans_export" => $data["parent"]['BON_Trans_export'],
                                            "BON_Trans_Mnt_HT" => floatval($data["parent"]['BON_Trans_Mnt_HT']),
                                            "BON_Trans_Mnt_TTC" => floatval($data["parent"]['BON_Trans_Mnt_TTC']),
                                            "BON_Trans_Num" => $data["parent"]['BON_Trans_Num'],
                                            "BON_Trans_obs" => $data["parent"]['BON_Trans_obs'],
                                            "BON_Trans_StatDest" => $data["parent"]["BON_Trans_StatDest"],
                                            "BON_Trans_StatSource" => $data["parent"]["BON_Trans_StatSource"],
                                            "BON_Trans_Stat" => $data["parent"]['BON_Trans_Stat'],
                                            "BON_Trans_Transporteur" => $data["parent"]['BON_Trans_Transporteur'],
                                            "BON_Trans_User" => $data["parent"]['BON_Trans_User'],
                                            "BON_Trans_Vehicule" => $data["parent"]['BON_Trans_Vehicule'],
                                            "DDm" => $data["parent"]['DDm'],
                                            "Declaree" => $data["parent"]['Declaree'],
                                            "Exportation" => $data["parent"]['Exportation'],
                                            "Observation" => $data["parent"]['Observation'],
                                        ]
                                    );
                            }*/
                            if (!$succesInsertBL) {

                                $result[] = $this->setResultBonTransfert($data["parent"]["BON_Trans_Num"],
                                    $data["parent"]["BON_Trans_Exerc"],
                                    "Erreur dans l'insertion du Bon Transfert N° " . $data["parent"]["BON_Trans_Num"],
                                    Enum::Error_Insert_BL);

                                throw new Exception("", Enum::Error_Insert_BL);

                            } else {
                                $valeur = $ligneBonLivraisonController->addBatchLigneBonLivraisonData($connection, $data["children"], $data["parent"]);

                                if (!$valeur['testLigneBL']) {
                                    $result[] = $this->setResultBonTransfert($data["parent"]["BON_Trans_Num"],
                                        $data["parent"]["BON_Trans_Exerc"],
                                        $valeur['message'] . " " . $data["parent"]["BON_Trans_Num"],
                                        $valeur['codeEnum']);

                                    throw new Exception("", $valeur['codeEnum']);

                                } else {
                                    $data["parent"]["BON_Trans_Mnt_HT"] = $valeur["Mnt_HT"];
                                    $data["parent"]["BON_Trans_Mnt_TTC"] = $valeur["Mnt_TTC"];
                                    $connection->table("bon_transfert")
                                        ->where("BON_Trans_Num", $data["parent"]["BON_Trans_Num"])
                                        ->where("BON_Trans_Exerc", $data["parent"]["BON_Trans_Exerc"])
                                        ->update(
                                            [
                                                "BON_Trans_Mnt_HT" => floatval($data["parent"]['BON_Trans_Mnt_HT']),
                                                "BON_Trans_Mnt_TTC" => floatval($data["parent"]['BON_Trans_Mnt_TTC']),
                                            ]
                                        );

                                    $result[] = $this->setResultBonTransfert($data["parent"]["BON_Trans_Num"],
                                        $data["parent"]["BON_Trans_Exerc"],
                                        "INSERTED Bon Transfert N° " . $data["parent"]["BON_Trans_Num"],
                                        Enum::Success_Code,$data["parent"]["BON_Trans_Num_M"]
                                    );

                                }
                            }
                        } else {

                            $result[] = $this->setResultBonTransfert($exists->BON_Trans_Num,
                                $data["parent"]["BON_Trans_Exerc"],
                                "Bon Transfert N° " . $data["parent"]["BON_Trans_Num"] . " EXISTE",
                                Enum::Success_Code);

                        }

                        $connection->commit();
                    } catch (\Exception $e) {
                        $connection->rollBack();
                        $message = $e->getMessage();
                        // return $message;
                    }
                }
            }
            return response()->json($result);
        } else {
            return response()->json("Empty Object");
        }

    }


}
