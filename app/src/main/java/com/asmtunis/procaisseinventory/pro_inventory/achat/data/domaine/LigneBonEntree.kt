package com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProInventoryConstants.LIGNE_BON_ENTREE_TABLE, primaryKeys = ["LIG_BonEntree_NumBon",
    "LIG_BonEntree_CodeArt", "LIG_BonEntree_Exerc"])
@Serializable
data class LigneBonEntree(
   //  @PrimaryKey(autoGenerate = true)
   //  @Transient
 //   val id: Long = 0,
    @ColumnInfo(name = "LIG_BonEntree_NumBon_M")
    @SerialName("LIG_BonEntree_NumBon_M")
    val lIGBonEntreeNumBonM: String = "",

    @ColumnInfo(name = "LIG_BonEntree_NumBon")
    @SerialName("LIG_BonEntree_NumBon")
    var lIGBonEntreeNumBon: String = "",

    @ColumnInfo(name = "LIG_BonEntree_CodeArt")
    @SerialName("LIG_BonEntree_CodeArt")
    var lIGBonEntreeCodeArt: String = "",

    @ColumnInfo(name = "LIG_BonEntree_Exerc")
    @SerialName("LIG_BonEntree_Exerc")
    var lIGBonEntreeExerc: String = "",

    @ColumnInfo(name = "LIG_BonEntree_Qte")
    @SerialName("LIG_BonEntree_Qte")
    var lIGBonEntreeQte: String? = null,

    @ColumnInfo(name = "LIG_BonEntree_Unite")
    @SerialName("LIG_BonEntree_Unite")
    var lIGBonEntreeUnite: String? = null,

    @ColumnInfo(name = "LIG_BonEntree_PUHT")
    @SerialName("LIG_BonEntree_PUHT")
    var lIGBonEntreePUHT: String? = null,

   /* @Transient
    @SerialName("LIG_BonEntree_SYNC")
    var lIGBonEntreeSYNC: String? = null,*/

    @ColumnInfo(name = "LIG_BonEntree_Tva")
    @SerialName("LIG_BonEntree_Tva")
    var lIGBonEntreeTVA: String? = null,

    @ColumnInfo(name = "ordre")
    @SerialName("ordre")
    var ordre: Int = -1,



















    @SerialName("BON_ENT_Mont")
    val bONENTMont: String? = null,

    @SerialName("CMP")
    val cMP: String = "",

    @SerialName("CMP_G")
    val cMPG: String = "",

    @SerialName("Cout_charge")
    val coutCharge: String = "",

    @SerialName("DDmM")
    val dDmM: String? = null,

    @SerialName("Date_Peremption")
    val datePeremption: String? = null,

    @SerialName("exportM")
    val exportM: String? = null,

    @SerialName("LG_BonEntree_BE")
    val lGBonEntreeBE: String? = null,

    @SerialName("LIG_AncPrixVente")
    val lIGAncPrixVente: String = "",

    @SerialName("LIG_BonEntree_CodeGroupe")
    val lIGBonEntreeCodeGroupe: String? = null,

    @SerialName("LIG_BonEntree_DDm")
    val lIGBonEntreeDDm: String = "",

    @SerialName("LIG_BonEntree_DatePerisage")
    val lIGBonEntreeDatePerisage: String? = null,

    @SerialName("LIG_BonEntree_export")
    val lIGBonEntreeExport: String = "",

    @SerialName("LIG_BonEntree_FraisTansport")
    val lIGBonEntreeFraisTansport: String = "",

    @SerialName("LIG_BonEntree_MntBrutHT")
    val lIGBonEntreeMntBrutHT: String = "",

    @SerialName("LIG_BonEntree_MntDc")
    val lIGBonEntreeMntDc: String = "",

    @SerialName("LIG_BonEntree_MntFodec")
    val lIGBonEntreeMntFodec: String = "",

    @SerialName("LIG_BonEntree_MntNetHt")
    val lIGBonEntreeMntNetHt: String = "",

    @SerialName("LIG_BonEntree_MntTTC")
    val lIGBonEntreeMntTTC: String = "",

    @SerialName("LIG_BonEntree_MntTva")
    val lIGBonEntreeMntTva: String = "",

    @SerialName("LIG_BonEntree_NumOrdre")
    val lIGBonEntreeNumOrdre: String = "",

    @SerialName("LIG_BonEntree_PUTTC")
    val lIGBonEntreePUTTC: String = "",

    @SerialName("LIG_BonEntree_QteGratuite")
    val lIGBonEntreeQteGratuite: String = "",

    @SerialName("LIG_BonEntree_QtePiece")
    val lIGBonEntreeQtePiece: String? = "",

    @SerialName("LIG_BonEntree_QteRejete")
    val lIGBonEntreeQteRejete: String = "",

    @SerialName("LIG_BonEntree_QteVendu")
    val lIGBonEntreeQteVendu: String = "",

    @SerialName("LIG_BonEntree_Remise")
    val lIGBonEntreeRemise: String = "",

    @SerialName("LIG_BonEntree_Remise2")
    val lIGBonEntreeRemise2: String? = null,

    @SerialName("LIG_BonEntree_Station")
    val lIGBonEntreeStation: String? = null,

    @SerialName("LIG_BonEntree_Taux")
    val lIGBonEntreeTaux: String = "",

    @SerialName("LIG_BonEntree_TauxComp")
    val lIGBonEntreeTauxComp: String = "",

    @SerialName("LIG_BonEntree_TauxDc")
    val lIGBonEntreeTauxDc: String = "",

    @SerialName("LIG_BonEntree_TauxFodec")
    val lIGBonEntreeTauxFodec: String = "",



    @SerialName("LIG_BonEntree_Type")
    val lIGBonEntreeType: String? = null,

    @SerialName("LIG_BonEntree_User")
    val lIGBonEntreeUser: String? = null,

    @SerialName("LIG_MargeVente")
    val lIGMargeVente: String = "",

    @SerialName("LIG_PVENTEHT")
    val lIGPVENTEHT: String = "",

    @SerialName("LIG_PrixSite")
    val lIGPrixSite: String = "",

    @SerialName("LIG_PrixVentePub")
    val lIGPrixVentePub: String? = null,

    @SerialName("LIG_QteRetour")
    val lIGQteRetour: String = "",

    @SerialName("LIG_QteV")
    val lIGQteV: String? = null,

    @SerialName("LIG_QteVendue")
    val lIGQteVendue: String = "",

    @SerialName("LIG_Retour")
    val lIGRetour: String = "",

    @SerialName("LIG_TauxEchange")
    val lIGTauxEchange: String = "",

    @SerialName("LiG_QteRestante")
    val liGQteRestante: String? = null,

    @SerialName("QteAStock")
    val qteAStock: String = "",

    @SerialName("QteDec")
    val qteDec: String = ""






): BaseModel()