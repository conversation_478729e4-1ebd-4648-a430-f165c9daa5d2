package com.asmtunis.procaisseinventory.pro_inventory.achat.data.remote.di

import com.asmtunis.procaisseinventory.pro_inventory.achat.data.remote.api.bn_entree.BonEntreeApi
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.remote.api.bn_entree.BonEntreeApiImpl
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.remote.api.ligne_bn_entree.LigneBonEntreeApi
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.remote.api.ligne_bn_entree.LigneBonEntreeApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object BonEntreeRemoteModule {

    @Provides
    @Singleton
    fun provideBonEntreeApi(client: HttpClient): BonEntreeApi = BonEntreeApiImpl(client)


    @Provides
    @Singleton
    fun provideLigneBonEntreeApi(client: HttpClient): LigneBonEntreeApi = LigneBonEntreeApiImpl(client)

}
