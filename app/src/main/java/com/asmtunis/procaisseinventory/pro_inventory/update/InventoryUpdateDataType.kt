package com.asmtunis.procaisseinventory.pro_inventory.update

enum class InventoryUpdateDataType(val displayName: String) {
    STATION("Station"),
    STATION_STOCK_ARTICLE("Station de stock d'articles"),
    TYPE_PRIX_UNITAIRE_HT("Type de prix unitaire HT"),
    TVA("TVA"),
    UNITE("Unité"),
    FAMILLE("Famille"),
    FOURNISSEUR("Fournisseur"),
    MARQUE("Marque"),
    BON_ENTREE("Bon d'entrée"),
    LIGNE_BON_ENTREE("Ligne de bon d'entrée"),
    BON_LIVRAISON("Bon de livraison"),
    LIGNE_BON_LIVRAISON("Ligne de bon de livraison"),
    INVENTAIRE("Inventaire"),
    PREFIXE("Préfixe"),
    ARTICLES("Articles"),
    EXERCICE("Exercice")
}