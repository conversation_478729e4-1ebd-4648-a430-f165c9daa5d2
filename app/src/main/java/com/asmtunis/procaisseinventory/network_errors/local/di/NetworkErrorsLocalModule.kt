package com.asmtunis.procaisseinventory.network_errors.local.di

import com.asmtunis.procaisseinventory.network_errors.local.dao.NetworkErrorsDAO
import com.asmtunis.procaisseinventory.network_errors.local.repository.NetworkErrorsLocalRepository
import com.asmtunis.procaisseinventory.network_errors.local.repository.NetworkErrorsLocalRepositoryImpl
import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class NetworkErrorsLocalModule {

    @Provides
    @Singleton
    fun provideNetworkErrorsDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.networkErrorDAO()

    @Provides
    @Singleton
    @Named("NetworkErrors")
    fun provideInventairePieceJointRepository(
        networkErrorDAO: NetworkErrorsDAO
    ): NetworkErrorsLocalRepository = NetworkErrorsLocalRepositoryImpl(
        networkErrorsDAO = networkErrorDAO
    )


}