<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ClasseRemiseController extends Controller
{

    public function getClasseRemises(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('classe_remise')->get());

    }

    public function addclasseRemiseMobile(Request $request)
    {
        $items = $request->json()->all();

        $connection = DatabaseConnection::setConnection($items);

        if (!empty($items)) {
           // $f = $connection->table('classe_remise_Mobile')->insert($data);
           foreach ($items["object"]["classeRemises"] as $data) {
            if($data["CLASS_REM_DDm"]=="NULL")
            $data["CLASS_REM_DDm"]=NULL;
            if($data["CLASS_REM_DDm"]=="")
            $data["CLASS_REM_DDm"]=NULL;

            $exist=$connection->table('classe_remise')
				->where('CLASS_REM_code', '=', $data['CLASS_REM_code'])
                ->get();
				if($exist==null || empty($exist)){
					$f=$connection->table('classe_remise')->insert($data);
				}else{
				$f=	$connection->table('classe_remise')
				->where('CLASS_REM_code', $data['CLASS_REM_code'])
					->update([
						'CLASS_REM_DDm' => $data['CLASS_REM_DDm'],
						'CLASS_REM_Desg' => $data['CLASS_REM_Desg'],
						'CLASS_REM_export' => $data['CLASS_REM_export'],
						'CLASS_REM_Montant' => $data['CLASS_REM_Montant'],
						'CLASS_REM_station' => $data['CLASS_REM_station'],
						'CLASS_REM_user' => $data['CLASS_REM_user'],
						'CLASS_REM_Taux' => $data['CLASS_REM_Taux']
                	 ]);
                //$f=$connection->table('classe_remise_Mobile')->insert($data);
				}
           
           }
            return response()->json($f);

        } else {
            return (false);
        }

    }

    public function updateFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        $connection->table('famille')->where('FAM_Code', $data["FAM_Code"])->update($data);

        return ("Data modified");

    }

    public function deleteFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $connection->table('famille')->where('FAM_Code', $data["object"])->delete();
        return ("Data Deleted");
    }




    public function updateOrCreate($connection, $data,$table, $whereClauses )
    {
       	$exists = $connection->table($table)->where($whereClauses)->first();

		if ($exists) {
			$connection->table($table)->where($whereClauses)->delete();
		}


	 	
	 	return $connection->table($table)->insert($data);

       
    }
}
