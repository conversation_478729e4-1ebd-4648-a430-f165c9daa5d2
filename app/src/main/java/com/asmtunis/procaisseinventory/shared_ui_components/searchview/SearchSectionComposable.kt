package com.asmtunis.procaisseinventory.shared_ui_components.searchview

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.FilterList
import androidx.compose.material.icons.twotone.Search
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.simapps.ui_kit.SearchView

@Composable
fun SearchSectionComposable(
    label: String,
    searchVisibility: Boolean,
    searchTextState: TextFieldValue,
    onSearchValueChange: (String) -> Unit,
    onShowSearchViewChange: (Boolean) -> Unit,
    onShowCustomFilterChange: (Boolean) -> Unit

) {
        AnimatedVisibility(
            visible = searchVisibility,
            enter = fadeIn() + slideInVertically(),
            exit = fadeOut() + slideOutVertically()
        ) {
            SearchView(
                label = label,
                onClearClick = {
                    onSearchValueChange("")
                },
                onShowSearchViewChange = {
                    onShowSearchViewChange(!searchVisibility)
                },
                onSearchValueChange = { query ->
                    onSearchValueChange(query)

                },

                state = searchTextState.text,
                onShowCustomFilterChange = {
                    onShowCustomFilterChange(it)
                }
            )


        }
        AnimatedVisibility(
            visible = !searchVisibility,
            enter = fadeIn() + slideInVertically(),
            exit = fadeOut() + slideOutVertically()
        ) {
           IconButton(onClick = {
                onShowSearchViewChange(!searchVisibility)
            }) {
                Icon(
                  /*  modifier = Modifier
                        .size(28.dp)
                        .clickable {
                            onShowSearchViewChange(!searchVisibility)

                        },*/
                    imageVector = Icons.TwoTone.Search,
                    contentDescription = stringResource(id = R.string.search_by)
                )
            }
        }
    


   /* AnimatedVisibility(
        visible = searchVisibility,
        enter = fadeIn() + slideInVertically(),
        exit = fadeOut() + slideOutVertically()
    ) {
        Spacer(modifier = Modifier.width(12.dp))
    }*/
    AnimatedVisibility(
        visible = !searchVisibility,
        enter = fadeIn() + slideInVertically(),
        exit = fadeOut() + slideOutVertically()
    ) {
        IconButton(onClick = {
            onShowCustomFilterChange(true)
        }) {
            Icon(
                imageVector = Icons.TwoTone.FilterList,
                contentDescription = "Localized description",
                /*  modifier = Modifier
                      .size(AssistChipDefaults.IconSize)
                      .clickable {
                          onShowCustomFilterChange(true)

                      }*/
            )
        }
    }
    Spacer(modifier = Modifier.width(9.dp))

}