package com.asmtunis.procaisseinventory.shared_ui_components

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.sp
import com.asmtunis.countrycodepicker.data.CountryData
import com.asmtunis.countrycodepicker.data.utils.getDefaultLangCode
import com.asmtunis.countrycodepicker.data.utils.transformation.PhoneNumberTransformation
import com.asmtunis.countrycodepicker.presentation.CodePicker
import com.asmtunis.procaisseinventory.core.UiText
import java.util.Locale

@Composable
fun PhoneCountryPicker(
    countryCode : String = Locale.getDefault().country,
    onCountryChange: (CountryData) -> Unit,
    stringId: Int,
    errorValue: UiText?,
    enabled : Boolean = true,
    value :String= "",
    hint : Int,
    onValueChange: (String) -> Unit,
    shape: Shape = MaterialTheme.shapes.medium,
    showCountryCode: Boolean = true,
    imeAction: ImeAction = ImeAction.Next


) {
    val context = LocalContext.current

    val keyboardController = LocalSoftwareKeyboardController.current


    OutlinedTextField(
                    label = { Text(context.getString(stringId), fontSize = 12.sp, maxLines = 1) },

                    value = value,//viewModel.stateSbscribtion.phone1,
                    onValueChange = {
                        onValueChange(it)
                    }, //,
                    modifier = Modifier.fillMaxWidth(0.95f),
                    shape = shape,
                    enabled = true,
                    readOnly = !enabled,
                    singleLine = true,
                    placeholder = { Text(text = stringResource(id = hint)) },
                   visualTransformation = PhoneNumberTransformation(countryCode),
                    isError = errorValue != null,
                    supportingText = {
                        if(errorValue?.asString(context) != null)
                            Text(
                                text = errorValue.asString(context)!!,
                                color = MaterialTheme.colorScheme.error,
                                fontSize = MaterialTheme.typography.labelSmall.fontSize,

                                )
                    },
                    keyboardOptions = KeyboardOptions.Default.copy(
                        imeAction = imeAction,
                        keyboardType = KeyboardType.Number,
                        autoCorrectEnabled = true,
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = {
                        keyboardController?.hide()
                    }
                    ),
                    leadingIcon = {
                                val dialog = CodePicker()
                                dialog.CodePickerDialog(
                                    pickedCountry = onCountryChange,
                                    defaultSelectedCountry = CountryData(getDefaultLangCode().lowercase(Locale.getDefault())),
                                    showCountryCode = showCountryCode,
                                    enabled = enabled
                                )


                    }
                )



}
