<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Symfony\Component\HttpFoundation\Response;

class P_ArticleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */

    public function rules()
    {
        return [
            'object' => 'required|array',
            'object.*.id' => 'nullable|string|max:50',
            'object.*.code' => 'nullable|string|max:50',
            'object.*.libelle' => 'nullable|string',
            'object.*.libelleCourte' => 'nullable|string',
            'object.*.margeB' => 'nullable|numeric',
            'object.*.soumisTva' => 'nullable|boolean',
            'object.*.idTva' => 'nullable|string|max:50',
            'object.*.tauxTva' => 'nullable|numeric',
            'object.*.prixuht' => 'nullable|numeric',
            'object.*.poids' => 'nullable|numeric',
            'object.*.volume' => 'nullable|numeric',
            'object.*.largeur' => 'nullable|numeric',
            'object.*.hauteur' => 'nullable|numeric',
            'object.*.profondeur' => 'nullable|numeric',
            'object.*.idModele' => 'nullable|string|max:50',
            'object.*.codeModele' => 'nullable|string|max:50',
            'object.*.libelleModele' => 'nullable|string',
            'object.*.idFamille' => 'nullable|string|max:50',
            'object.*.codeFamille' => 'nullable|string|max:50',
            'object.*.libelleFamille' => 'nullable|string',
            'object.*.idCouleur' => 'nullable|string|max:50',
            'object.*.codeCouleur' => 'nullable|string|max:50',
            'object.*.libelleCouleur' => 'nullable|string',
            'object.*.idMarque' => 'nullable|string|max:50',
            'object.*.codeMarque' => 'nullable|string|max:50',
            'object.*.libelleMarque' => 'nullable|string',
            'object.*.idUniteStockage' => 'nullable|string|max:50',
            'object.*.codeUnite' => 'nullable|string|max:50',
            'object.*.libelleUnite' => 'nullable|string',
            'object.*.idTypeArticle' => 'nullable|string|max:50',
            'object.*.codeTypeArticle' => 'nullable|string|max:50',
            'object.*.libelleTypeArticle' => 'nullable|string|max:50',
            'object.*.cmp' => 'nullable|numeric',
            'object.*.margaBenifReel' => 'nullable|numeric',
            'object.*.prixAchatTTC' => 'nullable|numeric',
            'object.*.prixVentePubHt' => 'nullable|numeric',
            'object.*.prixVentePublicTTC' => 'nullable|numeric',
            'object.*.dernierPrixAchat' => 'nullable|numeric',
            'object.*.dernierDateAchat' => 'nullable|date',
            'object.*.dernierPrixVente' => 'nullable|numeric',
            'object.*.dernierDateVente' => 'nullable|date',
            'object.*.qteStock' => 'nullable|numeric',
            'object.*.coefCharge' => 'nullable|numeric',
            'object.*.coutRevientPonderer' => 'nullable|numeric',
            'object.*.P_codeStation' => 'required|string|max:50',
            'object.*.j_codeStation' => 'required|string|max:50',
            'object.*.j_codeUser' => 'required|string|max:50',
            'object.*.active' => 'nullable|boolean',
            'object.*.isStockable' => 'nullable|boolean',
            'object.*.compteurCode' => 'nullable|integer',
            'object.*.idSectionAchat' => 'nullable|string|max:50',
            'object.*.idFrs' => 'nullable|string|max:50',
            'object.*.RefFrs' => 'nullable|string|max:50',
            'object.*.NGP' => 'nullable|numeric',
            'object.*.EcoFilter' => 'nullable|string|max:50',
            'object.*.QteMin' => 'nullable|numeric',
            'object.*.QteMax' => 'nullable|numeric',
            'object.*.prixuhtDevise' => 'nullable|numeric',
            'object.*.NumSerie' => 'nullable|boolean',
            'object.*.RemiseMax' => 'nullable|numeric',
            'object.*.TypeCalculeMarge' => 'nullable|string|max:50',
            'object.*.P_NetHt' => 'nullable|numeric',
            'object.*.margeTheorique' => 'nullable|numeric',
            'object.*.MajAutCMP' => 'nullable|boolean',
            'object.*.MajAutCr' => 'nullable|boolean',
            'object.*.MajAutDPA' => 'nullable|boolean',
            'object.*.Photo' => 'nullable|string',
            'object.*.nbmGarantie' => 'nullable|string|max:50',
            'object.*.isGarantieCheck' => 'nullable|boolean',
            'object.*.isEcommenrce' => 'nullable|boolean',
            'object.*.E_EtatProduit' => 'nullable|boolean',
            'object.*.E_stock' => 'nullable|string|max:50',
            'object.*.E_Prix_Prom' => 'nullable|string|max:50',
            'object.*.use_variant_couleur' => 'nullable|boolean',
            'object.*.use_qrcode_qte' => 'nullable|boolean',
            'object.*.idFormule' => 'nullable|string|max:255',
            'object.*.use_qte_souhaite' => 'nullable|boolean',
            'object.*.maxQteSouhaite' => 'nullable|string|max:50',
            'object.*.maxDepassement' => 'nullable|string|max:50',
            'object.*.ModeSuiviStk' => 'nullable|string|max:50',
            'object.*.idCellule' => 'nullable|string|max:50',
            'object.*.libelleCellule' => 'nullable|string',

        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY)
        );
    }
}
