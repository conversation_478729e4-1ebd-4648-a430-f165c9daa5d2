<?php

namespace App\Models\DuxInventory;



class P_Couleur extends BaseModel
{
    protected $connection = 'onthefly';
    protected $table = 'P_Couleur';
    public $incrementing = false;
    public $timestamps = false;
    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->idClasse = 'couleur';
        $this->useCode = true;
    }
}
