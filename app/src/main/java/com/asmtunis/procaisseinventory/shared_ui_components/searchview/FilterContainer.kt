@file:OptIn(ExperimentalMaterial3Api::class)

package com.asmtunis.procaisseinventory.shared_ui_components.searchview

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.order.OrderSection
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.SearchSection
import kotlinx.coroutines.launch

@Composable
fun FilterContainer(filterList: Array<String>,
                    listFilter : ListSearch,
                    listOrder: ListOrder,
                    orderList: Array<String>,
                    onShowCustomFilterChange :(Boolean) -> Unit,
                    onEvent : (ListEvent)->Unit,
                    customFilterContent: @Composable () -> Unit = { }
) {

    val scope = rememberCoroutineScope()
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    ModalBottomSheet(
        sheetState = sheetState,
        onDismissRequest = {
            scope.launch {
                sheetState.hide()
            }
            onShowCustomFilterChange(false)
        },
    ) {
        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .fillMaxWidth()
                .padding(top =12.dp, start = 12.dp, end= 12.dp, bottom = 40.dp)
        ) {

          //  Spacer(modifier = Modifier.height(12.dp))

            SearchSection(
                filerList = filterList,
                listSearch = listFilter,
                onFilterChange = {

                    onEvent(ListEvent.ListSearch(it))
                }
            )



            Spacer(modifier = Modifier.height(16.dp))
            OrderSection(
                orderList = orderList,
                listOrder = listOrder,
                onOrderChange = {
                    onEvent(ListEvent.Order(it))
                }
            )

         //   Spacer(modifier = Modifier.height(16.dp))
            customFilterContent()

            Spacer(modifier = Modifier.height(24.dp))
        }
    }
}