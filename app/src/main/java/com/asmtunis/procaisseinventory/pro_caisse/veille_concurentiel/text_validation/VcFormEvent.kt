package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.text_validation

import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ConcurrentVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.TypeCommunicationVC

sealed class VcFormEvent {

    data class produitChanged(val produit: String) : VcFormEvent()
    data class autreNoteChanged(val autreNote: String) : VcFormEvent()
    data class prixChanged(val prix: String) : VcFormEvent()
    data class tauxChanged(val taux: String) : VcFormEvent()
    data class noteChanged(val note: String) : VcFormEvent()
    data class fournisseurChanged(val fournisseur: ConcurrentVC) : VcFormEvent()
    data class typeCommunicationChanged(val typeCommunication: TypeCommunicationVC) : VcFormEvent()
    data class LocalProductChanged(val localProduct: Article) : VcFormEvent()

    object SubmitAddNewProductVc : VcFormEvent()
    object SubmitAddPromotionVc : VcFormEvent()
    object SubmitAddPrixVc : VcFormEvent()
    object SubmitAddAutreVc : VcFormEvent()

}