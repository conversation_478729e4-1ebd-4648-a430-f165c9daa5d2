<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;
use App\Http\Controllers\PrefixWSMobile;
use Illuminate\Support\Facades\Input;
use File;

class ReclamationWSMobile extends Controller
{


    public function getReclamationByCode(Request $request)
    {

        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Reclamation')->where('CLI_Code', $data["object"])->first());

    }


    /*fonction get data from database*/

    public function getReclamation(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);


        return response()->json($connection->table('Reclamation')->get());


    }


    public function getReclamationByX(Request $request)
    {
        return response()->json(DB::table('Reclamation')->where($request->field, $request->value)->get());

    }


    public function uploadReclamationFile(Request $request)
    {


        if (Input::hasFile('files')) {
            $file = Input::file('files');
            $file->move('reclamations', $file->getClientOriginalName());
            $image = $file->getClientOriginalName();

//unlink($zipPath);


        } else {
            return response()->json(null);

        }


    }


    public function addBatchReclamation(Request $request)
    {


        $items = $request->json()->all();

        if (!empty ($items)) {
            //	$data['object']['CLI_Code'] =(new PrefixWSMobile)->getPrefix($data,"Client");
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {

                $c = $connection->table('Reclamation')->insert($data);


                if ($c) {
                    $result[] = $data;
                } else {
                    $result[] = null;
                }


            }


        } else {
            return response()->json(null);
        }

        return response()->json($result);


    }


    public function updateReclamation(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);


        $connection->table('Reclamation')->where('CLI_Code', $data["CLI_Code"])->update($data);

        return ("Data modified");

    }


    public function deleteReclamation(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $connection->table('Reclamation')->where('CLI_Code', $data["object"])->delete();
        return ("Data Deleted");
    }


}





