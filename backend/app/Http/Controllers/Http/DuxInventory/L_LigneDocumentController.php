<?php

namespace App\Http\Controllers\Http\DuxInventory;



use App\Http\Requests\L_LigneDocumentRequest;
use App\Models\DuxInventory\L_LigneDocument;
use App\Services\M_DocumentService;



class L_LigneDocumentController extends M_DocumentController
{
    protected $documentService;
    protected $modelClass;
    protected $requestClass;


    public function __construct(M_DocumentService $documentService)
    {
        parent::__construct($documentService);
        $this->requestClass = L_LigneDocumentRequest::class;
        $this->modelClass = L_LigneDocument::class;

    }




}
