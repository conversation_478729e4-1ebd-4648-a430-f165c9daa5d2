package com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.cheque.text_validation

import com.asmtunis.procaisseinventory.data.banques.domaine.Banque


sealed class AddChequeFormEvent {

    data class NumeroChequeChanged(val numeroCheque: String) : AddChequeFormEvent()
    data class EcheanceChanged(val echeance: String) : AddChequeFormEvent()
    data class Bank(val bank: Banque) : AddChequeFormEvent()

    data class Montant(val montant: String) : AddChequeFormEvent()


    object SubmitAddCheque : AddChequeFormEvent()
}
