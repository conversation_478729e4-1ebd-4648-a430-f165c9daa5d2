package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.bn_livraison.dao

import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.BON_LIVRAISON_TABLE
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.BonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraisonWithArticle
import kotlinx.coroutines.flow.Flow

@Dao
interface BonLivraisonDAO {
    @get:Query("SELECT * FROM $BON_LIVRAISON_TABLE ORDER BY timestamp DESC")
    val all: Flow<List<BonLivraison>>

    @get:Query("SELECT * FROM $BON_LIVRAISON_TABLE ORDER BY timestamp DESC")
    val everything: Flow<List<BonLivraison>>

    @get:Query("SELECT * FROM $BON_LIVRAISON_TABLE ORDER BY timestamp DESC")
    val everythingLive: Flow<List<BonLivraison>>

    @get:Query("SELECT * FROM $BON_LIVRAISON_TABLE WHERE isSync=0  and  (Status='INSERTED'  or Status='UPDATED') ORDER BY timestamp asc")
    val nonSync: Flow<List<BonLivraison>>

    @Query(
        "SELECT * FROM $BON_LIVRAISON_TABLE " +
            " JOIN ${ProInventoryConstants.LIGNE_BON_LIVRAISON_TABLE} ON $BON_LIVRAISON_TABLE.BON_Trans_Num = ${ProInventoryConstants.LIGNE_BON_LIVRAISON_TABLE}.LG_BonTrans_NumBon" +

            " WHERE $BON_LIVRAISON_TABLE.IsSync=0 and  ($BON_LIVRAISON_TABLE.Status='INSERTED'  or $BON_LIVRAISON_TABLE.Status='UPDATED') ",
    )
    fun noSynced(): Flow<Map<BonLivraison, List<LigneBonLivraison>>?>

    @Query("SELECT * FROM $BON_LIVRAISON_TABLE WHERE (Status=:status) ORDER BY timestamp DESC")
    fun getByStatusForced(status: String): Flow<List<BonLivraison>>

    @Query(
        "SELECT ifnull(MAX(cast(substr(BON_Trans_Num,length(:prefix) + 1 ,length('BON_Trans_Num'))as integer)),0)+1 FROM " +
            "  $BON_LIVRAISON_TABLE WHERE substr(BON_Trans_Num, 0 ,length(:prefix)+1) = :prefix",
    )
    fun getNewCode(prefix: String): Flow<String>

    @Query(
        "SELECT ifnull(MAX(cast(substr(BON_Trans_Num,length(:prefix) + 1 ,length('BON_Trans_Num'))as integer)),0)+1 FROM   " +
            "$BON_LIVRAISON_TABLE WHERE BON_Trans_Vehicule = 'BT'",
    )
    fun getNewCodeBT(prefix: String): Flow<String>

    @Query("SELECT COUNT(*) FROM $BON_LIVRAISON_TABLE")
    fun count(): Flow<Int>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: BonLivraison)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<BonLivraison>)

    @Query("DELETE FROM $BON_LIVRAISON_TABLE")
    fun deleteAll()

    @Delete
    fun delete(bonLivraison: BonLivraison)

    @Query("UPDATE $BON_LIVRAISON_TABLE SET BON_Trans_Num =:code, Status = 'SELECTED' , IsSync = 1 where BON_Trans_Num_M = :codeM and BON_Trans_Exerc= :exercice")
    fun updateState(
        code: String,
        codeM: String,
        exercice: String,
    )

    @Transaction
    @Query(
        "SELECT * FROM $BON_LIVRAISON_TABLE " +
            "LEFT JOIN ${ProInventoryConstants.LIGNE_BON_LIVRAISON_TABLE} ON $BON_LIVRAISON_TABLE.BON_Trans_Num = ${ProInventoryConstants.LIGNE_BON_LIVRAISON_TABLE}.LG_BonTrans_NumBon" +
            " WHERE $BON_LIVRAISON_TABLE.BON_Trans_StatDest LIKE '%' || :searchString || '%' " +
            " and ( CASE WHEN :filterByStationSource !=  '' THEN BON_Trans_StatSource =:filterByStationSource ELSE BON_Trans_StatSource !=:filterByStationSource END " +
            "and  CASE WHEN :filterByStationDestination !=  ''THEN BON_Trans_StatDest=:filterByStationDestination ELSE BON_Trans_StatDest !=:filterByStationDestination  END " +
            "and  CASE WHEN :filterByEtatBnTransfert !=  ''THEN BON_Trans_Etat=:filterByEtatBnTransfert ELSE BON_Trans_Etat !=:filterByEtatBnTransfert  END) " +

            " ORDER BY " +
            "CASE WHEN :sortBy = 'BON_Trans_Num'  AND :isAsc = 1 THEN BON_Trans_Num END ASC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Num'  AND :isAsc = 2 THEN BON_Trans_Num END DESC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Mnt_TTC'  AND :isAsc = 1 THEN (CAST (BON_Trans_Mnt_TTC AS REAL)) END ASC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Mnt_TTC'  AND :isAsc = 2 THEN (CAST (BON_Trans_Mnt_TTC AS REAL)) END DESC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Date'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',BON_Trans_Date) END ASC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Date'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',BON_Trans_Date) END DESC ",
    )
    fun filterByCodeStationDestination(
        searchString: String,
        filterByStationSource: String,
        filterByStationDestination: String,
        filterByEtatBnTransfert: String,
        sortBy: String,
        isAsc: Int,
    ): Flow<Map<BonLivraison, List<LigneBonLivraisonWithArticle>>>

    @Transaction
    @Query(
        "SELECT * FROM $BON_LIVRAISON_TABLE " +
            "LEFT JOIN ${ProInventoryConstants.LIGNE_BON_LIVRAISON_TABLE} ON $BON_LIVRAISON_TABLE.BON_Trans_Num = ${ProInventoryConstants.LIGNE_BON_LIVRAISON_TABLE}.LG_BonTrans_NumBon" +
            " WHERE $BON_LIVRAISON_TABLE.BON_Trans_StatSource LIKE '%' || :searchString || '%' " +
                " and ( CASE WHEN :filterByStationSource !=  '' THEN BON_Trans_StatSource =:filterByStationSource ELSE BON_Trans_StatSource !=:filterByStationSource END " +
                "and  CASE WHEN :filterByStationDestination !=  ''THEN BON_Trans_StatDest=:filterByStationDestination ELSE BON_Trans_StatDest !=:filterByStationDestination  END " +
                "and  CASE WHEN :filterByEtatBnTransfert !=  ''THEN BON_Trans_Etat=:filterByEtatBnTransfert ELSE BON_Trans_Etat !=:filterByEtatBnTransfert  END) " +

            " ORDER BY " +
            "CASE WHEN :sortBy = 'BON_Trans_Num'  AND :isAsc = 1 THEN BON_Trans_Num END ASC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Num'  AND :isAsc = 2 THEN BON_Trans_Num END DESC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Mnt_TTC'  AND :isAsc = 1 THEN (CAST (BON_Trans_Mnt_TTC AS REAL)) END ASC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Mnt_TTC'  AND :isAsc = 2 THEN (CAST (BON_Trans_Mnt_TTC AS REAL)) END DESC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Date'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',BON_Trans_Date) END ASC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Date'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',BON_Trans_Date) END DESC ",
    )
    fun filterByCodeStationSource(
        searchString: String,
        filterByStationSource: String,
        filterByStationDestination: String,
        filterByEtatBnTransfert: String,
        sortBy: String,
        isAsc: Int,
    ): Flow<Map<BonLivraison, List<LigneBonLivraisonWithArticle>>>

    @Transaction
    @Query(
        "SELECT * FROM $BON_LIVRAISON_TABLE " +
            "LEFT JOIN ${ProInventoryConstants.LIGNE_BON_LIVRAISON_TABLE} ON $BON_LIVRAISON_TABLE.BON_Trans_Num = ${ProInventoryConstants.LIGNE_BON_LIVRAISON_TABLE}.LG_BonTrans_NumBon" +
            " WHERE $BON_LIVRAISON_TABLE.BON_Trans_Num LIKE '%' || :searchString || '%' " +
                " and ( CASE WHEN :filterByStationSource !=  '' THEN BON_Trans_StatSource =:filterByStationSource ELSE BON_Trans_StatSource !=:filterByStationSource END " +
                "and  CASE WHEN :filterByStationDestination !=  ''THEN BON_Trans_StatDest=:filterByStationDestination ELSE BON_Trans_StatDest !=:filterByStationDestination  END " +
                "and  CASE WHEN :filterByEtatBnTransfert !=  ''THEN BON_Trans_Etat=:filterByEtatBnTransfert ELSE BON_Trans_Etat !=:filterByEtatBnTransfert  END) " +

            " ORDER BY " +
            "CASE WHEN :sortBy = 'BON_Trans_Num'  AND :isAsc = 1 THEN BON_Trans_Num END ASC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Num'  AND :isAsc = 2 THEN BON_Trans_Num END DESC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Mnt_TTC'  AND :isAsc = 1 THEN (CAST (BON_Trans_Mnt_TTC AS REAL)) END ASC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Mnt_TTC'  AND :isAsc = 2 THEN (CAST (BON_Trans_Mnt_TTC AS REAL)) END DESC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Date'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',BON_Trans_Date) END ASC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Date'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',BON_Trans_Date) END DESC ",
    )
    fun filterByBontransfertNum(
        searchString: String,
        filterByStationSource: String,
        filterByStationDestination: String,
        filterByEtatBnTransfert: String,
        sortBy: String,
        isAsc: Int,
    ): Flow<Map<BonLivraison, List<LigneBonLivraisonWithArticle>>>

    @Transaction
    @Query(
        "SELECT * FROM $BON_LIVRAISON_TABLE " +
            "LEFT JOIN ${ProInventoryConstants.LIGNE_BON_LIVRAISON_TABLE} ON $BON_LIVRAISON_TABLE.BON_Trans_Num = ${ProInventoryConstants.LIGNE_BON_LIVRAISON_TABLE}.LG_BonTrans_NumBon " +
            "WHERE  CASE WHEN :filterByStationSource !=  '' THEN BON_Trans_StatSource =:filterByStationSource ELSE BON_Trans_StatSource !=:filterByStationSource END   " +
            "and  CASE WHEN :filterByStationDestination !=  '' THEN BON_Trans_StatDest=:filterByStationDestination ELSE BON_Trans_StatDest !=:filterByStationDestination  END " +
              "and  CASE WHEN :filterByEtatBnTransfert !=  ''THEN BON_Trans_Etat=:filterByEtatBnTransfert ELSE BON_Trans_Etat !=:filterByEtatBnTransfert  END" +

            " ORDER BY " +
            "CASE WHEN :sortBy = 'BON_Trans_Num'  AND :isAsc = 1 THEN BON_Trans_Num END ASC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Num'  AND :isAsc = 2 THEN BON_Trans_Num END DESC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Mnt_TTC'  AND :isAsc = 1 THEN (CAST (BON_Trans_Mnt_TTC AS REAL)) END ASC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Mnt_TTC'  AND :isAsc = 2 THEN (CAST (BON_Trans_Mnt_TTC AS REAL)) END DESC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Date'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',BON_Trans_Date) END ASC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Date'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',BON_Trans_Date) END DESC ",
    )
    fun getAllFiltred(
        isAsc: Int,
        filterByStationSource: String,
        filterByStationDestination: String,
        filterByEtatBnTransfert: String,
        sortBy: String,
    ): Flow<Map<BonLivraison, List<LigneBonLivraisonWithArticle>>>

    // Paginated versions for better performance with large datasets
    @Query("SELECT * FROM $BON_LIVRAISON_TABLE ORDER BY timestamp DESC")
    fun getAllPaginated(): PagingSource<Int, BonLivraison>

    @Query("SELECT * FROM $BON_LIVRAISON_TABLE WHERE Status = :status ORDER BY timestamp DESC")
    fun getByStatusPaginated(status: String): PagingSource<Int, BonLivraison>

    @Query("SELECT * FROM $BON_LIVRAISON_TABLE WHERE BON_Trans_StatSource = :station ORDER BY timestamp DESC")
    fun getBySourceStationPaginated(station: String): PagingSource<Int, BonLivraison>

    @Query("SELECT * FROM $BON_LIVRAISON_TABLE WHERE BON_Trans_StatDest = :station ORDER BY timestamp DESC")
    fun getByDestinationStationPaginated(station: String): PagingSource<Int, BonLivraison>

    @Query(
        "SELECT * FROM $BON_LIVRAISON_TABLE " +
            " WHERE BON_Trans_Num LIKE '%' || :searchString || '%' " +
            " AND (CASE WHEN :filterByStationSource != '' THEN BON_Trans_StatSource = :filterByStationSource ELSE BON_Trans_StatSource != :filterByStationSource END " +
            " AND CASE WHEN :filterByStationDestination != '' THEN BON_Trans_StatDest = :filterByStationDestination ELSE BON_Trans_StatDest != :filterByStationDestination END " +
            " AND CASE WHEN :filterByEtatBnTransfert != '' THEN BON_Trans_Etat = :filterByEtatBnTransfert ELSE BON_Trans_Etat != :filterByEtatBnTransfert END) " +
            " ORDER BY " +
            "CASE WHEN :sortBy = 'BON_Trans_Num' AND :isAsc = 1 THEN BON_Trans_Num END ASC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Num' AND :isAsc = 2 THEN BON_Trans_Num END DESC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Date' AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S', BON_Trans_Date) END ASC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Date' AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S', BON_Trans_Date) END DESC"
    )
    fun filterByBonTransNumPaginated(
        searchString: String,
        filterByStationSource: String,
        filterByStationDestination: String,
        filterByEtatBnTransfert: String,
        sortBy: String,
        isAsc: Int
    ): PagingSource<Int, BonLivraison>

    @Query(
        "SELECT * FROM $BON_LIVRAISON_TABLE " +
            " WHERE (CASE WHEN :filterByStationSource != '' THEN BON_Trans_StatSource = :filterByStationSource ELSE BON_Trans_StatSource != :filterByStationSource END " +
            " AND CASE WHEN :filterByStationDestination != '' THEN BON_Trans_StatDest = :filterByStationDestination ELSE BON_Trans_StatDest != :filterByStationDestination END " +
            " AND CASE WHEN :filterByEtatBnTransfert != '' THEN BON_Trans_Etat = :filterByEtatBnTransfert ELSE BON_Trans_Etat != :filterByEtatBnTransfert END) " +
            " ORDER BY " +
            "CASE WHEN :sortBy = 'BON_Trans_Num' AND :isAsc = 1 THEN BON_Trans_Num END ASC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Num' AND :isAsc = 2 THEN BON_Trans_Num END DESC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Date' AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S', BON_Trans_Date) END ASC, " +
            "CASE WHEN :sortBy = 'BON_Trans_Date' AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S', BON_Trans_Date) END DESC"
    )
    fun getAllFiltredPaginated(
        isAsc: Int,
        filterByStationSource: String,
        filterByStationDestination: String,
        filterByEtatBnTransfert: String,
        sortBy: String
    ): PagingSource<Int, BonLivraison>

}
