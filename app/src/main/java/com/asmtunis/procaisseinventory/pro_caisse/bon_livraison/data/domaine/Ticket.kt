package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine


import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Entity(tableName = ProCaisseConstants.TICKET_TABLE)
@Serializable
data class Ticket(
    /* @PrimaryKey(autoGenerate = true)
     @Transient
     val id: Long = 0,*/
    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = "TIK_NumTicket_M")
    @SerialName("TIK_NumTicket_M")
    val tIKNumTicketM: String = "",

    @ColumnInfo(name = "DDmM")
    @SerialName("DDmM")
    val dDmM: String? = null,

    @ColumnInfo(name = "exportM")
    @SerialName("exportM")
    val exportM: String? = null,

    @ColumnInfo(name = "TIK_Annuler")
    @SerialName("TIK_Annuler")
    val tIKAnnuler: String? = null,

    @ColumnInfo(name = "TIK_CODE_COMMERCIAL")
    @SerialName("TIK_CODE_COMMERCIAL")
    val tIKCODECOMMERCIAL: String? = null,

    @ColumnInfo(name = "TIK_CodClt")
    @SerialName("TIK_CodClt")
    val tIKCodClt: String = "",

    @ColumnInfo(name = "TIK_Contrat_Champ1")
    @SerialName("TIK_Contrat_Champ1")
    val tIKContratChamp1: String? = null,

    @ColumnInfo(name = "TIK_Contrat_Champ2")
    @SerialName("TIK_Contrat_Champ2")
    val tIKContratChamp2: String? = null,

    @ColumnInfo(name = "TIK_DDm")
    @SerialName("TIK_DDm")
    val tIKDDm: String = "",

    @ColumnInfo(name = "TIK_DESIG_COMMERCIAL")
    @SerialName("TIK_DESIG_COMMERCIAL")
    val tIKDESIGCOMMERCIAL: String? = null,

    @ColumnInfo(name = "TIK_DateHeureTicket")
    @SerialName("TIK_DateHeureTicket")
    val tIKDateHeureTicket: String = "",

    @ColumnInfo(name = "TIK_DateLivraison")
    @SerialName("TIK_DateLivraison")
    val tIKDateLivraison: String? = null,

    @ColumnInfo(name = "TIK_Date_Mariage")
    @SerialName("TIK_Date_Mariage")
    val tIKDateMariage: String? = null,

    @ColumnInfo(name = "TIK_Echeance")
    @SerialName("TIK_Echeance")
    val tIKEcheance: String? = null,

    @ColumnInfo(name = "TIK_Emplacement_Mariage")
    @SerialName("TIK_Emplacement_Mariage")
    val tIKEmplacementMariage: String? = null,

    @ColumnInfo(name = "TIK_EnvWebServ")
    @SerialName("TIK_EnvWebServ")
    val tIKEnvWebServ: String? = null,

    @ColumnInfo(name = "TIK_Etat")
    @SerialName("TIK_Etat")
    val tIKEtat: String = "",

    @ColumnInfo(name = "TIK_EtatCom")
    @SerialName("TIK_EtatCom")
    val tIKEtatCom: String? = null,

    @ColumnInfo(name = "TIK_Exerc")
    @SerialName("TIK_Exerc")
    val tIKExerc: String = "",

    @ColumnInfo(name = "TIK_export")
    @SerialName("TIK_export")
    val tIKExport: String? = null,

    @ColumnInfo(name = "TIK_Gouvern")
    @SerialName("TIK_Gouvern")
    val tIKGouvern: String? = null,

    @ColumnInfo(name = "TIK_IdCarnet")
    @SerialName("TIK_IdCarnet")
    val tIKIdCarnet: String? = null,

    @ColumnInfo(name = "TIK_IdSCaisse")
    @SerialName("TIK_IdSCaisse")
    val tIKIdSCaisse: String = "",

    @ColumnInfo(name = "TIK_Influence")
    @SerialName("TIK_Influence")
    val tIKInfluence: String? = null,

    @ColumnInfo(name = "TIK_is_Contrat")
    @SerialName("TIK_is_Contrat")
    val tIKIsContrat: String? = null,

    @ColumnInfo(name = "TIK_LatitudeEv")
    @SerialName("TIK_LatitudeEv")
    val tIKLatitudeEv: String? = null,

    @ColumnInfo(name = "TIK_LatitudeSv")
    @SerialName("TIK_LatitudeSv")
    val tIKLatitudeSv: String? = null,

    @ColumnInfo(name = "TIK_Livre_le")
    @SerialName("TIK_Livre_le")
    val tIKLivreLe: String? = null,

    @ColumnInfo(name = "TIK_LongitudeEv")
    @SerialName("TIK_LongitudeEv")
    val tIKLongitudeEv: String? = null,

    @ColumnInfo(name = "TIK_LongitudeSv")
    @SerialName("TIK_LongitudeSv")
    val tIKLongitudeSv: String? = null,

    @ColumnInfo(name = "TIK_Mnt_Bonus")
    @SerialName("TIK_Mnt_Bonus")
    val tIKMntBonus: String? = null,

    @ColumnInfo(name = "TIK_MtCarteBanq")
    @SerialName("TIK_MtCarteBanq")
    val tIKMtCarteBanq: String? = null,

    @ColumnInfo(name = "TIK_MtCheque")
    @SerialName("TIK_MtCheque")
    val tIKMtCheque: String? = null,

    @ColumnInfo(name = "TIK_MtEspece")
    @SerialName("TIK_MtEspece")
    val tIKMtEspece: String? = null,

    @ColumnInfo(name = "TIK_MtHT")
    @SerialName("TIK_MtHT")
    val tIKMtHT: String? = null,

    @ColumnInfo(name = "TIK_MtRemise")
    @SerialName("TIK_MtRemise")
    val tIKMtRemise: String? = null,

    @ColumnInfo(name = "TIK_MtTTC")
    @SerialName("TIK_MtTTC")
    var tIKMtTTC: String = "",

    @ColumnInfo(name = "TIK_MtTVA")
    @SerialName("TIK_MtTVA")
    val tIKMtTVA: String? = null,

    @ColumnInfo(name = "TIK_Mtrecue")
    @SerialName("TIK_Mtrecue")
    val tIKMtrecue: String? = null,

    @ColumnInfo(name = "TIK_Mtrendue")
    @SerialName("TIK_Mtrendue")
    val tIKMtrendue: String? = null,

    @ColumnInfo(name = "TIK_NbrImp")
    @SerialName("TIK_NbrImp")
    val tIKNbrImp: String? = null,

    @ColumnInfo(name = "TIK_Nbre_Pts_Gain")
    @SerialName("TIK_Nbre_Pts_Gain")
    val tIKNbrePtsGain: String? = null,

    @ColumnInfo(name = "TIK_Nbre_Total_Pts")
    @SerialName("TIK_Nbre_Total_Pts")
    val tIKNbreTotalPts: String? = null,

    @ColumnInfo(name = "TIK_NomClient")
    @SerialName("TIK_NomClient")
    val tIKNomClient: String = "",

    @ColumnInfo(name = "TIK_Num_Carte")
    @SerialName("TIK_Num_Carte")
    val tIKNumCarte: String? = null,

    @ColumnInfo(name = "TIK_NumCheque")
    @SerialName("TIK_NumCheque")
    val tIKNumCheque: String? = null,

    @ColumnInfo(name = "TIK_Num_Contrat")
    @SerialName("TIK_Num_Contrat")
    val tIKNumContrat: String? = null,

    @ColumnInfo(name = "TIK_NumFact")
    @SerialName("TIK_NumFact")
    val tIKNumFact: String = "",

    @ColumnInfo(name = "TIK_NumTicket")
    @SerialName("TIK_NumTicket")
    val tIKNumTicket: String = "",

    @ColumnInfo(name = "TIK_NumeroBL")
    @SerialName("TIK_NumeroBL")
    val tIKNumeroBL: String? = null,

    @ColumnInfo(name = "TIK_Regler")
    @SerialName("TIK_Regler")
    val tIKRegler: String? = null,

    @ColumnInfo(name = "TIK_Retour_le")
    @SerialName("TIK_Retour_le")
    val tIKRetourLe: String? = null,

    @ColumnInfo(name = "TIK_Source")
    @SerialName("TIK_Source")
    val tIKSource: String? = null,

    @ColumnInfo(name = "TIK_station")
    @SerialName("TIK_station")
    val tIKStation: String = "",

    @ColumnInfo(name = "TIK_TauxRemise")
    @SerialName("TIK_TauxRemise")
    val tIKTauxRemise: String? = null,

    @ColumnInfo(name = "TIK_Timbre")
    @SerialName("TIK_Timbre")
    val tIKTimbre: String = "",

    @ColumnInfo(name = "TIK_TypeTiket")
    @SerialName("TIK_TypeTiket")
    val tIKTypeTiket: String? = null,

    @ColumnInfo(name = "TIK_user")
    @SerialName("TIK_user")
    val tIKUser: String? = null,

    //oNLY lOCAL
    @SerialName("TIK_Nbr_Lin")
    @ColumnInfo(name = "TIK_Nbr_Lin")
    val nbrLinTIK: String? = null,

    @ColumnInfo(name = "Mnt_RevImp")
    @SerialName("Mnt_RevImp")
    var mntRevImp: Double? = null,

    @ColumnInfo(name = "debugDuplicatedTikNUm")
    @SerialName("debugDuplicatedTikNUm")
    val debugDuplicatedTikNUm: String? = null,

    @ColumnInfo(name = "TIK_MtCredit")
    @Transient
    val tIKMtCredit: Double = 0.0,


    @ColumnInfo(name = "Syn_Error_Msg")
    @Transient
    val syncErrorMsg: String = "", // uded only local to show sync error msg
) : BaseModel() {
    @Ignore
    @Transient
    var nbreTicketsRecap: String? = ""

    @Ignore
    val tIKDDmFormatted = this.tIKDDm.substringBefore(".")
}

/*

package com.asmtunis.asm.data.ticket.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.asm.core.localdb.core.ProCaisseConstants
import com.asmtunis.asm.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.TICKET_TABLE)
@Serializable
data class Tickets(

    @ColumnInfo(name = "TIK_NumTicket")
    @SerialName("TIK_NumTicket")
    val tIKNumTicket: Int = 0,

    @PrimaryKey
    @ColumnInfo(name = "TIK_NumTicket_M")
    @SerialName("TIK_NumTicket_M")
    val tikNumTicketM: String? = null,

    @ColumnInfo(name = "TIK_Exerc")
    @SerialName("TIK_Exerc")
    val tIKExerc: String? = null,

    @ColumnInfo(name = "TIK_IdCarnet")
    @SerialName("TIK_IdCarnet")
    val tIKIdCarnet: String? = null,

    @ColumnInfo(name = "TIK_DateHeureTicket")
    @SerialName("TIK_DateHeureTicket")
    val tIKDateHeureTicket: String? = null,

    @ColumnInfo(name = "TIK_IdSCaisse")
    @SerialName("TIK_IdSCaisse")
    val tIKIdSCaisse: String? = null,

    @ColumnInfo(name = "TIK_CodClt")
    @SerialName("TIK_CodClt")
    val tIKCodClt: String? = null,

    @ColumnInfo(name = "TIK_NumFact")
    @SerialName("TIK_NumFact")
    val tIKNumFact: Double = 0.0,

    @ColumnInfo(name = "TIK_Regler")
    @SerialName("TIK_Regler")
    val tIKRegler: Double = 0.0,

    @ColumnInfo(name = "TIK_MtTTC")
    @SerialName("TIK_MtTTC")
    val tIKMtTTC: Double = 0.0,

    @ColumnInfo(name = "TIK_MtRemise")
    @SerialName("TIK_MtRemise")
    val tIKMtRemise: Double = 0.0,

    @ColumnInfo(name = "TIK_MtHT")
    @SerialName("TIK_MtHT")
    val tIKMtHT: Double = 0.0,

    @ColumnInfo(name = "TIK_MtTVA")
    @SerialName("TIK_MtTVA")
    val tIKMtTVA: Double = 0.0,

    @ColumnInfo(name = "TIK_TypeTiket")
    @SerialName("TIK_TypeTiket")
    val tIKTypeTiket: String? = null,

    @ColumnInfo(name = "TIK_MtEspece")
    @SerialName("TIK_MtEspece")
    val tIKMtEspece: Double = 0.0,

    @ColumnInfo(name = "TIK_MtCheque")
    @SerialName("TIK_MtCheque")
    val tIKMtCheque: String? = null,

    @ColumnInfo(name = "TIK_NumCheque")
    @SerialName("TIK_NumCheque")
    val tIKNumCheque: String? = null,

    @ColumnInfo(name = "TIK_Echeance")
    @SerialName("TIK_Echeance")
    val tIKEcheance: String? = null,

    @ColumnInfo(name = "TIK_MtCarteBanq")
    @SerialName("TIK_MtCarteBanq")
    val tIKMtCarteBanq: Double = 0.0,

    @ColumnInfo(name = "TIK_Mtrecue")
    @SerialName("TIK_Mtrecue")
    val tIKMtrecue: Double = 0.0,

    @ColumnInfo(name = "TIK_Mtrendue")
    @SerialName("TIK_Mtrendue")
    val tIKMtrendue: Double = 0.0,


    @ColumnInfo(name = "TIK_MtCredit")
//@Exclude
    val tIKMtCredit: Double = 0.0,

    @ColumnInfo(name = "TIK_Etat")
    @SerialName("TIK_Etat")
    val tIKEtat: String? = null,

    @ColumnInfo(name = "TIK_DateLivraison")
    @SerialName("TIK_DateLivraison")
    val tIKDateLivraison: String? = null,

    @ColumnInfo(name = "TIK_NomClient")
    @SerialName("TIK_NomClient")
    val tIKNomClient: String? = null,

    @ColumnInfo(name = "TIK_user")
    @SerialName("TIK_user")
    val tIKUser: String? = null,

    @ColumnInfo(name = "TIK_station")
    @SerialName("TIK_station")
    val tIKStation: String? = null,

    @ColumnInfo(name = "TIK_export")
    @SerialName("TIK_export")
    val tIKExport: Int = 0,

    @ColumnInfo(name = "TIK_DDm")
    @SerialName("TIK_DDm")

    val tIKDDm: String? = null,

    @ColumnInfo(name = "TIK_Annuler")
    @SerialName("TIK_Annuler")
    val tIKAnnuler: Int = 0,

    @ColumnInfo(name = "TIK_CODE_COMMERCIAL")
    @SerialName("TIK_CODE_COMMERCIAL")
    val tIKCODECOMMERCIAL: String? = null,

    @ColumnInfo(name = "TIK_DESIG_COMMERCIAL")
    @SerialName("TIK_DESIG_COMMERCIAL")
    val tIKDESIGCOMMERCIAL: String? = null,

    @ColumnInfo(name = "TIK_is_Contrat")
    @SerialName("TIK_is_Contrat")
    val tIKIsContrat: Int = 0,

    @ColumnInfo(name = "TIK_Num_Contrat")
    @SerialName("TIK_Num_Contrat")
    val tIKNumContrat: String? = null,

    @ColumnInfo(name = "TIK_Date_Mariage")
    @SerialName("TIK_Date_Mariage")
    val tIKDateMariage: String? = null,

    @ColumnInfo(name = "TIK_Emplacement_Mariage")
    @SerialName("TIK_Emplacement_Mariage")
    val tIKEmplacementMariage: String? = null,

    @ColumnInfo(name = "TIK_Contrat_Champ1")
    @SerialName("TIK_Contrat_Champ1")
    val tIKContratChamp1: String? = null,

    @ColumnInfo(name = "TIK_Contrat_Champ2")
    @SerialName("TIK_Contrat_Champ2")
    val tIKContratChamp2: String? = null,

    @ColumnInfo(name = "TIK_Nbre_Pts_Gain")
    @SerialName("TIK_Nbre_Pts_Gain")
    val tIKNbrePtsGain: Int = 0,

    @ColumnInfo(name = "TIK_Nbre_Total_Pts")
    @SerialName("TIK_Nbre_Total_Pts")
    val tIKNbreTotalPts: Double = 0.0,

    @ColumnInfo(name = "TIK_Num_Carte")
    @SerialName("TIK_Num_Carte")
    val tIKNumCarte: String? = null,

    @SerialName("TIK_Mnt_Bonus")
    @ColumnInfo(name = "TIK_Mnt_Bonus")
    val tIKMntBonus: String? = null,

    @SerialName("TIK_Source")
    @ColumnInfo(name = "TIK_Source")
    val tIK_Source: String? = null,

    @ColumnInfo(name = "TIK_TauxRemise")
    @SerialName("TIK_TauxRemise")
    val tIKTauxRemise: Double = 0.0,

    @ColumnInfo(name = "TIK_NumeroBL")
    @SerialName("TIK_NumeroBL")
    val tIKNumeroBL: String? = null,

    @ColumnInfo(name = "TIK_EnvWebServ")
    @SerialName("TIK_EnvWebServ")
    val tIKEnvWebServ: Int = 0,

    @SerialName("TIK_LatitudeEv")
    val tIKLatitudeEv: Double = 0.0,

    @SerialName("TIK_LongitudeEv")
    val tIKLongitudeEv: Double = 0.0,

    @SerialName("TIK_LatitudeSv")
    val tIKLatitudeSv: Double = 0.0,

    @SerialName("TIK_LongitudeSv")
    val tIKLongitudeSv: Double = 0.0,

    @ColumnInfo(name = "TIK_Timbre")
    @SerialName("TIK_Timbre")
    val timbre: Double = 0.0,


) : BaseModel()
 */