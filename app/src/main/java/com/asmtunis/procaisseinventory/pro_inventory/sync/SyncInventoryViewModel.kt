package com.asmtunis.procaisseinventory.pro_inventory.sync

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.enum_classes.InventoryStatus
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROINVENTORY_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.sync.SyncEntity
import com.asmtunis.procaisseinventory.core.sync.SyncManager
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.BonEntree
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.BonEntreeResponse
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.LigneBonEntree
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.AddBatchBonLivraisonResponse
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.BonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.data.NestedItem
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryRemote
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.Inventaire
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.domaine.AddTicketRayonItem
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.domaine.TicketRayon
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject

@HiltViewModel
class SyncInventoryViewModel
    @Inject
    constructor(
        private val syncManager: SyncManager,
        @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
        @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
        @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
        @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
        private val proInventoryRemote: ProInventoryRemote,
        private val proInventoryLocalDb: ProInventoryLocalDb,
        private val proCaisseLocalDb: ProCaisseLocalDb,
        private val listenNetwork: ListenNetwork,
        // app: Application
    ) : ViewModel() { // : AndroidViewModel(app) {

    // Sync state from SyncManager
    val syncState = syncManager.syncState

    private var autoSyncState  by mutableStateOf(false)
    private var autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(key = PROINVENTORY_AUTO_SYNC_AUTHORISATION, default = true).distinctUntilChanged()
    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()

    private var  connected  by mutableStateOf(false)

    init {
            getNotSyncAchat()
            getNotSyncBnTransfert()
            getNotSyncInventaire()
            getNotSyncTicketRayon()
        }

        /**
         * ACHAT
         */
        var responseAddAchatState: RemoteResponseState<List<BonEntreeResponse>> by mutableStateOf(RemoteResponseState())
            private set
        var bonEntreeNotSync: Map<BonEntree, List<LigneBonEntree>> by mutableStateOf(emptyMap())
            private set
    var notSyncBonEntreeObj : String by mutableStateOf("")
        private set
        private fun getNotSyncAchat() {
            viewModelScope.launch {
                val achatNotSyncFlow = proInventoryLocalDb.bonEntree.noSynced().distinctUntilChanged()


                combine(networkFlow, achatNotSyncFlow, autoSyncFlow) { isConnected, achatNotSyncList, autoSync ->
                    connected = isConnected
                    autoSyncState = autoSync
                    achatNotSyncList.ifEmpty { emptyMap() }
                }.collect {

                    if (it.isEmpty()) {
                        bonEntreeNotSync = emptyMap()
                        return@collect
                    }
                    bonEntreeNotSync = it
                    if(connected && autoSyncState) syncAchat()
                }
            }
        }

        fun syncAchat(selectedBnEntree: BonEntree = BonEntree()) {

            viewModelScope.launch(dispatcherIO) {
                val nestedItems: ArrayList<NestedItem<BonEntree, List<LigneBonEntree>>> = ArrayList()
                val nestedItem = NestedItem<BonEntree, List<LigneBonEntree>>()

                bonEntreeNotSync.forEach { (key, value) ->
                    run {
                        nestedItem.parent = key
                        nestedItem.children = value

                        nestedItems.add(nestedItem)
                    }
                }
                      if(selectedBnEntree != BonEntree()) nestedItems.removeIf { it.parent != selectedBnEntree }
                val baseConfigObj =
                    GenericObject(
                        proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()?.let { Json.decodeFromString(it) }?: BaseConfig(),
                        Json.encodeToJsonElement(nestedItems),
                    )
                notSyncBonEntreeObj = Json.encodeToString(baseConfigObj)
                proInventoryRemote.bonEntree.addBatchBonEntreesWithLines(notSyncBonEntreeObj).onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            for (i in result.data!!.indices) {
                                if (result.data[i].code == 10200) {
                                    proInventoryLocalDb.bonEntree.updateBonEntreeStatus(
                                        bonEntNum = result.data[i].bONENTNum,
                                        bonEntNumM = result.data[i].bONENTNumM,
                                        exercice = result.data[i].bONENTExer,
                                    )
                                    proInventoryLocalDb.ligneBonEntree.updateLigneBonEntreeStatus(
                                        bonEntNum = result.data[i].bONENTNum,
                                        bonEntNumM = result.data[i].bONENTNumM,
                                        exercice = result.data[i].bONENTExer,
                                    )
                                }
                                // TODO IMPLEMENT A LIST FOR EXP TO STORE ERROR MSG
                            }

                            responseAddAchatState = RemoteResponseState(data = result.data, loading = false, error = null)
                        }

                        is DataResult.Loading -> {
                            responseAddAchatState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            responseAddAchatState = RemoteResponseState(data = null, loading = false, error = result.message, message = selectedBnEntree.bONENTNum)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }
        }

        /**
         * Bon Transfert
         */

        var responseAddBonTransfertState: RemoteResponseState<List<AddBatchBonLivraisonResponse>> by mutableStateOf(RemoteResponseState())
            private set
        var bonTransfertNotSync: Map<BonLivraison, List<LigneBonLivraison>> by mutableStateOf(emptyMap())
            private set
    var notSyncBonTransfertObj : String by mutableStateOf("")
        private set
        private fun getNotSyncBnTransfert() {
            viewModelScope.launch {
                val bonTransfertNotSyncFlow =  proInventoryLocalDb.bonLivraison.noSynced().distinctUntilChanged()


                combine(networkFlow, bonTransfertNotSyncFlow, autoSyncFlow) { isConnected, bonTransfertNotSyncList, autoSync ->
                    connected = isConnected
                    autoSyncState = autoSync
                    bonTransfertNotSyncList?.ifEmpty { emptyMap() }?: emptyMap()
                }.collect {

                    if (it.isEmpty()) {
                        bonTransfertNotSync = emptyMap()
                        return@collect
                    }
                    bonTransfertNotSync = it
                    if(connected && autoSyncState) syncBnTransfert()
                }
            }
        }

        fun syncBnTransfert(selectedBnTransfert: BonLivraison = BonLivraison()) {
            viewModelScope.launch(dispatcherIO) {
                val nestedItems: ArrayList<NestedItem<BonLivraison, List<LigneBonLivraison>>> = ArrayList()
                val nestedItem = NestedItem<BonLivraison, List<LigneBonLivraison>>()

                bonTransfertNotSync.forEach { (key, value) ->
                    run {
                        nestedItem.parent = key
                        nestedItem.children = value
                        nestedItems.add(nestedItem)
                    }
                }

                if(selectedBnTransfert != BonLivraison()) nestedItems.removeIf { it.parent != selectedBnTransfert }

                val baseConfigObj =
                    GenericObject(
                        proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()?.let { Json.decodeFromString(it) }?: BaseConfig(),
                        Json.encodeToJsonElement(nestedItems),
                    )
                notSyncBonTransfertObj = Json.encodeToString(baseConfigObj)
                proInventoryRemote.bonLivraison.addBatchBonLivraisonWithLines(notSyncBonTransfertObj).onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            for (i in result.data!!.indices) {
                                if (result.data[i].code == 10200)
                                    {

                                        proInventoryLocalDb.bonLivraison.updateState(
                                            code = result.data[i].bonTransNum,
                                            codeM = result.data[i].bonTransNumM,
                                            exercice = result.data[i].bonTransExerc,
                                        )

                                        proInventoryLocalDb.ligneBonLivraison.updateState(
                                            code = result.data[i].bonTransNum,
                                            codeM = result.data[i].bonTransNumM,
                                            exercice = result.data[i].bonTransExerc,
                                        )
                                    }
                            }

                            responseAddBonTransfertState = RemoteResponseState(data = result.data, loading = false, error = null)
                        }

                        is DataResult.Loading -> {
                            responseAddBonTransfertState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            responseAddBonTransfertState = RemoteResponseState(data = null, loading = false, error = result.message, message = selectedBnTransfert.bONTransNum)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }
        }

        /**
         * I N V E N T A I R E
         */

        var responseAddInventaireState: RemoteResponseState<List<NestedItem<Inventaire, List<LigneInventaire>>>> by mutableStateOf(RemoteResponseState())
            private set
        var inventaireNotSync: Map<Inventaire, List<LigneInventaire>> by mutableStateOf(emptyMap())
            private set
    var notSyncInventaireObj : String by mutableStateOf("")
        private set
        private fun getNotSyncInventaire() {
            viewModelScope.launch {

                val inventaireNotSyncFlow =  proInventoryLocalDb.inventaire.noSynced().distinctUntilChanged()


                combine(networkFlow, inventaireNotSyncFlow, autoSyncFlow) { isConnected, inventaireNotSyncList, autoSync ->
                    connected = isConnected
                    autoSyncState = autoSync
                    inventaireNotSyncList.ifEmpty { emptyMap() }
                }.collect {

                    if (it.isEmpty()) {
                        inventaireNotSync = emptyMap()
                        return@collect
                    }
                    inventaireNotSync = it
                    if(connected && autoSyncState) syncInventaire()
                }
            }
        }

        fun syncInventaire(selectedInv: Inventaire = Inventaire()) {
            viewModelScope.launch(dispatcherIO) {
                val nestedItems: ArrayList<NestedItem<Inventaire, List<LigneInventaire>>> = ArrayList()
                val nestedItem = NestedItem<Inventaire, List<LigneInventaire>>()

                inventaireNotSync.forEach { (key, value) ->
                    run {
                        nestedItem.parent = key
                        nestedItem.children = value
                        nestedItems.add(nestedItem)
                    }
                }
                if(selectedInv != Inventaire()) nestedItems.removeIf { it.parent != selectedInv }
                val baseConfigObj =
                    GenericObject(
                        connexion = proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()?.let { Json.decodeFromString(it) }?: BaseConfig(),
                       data = Json.encodeToJsonElement(nestedItems),
                    )
                notSyncInventaireObj = Json.encodeToString(baseConfigObj)
                proInventoryRemote.inventaire.addBatchInventairesWithLines(notSyncInventaireObj).onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            if(result.data?.indices != null) {
                                for (i in result.data.indices) {

                                    val codeM = result.data[i].parent?.invCodeM?: nestedItems[i].parent?.invCodeM?: ""
                                    val code = result.data[i].parent?.iNVCode?: ""

                                    proInventoryLocalDb.inventaire.updateNumAndEtat(
                                        code = code,
                                        codeLocal = codeM,
                                        etat = InventoryStatus.UPDATED.status
                                    )

                                    proInventoryLocalDb.ligneInventaire.updateStatus(
                                        code = code,
                                        codeLocal = codeM,
                                        etat = InventoryStatus.UPDATED.status
                                    )
                                }
                            }

                            responseAddInventaireState = RemoteResponseState(data = result.data, loading = false, error = null)
                        }

                        is DataResult.Loading -> {
                            responseAddInventaireState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            responseAddInventaireState = RemoteResponseState(data = null, loading = false, error = result.message, message = selectedInv.iNVCode)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }
        }

        /**
         *   T I C K E T     R A Y O N
         */

        var responseAddTicketRayonState: RemoteResponseState<List<AddTicketRayonItem>> by mutableStateOf(RemoteResponseState())
            private set
        var ticketRayonNotSync: List<TicketRayon> by mutableStateOf(emptyList())
            private set
    var notSyncTicketRayonObj : String by mutableStateOf("")
        private set
        private fun getNotSyncTicketRayon() {
            viewModelScope.launch {

                val ticketRayonNotSyncFlow =  proInventoryLocalDb.ticketRayon.notSync().distinctUntilChanged()


                combine(networkFlow, ticketRayonNotSyncFlow, autoSyncFlow) { isConnected, ticketRayonNotSyncList, autoSync ->
                    connected = isConnected
                    autoSyncState = autoSync
                    ticketRayonNotSyncList.ifEmpty { emptyList() }
                }.collect {

                    if (it.isEmpty()) {
                        ticketRayonNotSync = emptyList()
                        return@collect
                    }
                    ticketRayonNotSync = it
                    if(connected && autoSyncState) syncTicketRayon()
                }
            }
        }

        fun syncTicketRayon(selectedTickRayon: TicketRayon = TicketRayon()) {
            viewModelScope.launch(dispatcherIO) {
                val baseConfigObj =
                    GenericObject(
                        proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()?.let { Json.decodeFromString(it) }?: BaseConfig(),
                        Json.encodeToJsonElement(if(selectedTickRayon != TicketRayon()) listOf(selectedTickRayon) else ticketRayonNotSync),
                    )
                notSyncTicketRayonObj = Json.encodeToString(baseConfigObj)
                proInventoryRemote.ticketRayon.addTicketRayon(notSyncTicketRayonObj).onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            for (ticketRayon in result.data!!) {
                                if (ticketRayon.code == 10200) {
                                    proInventoryLocalDb.ticketRayon.updateSyncTicketRayon(ticketRayon.aRTCode)
                                }
                            }

                            responseAddTicketRayonState = RemoteResponseState(data = result.data, loading = false, error = null)
                        }

                        is DataResult.Loading -> {
                            responseAddTicketRayonState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            responseAddTicketRayonState = RemoteResponseState(data = null, loading = false, error = result.message, message = selectedTickRayon.aRTDesignation)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }
        }

    // ========================================
    // SYNCMANAGER INTEGRATION & CONVENIENCE METHODS
    // ========================================

    /**
     * Trigger manual sync for all ProInventory entities using SyncManager
     */
    fun triggerManualSyncAll() {
        viewModelScope.launch {
            try {
                // Try SyncManager for each entity type
                val achatResult = syncManager.syncEntity(SyncEntity.BON_ENTREE)
                val transfertResult = syncManager.syncEntity(SyncEntity.BON_TRANSFERT)
                val inventaireResult = syncManager.syncEntity(SyncEntity.INVENTAIRE)
                val ticketResult = syncManager.syncEntity(SyncEntity.TICKET_RAYON)

                // Fallback to individual sync methods if any SyncManager calls fail
                if (!achatResult.isSuccess && bonEntreeNotSync.isNotEmpty()) {
                    syncAchat()
                }
                if (!transfertResult.isSuccess && bonTransfertNotSync.isNotEmpty()) {
                    syncBnTransfert()
                }
                if (!inventaireResult.isSuccess && inventaireNotSync.isNotEmpty()) {
                    syncInventaire()
                }
                if (!ticketResult.isSuccess && ticketRayonNotSync.isNotEmpty()) {
                    syncTicketRayon()
                }
            } catch (exception: Exception) {
                // Fallback to individual sync methods on exception
                syncAchat()
                syncBnTransfert()
                syncInventaire()
                syncTicketRayon()
            }
        }
    }

    /**
     * Check if any sync operation is currently in progress
     */
    val isSyncing: Boolean
        get() = responseAddAchatState.loading ||
                responseAddBonTransfertState.loading ||
                responseAddInventaireState.loading ||
                responseAddTicketRayonState.loading

    /**
     * Get the current sync error from any active sync operation
     */
    val syncError: String?
        get() = responseAddAchatState.error
            ?: responseAddBonTransfertState.error
            ?: responseAddInventaireState.error
            ?: responseAddTicketRayonState.error

    /**
     * Get the total count of unsynchronized items across all ProInventory entities
     */
    val unsyncedCount: Int
        get() = bonEntreeNotSync.size +
                bonTransfertNotSync.size +
                inventaireNotSync.size +
                ticketRayonNotSync.size

    /**
     * Reset all sync states to initial state
     */
    fun resetAllSyncStates() {
        responseAddAchatState = RemoteResponseState()
        responseAddBonTransfertState = RemoteResponseState()
        responseAddInventaireState = RemoteResponseState()
        responseAddTicketRayonState = RemoteResponseState()
    }

    /**
     * Get sync status summary for UI display
     */
    val syncStatusSummary: String
        get() = when {
            isSyncing -> "Synchronisation ProInventory en cours..."
            syncError != null -> "Erreur de synchronisation: $syncError"
            unsyncedCount > 0 -> "$unsyncedCount éléments ProInventory non synchronisés"
            else -> "Tous les éléments ProInventory sont synchronisés"
        }

    /**
     * Get detailed sync status for each entity type
     */
    val detailedSyncStatus: Map<String, String>
        get() = mapOf(
            "Bon Entrée" to when {
                responseAddAchatState.loading -> "Synchronisation en cours..."
                responseAddAchatState.error != null -> "Erreur: ${responseAddAchatState.error}"
                bonEntreeNotSync.isEmpty() -> "Synchronisé"
                else -> "${bonEntreeNotSync.size} non synchronisés"
            },
            "Bon Transfert" to when {
                responseAddBonTransfertState.loading -> "Synchronisation en cours..."
                responseAddBonTransfertState.error != null -> "Erreur: ${responseAddBonTransfertState.error}"
                bonTransfertNotSync.isEmpty() -> "Synchronisé"
                else -> "${bonTransfertNotSync.size} non synchronisés"
            },
            "Inventaire" to when {
                responseAddInventaireState.loading -> "Synchronisation en cours..."
                responseAddInventaireState.error != null -> "Erreur: ${responseAddInventaireState.error}"
                inventaireNotSync.isEmpty() -> "Synchronisé"
                else -> "${inventaireNotSync.size} non synchronisés"
            },
            "Ticket Rayon" to when {
                responseAddTicketRayonState.loading -> "Synchronisation en cours..."
                responseAddTicketRayonState.error != null -> "Erreur: ${responseAddTicketRayonState.error}"
                ticketRayonNotSync.isEmpty() -> "Synchronisé"
                else -> "${ticketRayonNotSync.size} non synchronisés"
            }
        )
}
