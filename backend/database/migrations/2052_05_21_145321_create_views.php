<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateViews extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        try {
            if (!Schema::hasTable('View_depenseCaisse')) {

                DB::statement(
                    "create VIEW View_depenseCaisse
AS
SELECT     dbo.depence_caisse.DEP_Code, dbo.DEPENCE.DEP_Lib, dbo.depence_caisse.DEP_Montant, dbo.station.STAT_Code, dbo.station.STAT_Desg, dbo.station.STAT_Adresse, dbo.Utilisateur.Code_Ut, 
                      dbo.Utilisateur.Nom, dbo.Utilisateur.Prenom, dbo.depence_caisse.DEP_descrip, dbo.depence_caisse.DDm
FROM         dbo.depence_caisse INNER JOIN
                      dbo.SessionCaisse ON dbo.depence_caisse.DEP_SC_IdSCession = dbo.SessionCaisse.SC_IdSCaisse INNER JOIN
                      dbo.Caisse ON dbo.SessionCaisse.SC_Caisse = dbo.Caisse.CAI_IdCaisse INNER JOIN
                      dbo.station ON dbo.Caisse.CAI_Station = dbo.station.STAT_Code INNER JOIN
                      dbo.DEPENCE ON dbo.depence_caisse.DEP_Code = dbo.DEPENCE.DEP_Code INNER JOIN
                      dbo.Utilisateur ON dbo.SessionCaisse.SC_CodeUtilisateur = dbo.Utilisateur.Code_Ut"
                );
            }
        } catch (\Exception $e) {
        }
        try {
            if (!Schema::hasTable('View_Historique_suppressArt')) {

                DB::statement(
                    "create VIEW View_Historique_suppressArt
AS
SELECT        dbo.Historique_Article_Caisse.id, dbo.Historique_Article_Caisse.S_codeArt, dbo.Historique_Article_Caisse.S_Qte, dbo.Historique_Article_Caisse.S_Carnet, dbo.Historique_Article_Caisse.S_Caisse, 
                         dbo.Historique_Article_Caisse.S_Date, dbo.Historique_Article_Caisse.S_Session, dbo.Historique_Article_Caisse.S_PEnc, dbo.Historique_Article_Caisse.S_PTotal, dbo.Historique_Article_Caisse.S_Ticket, 
                         dbo.Historique_Article_Caisse.S_User, dbo.Historique_Article_Caisse.S_Type, dbo.Historique_Article_Caisse.exportM, dbo.Historique_Article_Caisse.DDmM, dbo.article.ART_Designation, 
                         dbo.Utilisateur.Nom + dbo.Utilisateur.Prenom AS caissier, dbo.Caisse.CAI_IdCaisse, dbo.station.STAT_Desg, dbo.station.STAT_Adresse, dbo.station.STAT_Code
FROM            dbo.Caisse INNER JOIN
                         dbo.SessionCaisse ON dbo.Caisse.CAI_IdCaisse = dbo.SessionCaisse.SC_Caisse INNER JOIN
                         dbo.Historique_Article_Caisse INNER JOIN
                         dbo.article ON dbo.Historique_Article_Caisse.S_codeArt = dbo.article.ART_Code INNER JOIN
                         dbo.Utilisateur ON dbo.Historique_Article_Caisse.S_User = dbo.Utilisateur.Code_Ut ON dbo.SessionCaisse.SC_IdSCaisse = dbo.Historique_Article_Caisse.S_Session INNER JOIN
                         dbo.station ON dbo.Caisse.CAI_Station = dbo.station.STAT_Code"
                );
            }
        } catch (\Exception $e) {
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement("DROP VIEW View_Historique_suppressArt");
        DB::statement("DROP VIEW View_depenseCaisse");
    }
}
