<?php

namespace App\Models;


use App\Helpers\DatabaseConnection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;


class article extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'article';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'ART_Code';

    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['ART_Designation',
        'ART_PrixUnitaireHT',
        'ART_CodeBar',
        'ART_DesgCourte',
        'ART_Famille',
        'ART_Marque', 'ART_MargeB',
        'ART_PrixUnitaireHTVA',
        'ART_TypePrixUnitaireHTVA',
        'ART_TVA', 'ART_Couleur',
        'ART_User', 'ART_Station', 'ART_QTEmin', 'ART_Fodec', 'ART_DC', 'ART_QTEmax', 'ART_TAILLE', 'ART_QteDeclaree', 'ART_Prix_AchatFactReel', 'ART_Image', 'ART_DateCr', 'ART_QteStock', 'ART_NumSerie', 'ART_IsTaktile', 'ART_PrixGros1', 'ART_QtePrixGros1', 'ART_PrixGros2', 'ART_QtePrixGros2', 'ART_PrixGros3', 'ART_QteGros3', 'ART_CoulBN', 'ART_PrixUnitaireHTRes', 'ART_PrixUnitaireHTGlobale', 'ART_export', 'ART_ddm', 'PrixSolde', 'TauxSolde', 'ART_Equivalence', 'Regularisation', 'ART_Cout_charge', 'ART_codeSerie', 'CRPonderer', 'QTE_Restante', 'Coeff_charge', 'Charge_tot_coeff', 'Type_Produit', 'Type_service', 'is_bloquer', 'ART_CodeFrs', 'ART_Fournisseur', 'ART_Poid_Qte', 'Emp_Code', 'Touche_Balance', 'Type_Balance', 'Anc_cout', 'Art_Session', 'Art_Prom', 'Art_NbProm', 'Art_TauxProm', 'Remise_Fidelite', 'photo_Path', 'ddm', 'export', 'Ecrivain', 'Collection', 'is_Calcul_inverse', 'is_Tacktil', 'is_Peremption', 'Nbr_Jour_Peremption', 'is_Promo_Qte_Prix', 'ART_CodeFrs2', 'ART_ChezFrs2', 'ART_CodeFrs3', 'ART_ChezFrs3', 'is_AlertStock', 'Art_MaxTRemise', 'Art_MntRemise', 'Poste', 'Art_NbrePoint', 'Art_SousFamille', 'Art_DesigFact', 'Art_Champ1', 'Art_Champ2', 'Art_Champ3', 'Art_NbrePiece', 'is_supplement', 'is_numSerie', 'couleur', 'periode', 'IsService', 'IsDistributeur', 'Ing1', 'Ing2', 'Ing3', 'Ing4', 'Ing5', 'Ing6', 'Ing7', 'Ing8', 'Ing9', 'Ing10', 'Nbrperemption', 'Coffe_prime', 'DFabrication', 'ART_Calibre', 'ART_Grammage', 'ART_Plomb', 'ART_Caract', 'ART_ECommerce', 'ART_EMode', 'ART_EPrixTTC', 'ART_ESync', 'ART_EMemePrix', 'ART_DernPrixHT', 'ART_DernRemsie', 'ART_DatePeremption', 'ART_SoldeDD', 'ART_SoldeDF', 'ART_SoldeP', 'ART_DF', 'ART_DLC', 'ART_HPV', 'ART_HDM', 'ART_HPS', 'ART_ECDesig', 'ART_EDDS', 'ART_EPSolde', 'ART_EEtatS', 'ART_ETSolde', 'ART_Charge', 'ART_Etat', 'is_Edition', 'is_pris_encharge', 'exportM', 'DDmM', 'photo_Path1', 'photo_Path2', 'photo_Path3', 'photo_Path4', 'ART_ECDescrip', 'ART_EType', 'ART_Ligne', 'ART_Colonne', 'ART_Combinaison', 'ART_CodeGroupe', 'ART_Sexe', 'ART_Collection', 'ART_Saison', 'is_Pack', 'ART_CodePack', 'type_operation'];

    public function ArticleMVT()
    {
        return "Set Language French  SET DATEFORMAT 'ymd'  SELECT dbo.article.ddm,
        dbo.article.ART_Code, dbo.article.ART_CodeBar , dbo.article.Type_Produit,
                (select top 1 Fils_CodeBar
                 from article_code_bar
                 where dbo.article.ART_CodeBar=article_code_bar.Parent_CodeBar
                 and article_code_bar.Parent_CodeBar
                 not like article_code_bar.Fils_CodeBar) as Fils_CodeBar,
                          dbo.article.ART_Designation,
                          ISNULL(dbo.article.ART_PrixUnitaireHT, 0) AS ART_PrixUnitaireHT,
                          dbo.article.ART_TVA,
                          ISNULL(dbo.article.ART_QteStock, 0) AS ART_QteStock,
                          isnull(dbo.article.ART_PrixGros1,0) AS prixGros1,
                          isnull(dbo.article.ART_PrixGros2,0) AS prixGros2,
                          isnull(dbo.article.ART_PrixGros3,0) AS prixGros3, 
                    dbo.article.photo_Path,
                          ISNULL(dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC, 0) AS ART_PrixPublique,
                          ISNULL(dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC, 0) AS pvttc,
                          dbo.marque.MAR_Designation,
                          dbo.article.PrixSolde, 
                          ISNULL(dbo.article.TauxSolde, 0) AS TauxSolde,
                          dbo.famille.FAM_Lib,
                          dbo.StationArticle.SART_Qte, dbo.StationArticle.SART_CodeSatation, dbo.famille.FAM_Code,
                          dbo.Unite_article.UNITE_ARTICLE_QtePiece,
                          dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC,
                          dbo.Unite_article.UNITE_ARTICLE_CodeUnite,
                          Art_MaxTRemise
                FROM dbo.article
                      JOIN dbo.StationArticle 
                 ON dbo.article.ART_Code = dbo.StationArticle.SART_CodeArt 
                 LEFT OUTER JOIN dbo.famille 
                 ON dbo.article.ART_Famille = dbo.famille.FAM_Code
                 LEFT OUTER JOIN dbo.marque 
                 ON dbo.article.ART_Marque = dbo.marque.MAR_Code
                 LEFT OUTER JOIN dbo.Unite_article 
                 ON dbo.article.ART_Code = dbo.Unite_article.UNITE_ARTICLE_CodeArt
                   WHERE (ISNULL(dbo.article.is_bloquer, N'False') <> 'True')   ";
    }

    public function ArticleSansMvt()
    {
        return "SELECT        dbo.couleur.COU_Designation, dbo.Taille.TAI_Taille, dbo.article.ART_CodeGroupe, dbo.article.ddm, dbo.article.Type_Produit, dbo.article.ART_Code, dbo.article.ART_CodeBar,
                             (SELECT        TOP (1) Fils_CodeBar
                               FROM            dbo.article_code_bar
                               WHERE        (dbo.article.ART_CodeBar = Parent_CodeBar) AND (Parent_CodeBar NOT LIKE Fils_CodeBar)) AS Fils_CodeBar, dbo.article.ART_Designation, ISNULL(dbo.article.ART_PrixUnitaireHT, 0) AS ART_PrixUnitaireHT, 
                         dbo.article.ART_TVA, ISNULL(dbo.article.ART_QteStock, 0) AS ART_QteStock, ISNULL(dbo.article.ART_PrixGros1, 0) AS prixGros1, ISNULL(dbo.article.ART_PrixGros2, 0) AS prixGros2, ISNULL(dbo.article.ART_PrixGros3, 0) 
                         AS prixGros3, dbo.article.photo_Path, ISNULL(dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC, 0) AS ART_PrixPublique, ISNULL(dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC, 0) AS pvttc, dbo.marque.MAR_Designation, 
                         dbo.article.PrixSolde, ISNULL(dbo.article.TauxSolde, 0) AS TauxSolde, dbo.famille.FAM_Lib, dbo.famille.FAM_Code, dbo.Unite_article.UNITE_ARTICLE_QtePiece, dbo.Unite_article.UNITE_ARTICLE_CodeUnite, 
                         dbo.article.Art_MaxTRemise, dbo.Unite_article.UNITE_ARTICLE_CodeArt, dbo.marque.MAR_Code
FROM            dbo.article LEFT OUTER JOIN
                         dbo.Taille ON dbo.article.ART_TAILLE = dbo.Taille.TAI_Taille LEFT OUTER JOIN
                         dbo.couleur ON dbo.article.ART_Couleur = dbo.couleur.COU_Code LEFT OUTER JOIN
                         dbo.famille ON dbo.article.ART_Famille = dbo.famille.FAM_Code LEFT OUTER JOIN
                         dbo.marque ON dbo.article.ART_Marque = dbo.marque.MAR_Code LEFT OUTER JOIN
                         dbo.Unite_article ON dbo.article.ART_Code = dbo.Unite_article.UNITE_ARTICLE_CodeArt
WHERE        (ISNULL(dbo.article.is_bloquer, N'False') <> 'True') AND (dbo.Unite_article.UNITE_ARTICLE_IsUnitaire = 'True') AND (LOWER(dbo.article.Type_Produit) <> 'patrimoine')";
    }

    public function getPatrimoine(Request $request, $connection)
    {
        $patrimoine = "Set Language French  SET DATEFORMAT 'ymd' SELECT dbo.article.ddm, dbo.article.ART_Code, dbo.article.ART_CodeBar, dbo.article.Type_Produit,
                             (SELECT        TOP (1) Fils_CodeBar
                               FROM            dbo.article_code_bar
                               WHERE        (dbo.article.ART_CodeBar = Parent_CodeBar) AND (Parent_CodeBar NOT LIKE Fils_CodeBar)) AS Fils_CodeBar, dbo.article.ART_Designation, ISNULL(dbo.article.ART_PrixUnitaireHT, 0) 
                         AS ART_PrixUnitaireHT, dbo.article.ART_TVA, ISNULL(dbo.article.ART_QteStock, 0) AS ART_QteStock, ISNULL(dbo.article.ART_PrixGros1, 0) AS prixGros1, ISNULL(dbo.article.ART_PrixGros2, 0) AS prixGros2, 
                         ISNULL(dbo.article.ART_PrixGros3, 0) AS prixGros3, dbo.article.photo_Path, ISNULL(dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC, 0) AS ART_PrixPublique, 
                         ISNULL(dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC, 0) AS pvttc, dbo.marque.MAR_Designation, dbo.article.PrixSolde, ISNULL(dbo.article.TauxSolde, 0) AS TauxSolde, dbo.famille.FAM_Lib, 
                         dbo.famille.FAM_Code, dbo.Unite_article.UNITE_ARTICLE_QtePiece, dbo.Unite_article.UNITE_ARTICLE_CodeUnite, dbo.article.Art_MaxTRemise,dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC
FROM            dbo.article LEFT OUTER JOIN
                         dbo.famille ON dbo.article.ART_Famille = dbo.famille.FAM_Code LEFT OUTER JOIN
                         dbo.marque ON dbo.article.ART_Marque = dbo.marque.MAR_Code LEFT OUTER JOIN
                         dbo.Unite_article ON dbo.article.ART_Code = dbo.Unite_article.UNITE_ARTICLE_CodeArt
WHERE        (ISNULL(dbo.article.is_bloquer, N'False') <> 'True') AND (LOWER(dbo.article.Type_Produit) = 'patrimoine')";
        /* if($ddm)
         {
             $patrimoine .= "AND dbo.article.ddm >= '".$ddm."'";
         }*/
        $data = $request->json()->all();
        $result2 = $connection->select($patrimoine);
        return $result2;
    }

    public function articleStation(Request $request, $ddm): string
    {
        $requette = "Set Language French  SET DATEFORMAT 'ymd'  SELECT dbo.article.ddm,
        dbo.article.ART_Code, dbo.article.ART_CodeBar , dbo.article.Type_Produit,
                (select top 1 Fils_CodeBar
                 from article_code_bar
                 where dbo.article.ART_CodeBar = article_code_bar.Parent_CodeBar
                 and article_code_bar.Parent_CodeBar
                 not like article_code_bar.Fils_CodeBar) as Fils_CodeBar,
                          dbo.article.ART_Designation,
                          ISNULL(dbo.article.ART_PrixUnitaireHT, 0) AS ART_PrixUnitaireHT,
                          dbo.article.ART_TVA,
                          ISNULL(dbo.article.ART_QteStock, 0) AS ART_QteStock,
                          isnull(dbo.article.ART_PrixGros1,0) AS prixGros1,
                          isnull(dbo.article.ART_PrixGros2,0) AS prixGros2,
                          isnull(dbo.article.ART_PrixGros3,0) AS prixGros3, 
                          dbo.article.photo_Path,
                          ISNULL(dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC, 0) AS ART_PrixPublique,
                          ISNULL(dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC, 0) AS pvttc,
                          dbo.marque.MAR_Designation,
                          dbo.article.PrixSolde, 
                          ISNULL(dbo.article.TauxSolde, 0) AS TauxSolde,
                          dbo.famille.FAM_Lib,
                          dbo.famille.FAM_Code,
                          dbo.Unite_article.UNITE_ARTICLE_QtePiece,
                          dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC,
                          dbo.Unite_article.UNITE_ARTICLE_CodeUnite,
                          AS_PrixVeTTC
                          
                 FROM dbo.article
                 JOIN ArticleStation on ArticleStation.ART_Code = article.ART_Code
                 LEFT OUTER JOIN dbo.famille 
                 ON dbo.article.ART_Famille = dbo.famille.FAM_Code
                 LEFT OUTER JOIN dbo.marque 
                 ON dbo.article.ART_Marque = dbo.marque.MAR_Code
                 LEFT OUTER JOIN dbo.Unite_article 
                 ON dbo.article.ART_Code = dbo.Unite_article.UNITE_ARTICLE_CodeArt
                 WHERE (ISNULL(dbo.article.is_bloquer, N'False') <> 'True')
                
                ";
        /*if($ddm)
        {
            $requette .="AND dbo.article.ddm >= '".$ddm."'";
        }*/

        return $requette;
    }

    public function PrixSiteOrPrixPublique($articles, $connection, $station)
    {
        $Type_PrixSite = $connection->table('station')->where('STAT_Code', '=', $station)->pluck('Type_PrixSite')->first();

        foreach ($articles as $article) {
            if (strtolower($Type_PrixSite) === 'prix site') {
                $article->ART_PrixPublique = $article->AS_PrixVeTTC;
                $article->pvttc = $article->AS_PrixVeTTC;

            }
            unset($article->AS_PrixVeTTC);

        }

        return $articles;

    }

}
