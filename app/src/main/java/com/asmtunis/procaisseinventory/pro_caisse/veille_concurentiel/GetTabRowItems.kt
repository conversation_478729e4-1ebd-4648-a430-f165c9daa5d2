package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel

import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain.AutreVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.screens.AutreScreen
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.screens.NewProductScreen
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine.PrixVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.screens.PrixScreen
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.screens.PromotionScreen
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.TabRowItem
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.CameraViewModel
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels





    @Composable
    fun getTabRowItems(
        navigate: (route: Any) -> Unit,
        popBackStack: () -> Unit,
        mainViewModel: MainViewModel,
        getProCaisseDataViewModel: GetProCaisseDataViewModel,
        cameraViewModel: CameraViewModel,
        proCaisseViewModels: ProCaisseViewModels,
        isConnected: Boolean,
        selectedBaseconfig: BaseConfig,
        newProductList: List<NewProductVCWithImages>,
        promotionList: List<PromoVCWithImages>,
        autreList: List<AutreVCWithImages>,
        prixList: List<PrixVCWithImages>,
        autreListState: LazyListState,
        newProductListState: LazyListState,
        prixListState: LazyListState,
        promotionListState: LazyListState,
    ): List<TabRowItem>  {
        val vcViewModel = proCaisseViewModels.veilleConcurentielViewModel
        val newProdViewModel = proCaisseViewModels.newProdViewModel
        val promotionViewModel = proCaisseViewModels.promotionViewModel
        val prixViewModel = proCaisseViewModels.prixViewModel
        val autreViewModel = proCaisseViewModels.autreViewModel


        return listOf(
            TabRowItem(
                title = stringResource(id = R.string.new_product_size, newProductList.size.toString()),
                screen = {
                    if(getProCaisseDataViewModel.newProductState.loading)
                        LottieAnim(lotti = R.raw.loading, size = 250.dp)
                    else
                        NewProductScreen(
                            listState = newProductListState,
                            isConnected = isConnected,
                            selectedBaseconfig = selectedBaseconfig,
                            getProCaisseDataViewModel = getProCaisseDataViewModel,
                            cameraViewModel = cameraViewModel,
                            navigate = { navigate(it) },
                            popBackStack = { popBackStack() },
                            vcViewModel = vcViewModel,
                            newProdViewModel = newProdViewModel,
                            mainViewModel = mainViewModel
                        )
                }
                // icon = Icons.Rounded.Place,
            ),
            TabRowItem(
                title = stringResource(id = R.string.promotion_size, promotionList.size),
                screen = {
                    if(getProCaisseDataViewModel.promoState.loading)
                        LottieAnim(lotti = R.raw.loading, size = 250.dp)
                    else
                        PromotionScreen(
                            navigate = { navigate(it) },
                            popBackStack = { popBackStack() },
                            listState = promotionListState,
                            isConnected = isConnected,
                            selectedBaseconfig = selectedBaseconfig,
                            getProCaisseDataViewModel = getProCaisseDataViewModel,
                            vcViewModel = vcViewModel,
                            promotionViewModel = promotionViewModel,
                            cameraViewModel = cameraViewModel,
                            mainViewModel = mainViewModel

                        )
                }
                //  icon = Icons.Rounded.Search,
            ),
            TabRowItem(
                title = stringResource(id = R.string.price_size, prixList.size),
                screen = {
                    if(getProCaisseDataViewModel.prixState.loading)
                        LottieAnim(lotti = R.raw.loading, size = 250.dp)
                    else
                        PrixScreen(
                            navigate = { navigate(it) },
                            popBackStack = { popBackStack() },
                            listState = prixListState,
                            isConnected = isConnected,
                            selectedBaseconfig = selectedBaseconfig,
                            getProCaisseDataViewModel = getProCaisseDataViewModel,
                            vcViewModel = vcViewModel,
                            cameraViewModel= cameraViewModel,
                            mainViewModel = mainViewModel,
                            prixViewModel = prixViewModel
                        )
                }
                //  icon = Icons.Rounded.Star,
            ),
            TabRowItem(
                title = stringResource(id = R.string.other_size, autreList.size),
                screen = {
                    if(getProCaisseDataViewModel.autreState.loading)
                        LottieAnim(lotti = R.raw.loading, size = 250.dp)
                    else
                        AutreScreen (
                            navigate = { navigate(it) },
                            popBackStack = { popBackStack() },
                            listState = autreListState,
                            isConnected = isConnected,
                            selectedBaseconfig = selectedBaseconfig,
                            getProCaisseDataViewModel = getProCaisseDataViewModel,
                            cameraViewModel= cameraViewModel,
                            vcViewModel = vcViewModel,
                            autreViewModel = autreViewModel,
                            mainViewModel = mainViewModel
                        )
                }
                //  icon = Icons.Rounded.Star,
            )
        )
    }



