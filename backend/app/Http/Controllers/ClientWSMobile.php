<?php

namespace App\Http\Controllers;

use App\Enums\UserType;
use App\Helpers\AppHelper;
use App\Helpers\DatabaseConnection;
use App\Helpers\Enum;
use App\Models\Client;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


class ClientWSMobile extends Controller
{
    public function getClientByCode(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('client')->where('CLI_Code', $data["object"])->first());
    }


    /*fonction get data from database*/

    public function getClient(Request $request)
    {
        if (!!Request::createFromGlobals()->hasHeader('user')) {
            $idUser = $request->header('user');
            $data = $request->json()->all();
            $CLI_DDm = $request->input('CLI_DDm');
            $connection = DatabaseConnection::setConnection($data);
            $user = $connection->table('Utilisateur')->where('Code_Ut', $idUser)->first();

            if ($user != null) {
                $allClients = $connection->table('client')
                    ->join('View_SoldeClient', 'CodeClient', '=', 'CLI_Code')
                    ->where(strtolower('Clt_Info1'), '!=', "patrimoine")
                    ->where(strtolower('Clt_Info1'), '!=', "autonome");

                $query = clone $allClients;

                if ($CLI_DDm) {
                    $query->where('ClI_DDm', '>=', $CLI_DDm);
                }

                // Seller or patented
                if (strtolower($user->Type_user) == UserType::seller || strtolower($user->Type_user) == UserType::patented) {
                    if (Client::authorizationsAllClients($idUser, $connection)) {
                        $query = clone $allClients;
                        if ($CLI_DDm) {
                            $query->where('ClI_DDm', '>=', $CLI_DDm);
                        }
                        $query->orWhereNull("Clt_Info1");

                    } else {
                        $allClientsByStations = $connection->table('client')
                            ->join('View_SoldeClient', 'CodeClient', '=', 'CLI_Code')
                            ->where('CLI_Station', '=', $user->Station);

                        $query = clone $allClientsByStations;
                        if ($CLI_DDm) {
                            $query->where('CLI_DDm', '>=', $CLI_DDm);
                        }
                        $query->orWhere(strtolower('CLI_Type'), '=', 'Passager');

                        $authorizationsAllClientsByStation = Client::authorizationsAllClientsByStation($idUser, $connection);
                        $authorizationsAllClientsByZone = Client::authorizationsAllClientsByZone($idUser, $connection);

                        if ($authorizationsAllClientsByStation) {
                            $query = clone $allClientsByStations;
                            if ($CLI_DDm) {
                                $query->where('CLI_DDm', '>=', $CLI_DDm);
                            }
                            $query->orWhere(strtolower('CLI_Type'), '=', 'Passager');

                        } else if ($authorizationsAllClientsByZone) {
                            $zones = Client::getZonesUser($idUser, $connection);
                            $query = Client::getClientsByZones($zones, $connection);
                            if ($CLI_DDm) {
                                $query->where('ClI_DDm', '>=', $CLI_DDm);
                            }
                        } else if ($authorizationsAllClientsByZone && $authorizationsAllClientsByStation) {
                            $clientByStation = clone $allClientsByStations;
                            if ($CLI_DDm) {
                                $clientByStation->where('ClI_DDm', '>=', $CLI_DDm);
                            }
                            $clientByStation->orWhere(strtolower('CLI_Type'), '=', 'Passager');
                            $zones = Client::getZonesUser($idUser, $connection);
                            $clientsByZones = Client::getClientsByZones($zones, $connection);
                            if ($CLI_DDm) {
                                $clientsByZones->where('ClI_DDm', '>=', $CLI_DDm);
                            }

                            $query = [$clientByStation + $clientsByZones];
                        }
                    }


                }

            }

            //autonomous
            if (strtolower($user->Type_user) == UserType::autonomous) {
                $query = $connection->table('client')
                    ->join('View_SoldeClient', 'CodeClient', '=', 'CLI_Code')
                    ->where(strtolower('Clt_Info1'), '=', UserType::autonomous)
                    ->where('CLI_User', '=', $idUser);

                if ($CLI_DDm) {
                    $query->where('ClI_DDm', '>=', $CLI_DDm);
                }

            }

            //heritageOperator
            if (strtolower($user->Type_user) == UserType::heritageOperator) {

                $query = $connection->table('client')
                    ->join('View_SoldeClient', 'CodeClient', '=', 'CLI_Code')
                    ->where(strtolower('Clt_Info1'), '=', "patrimoine");

                if ($CLI_DDm) {
                    $query->where('ClI_DDm', '>=', $CLI_DDm);
                }

            }

            //commercial
            if (strtolower($user->Type_user) == UserType::commercial) {

                if (Client::authorizationsAllClients($idUser, $connection)) {
                    $query = clone $allClients;

                    if ($CLI_DDm) {
                        $query->where('ClI_DDm', '>=', $CLI_DDm);
                    }
                    $query->orWhereNull("Clt_Info1");
                } else {
                    $zones = Client::getZonesUser($idUser, $connection);
                    $query = Client::getClientsByZones($zones, $connection);
                    if ($CLI_DDm) {
                        $query->where('ClI_DDm', '>=', $CLI_DDm);
                    }

                }
            }
            //salesRepresentative
            if (strtolower($user->Type_user) == UserType::salesRepresentative) {

                if (Client::authorizationsAllClients($idUser, $connection)) {
                    $query = clone $allClients;

                    if ($CLI_DDm) {
                        $query->where('ClI_DDm', '>', $CLI_DDm);
                    }
                    $query->orWhereNull("Clt_Info1");
                } else {
                    $user_chef_zone = $connection->table('Utilisateur')
                        ->where('Code_Ut', '=', $idUser)->pluck('CHEF_ZONE')->first();
                    $query = $connection->table('client')
                        ->join('View_SoldeClient', 'CodeClient', '=', 'CLI_Code')
                        ->where('Clt_Info3', '=', $user_chef_zone);
                    if ($CLI_DDm) {
                        $query->where('ClI_DDm', '>', $CLI_DDm);
                    }
                }

            }
            //CltEquivalent
            if ($request->has('CltEquivalent') && $request->input('CltEquivalent') !== null) {
                $query->orWhere("CLI_Code", $request->input('CltEquivalent'))->get();
            }
            return response()->json(collect($query->groupBy(Client::groupByColumns())->get(Client::fillables())));
        }

    }


    public function getClientByX(Request $request)
    {
        return response()->json(DB::table('client')->where($request->field, $request->value)->get());
    }


    public function addBatchClient(Request $request)
    {
        $items = $request->json()->all();

        if (!empty($items)) {
            $connection = DatabaseConnection::setConnection($items);
            $result = [];
            foreach ($items["object"] as &$data) {
                $connection->beginTransaction();
                try {
                    if (!$this->isExistCLI($data['CLI_Code'], $data['CLI_Code_M'] ?? null, $connection)) {
                        $data['CLI_Code'] = (new PrefixWSMobile)->getPrefix($connection, "Client",
                            $data['exercice'], $data['CLI_User']);
                        unset($data['exercice']);
                        unset($data['Solde']);
                        $item = $data;
                        $item['CLI_Exo_Valable'] = 0;
                        $item['CLI_Date_Cre'] = AppHelper::setDateFormat($data['CLI_Date_Cre']);
                        if (isset($data['datePA'])) {
                            $item['datePA'] = AppHelper::setDateFormat($data['datePA']);
                        } else {
                            $item['datePA'] = Carbon::now();
                        }
                        $item['Clt_Latitude'] = AppHelper::getNumber($data['Clt_Latitude']);
                        $item['Clt_Longitude'] = AppHelper::getNumber($data['Clt_Longitude']);
                        $connection->table('client')->insert($item);
                    } else {
                        unset($data['exercice']);
                        unset($data['Solde']);
                        $item = $data;
                        $query = $connection->table('client');
                        if (isset($data['CLI_Code_M']) && !!$data['CLI_Code_M']) {
                            $query->where("CLI_Code_M", $data["CLI_Code_M"]);
                        } else {
                            $query->where("CLI_Code", $data["CLI_Code"]);
                        }
                        $query->update([
                            "CLI_DDm" => $data['CLI_DDm'],
                            "CLI_Exeno" => $data["CLI_Exeno"] ?? null,
                            "CLI_DC" => $data["CLI_DC"] ?? null,
                            "CLI_Date_Cre" => $data['CLI_Date_Cre'],
                            "datePA" => $data["datePA"] ?? null,
                            "CLI_Exo_Valable" => $data["CLI_Exo_Valable"] ?? null,
                            "CLI_export" => $data["CLI_export"] ?? null,
                            "CLI_Fodec" => $data["CLI_Fodec"] ?? null,
                            "CLI_Forfetaire" => $data["CLI_Forfetaire"] ?? null,
                            "CLI_Industrielle" => $data["CLI_Industrielle"] ?? null,
                            "CLI_NomPren" => $data["CLI_NomPren"] ?? null,
                            "CLI_Solde" => $data["CLI_Solde"] ?? null,
                            "CLI_TauxRemGlob" => $data["CLI_TauxRemGlob"],
                            "CLI_isCredit" => $data['CLI_isCredit'],
                            "CLI_Timbre" => $data["CLI_Timbre"],
                            "CLI_Type" => $data["CLI_Type"] ?? null,
                            "CLI_Exonoration" => $data["CLI_Exonoration"] ?? null,
                            'Clt_Latitude' => AppHelper::getNumber($data['Clt_Latitude']),
                            'Clt_Longitude' => AppHelper::getNumber($data['Clt_Longitude']),
                            'CLI_Adresse' => $data["CLI_Adresse"] ?? null,
                            'CLI_Tel1' => $data["CLI_Tel1"] ?? null,
                            'CLI_Tel2' => $data["CLI_Tel2"] ?? null,
                            "Clt_Info6" => $data["Clt_Info6"] ?? null,
                            "Clt_Info7" => $data["Clt_Info7"] ?? null,
                            "Clt_Info8" => $data["Clt_Info8"] ?? null,
                            "Clt_Info9" => $data["Clt_Info9"] ?? null,
                            "Clt_Info10" => $data["Clt_Info10"] ?? null,
                        ]);
                    }
                    $result[] = $item;
                    $connection->commit();
                } catch (\Exception $exception) {
                    $connection->rollBack();

                }

            }
        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }


    public function addClient(Request $request)
    {
        $data = $request->json()->all();

        if (!empty($data)) {
            $connection = DatabaseConnection::setConnection($data);

            $c = $connection->table('client')->insert($data["object"]);


            if ($c) {
                return response()->json($data['object']);
            } else {
                return response()->json(null);
            }

        } else {
            return response()->json(null);
        }
    }


    public function updateClient(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        $connection->table('client')->where('CLI_Code', $data["CLI_Code"])->update($data);
        return response()->json(true);

    }


    public function deleteClient(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $connection->table('client')->where('CLI_Code', $data["object"])->delete();
        return ("Data Deleted");
    }

    public function isExistCLI($cliId, $id_mobile, $connection)
    {
        $query = $connection->table("client")
            ->where("CLI_Code", $cliId);
        if (!!$id_mobile) {
            $query->orWhere("CLI_Code_M", $id_mobile);
        }

        return $query->first();
    }

    public function AffectCodeBareBatiment(Request $request)
    {

        $items = $request->json()->all();
        $connection = DatabaseConnection::setConnection($items);
        $object = $items['object'];
        $result = array();
        $connection->beginTransaction();
        try {

            $verifExisteCodeBar = $connection->table('client')
                ->where('Clt_ImoCB', $object['Clt_ImoCB'])
                ->where('CLI_Code', '!=', $object['CLI_Code'])
                ->first();

            if (!empty($verifExisteCodeBar)) {
                $result = $this->setGlobalResult("CLI_Code", $verifExisteCodeBar->CLI_Code,
                    "Code bare '".$object['Clt_ImoCB']."' est deja affecte", Enum::Error_code_barre_batiment_deja_affecte);

            } else {
                $affectCodeBarre = $connection->table('client')->where('CLI_Code', $object['CLI_Code'])->update([
                    'Clt_ImoCB' => $object['Clt_ImoCB']
                ]);


                $result = $this->setGlobalResult("CLI_Code", $object["CLI_Code"],
                    "UPDATED", Enum::Success_Code
                );

                $connection->commit();
            }
        } catch (\Exception $e) {
            $connection->rollBack();

        }

        return response()->json($result);
    }

}
