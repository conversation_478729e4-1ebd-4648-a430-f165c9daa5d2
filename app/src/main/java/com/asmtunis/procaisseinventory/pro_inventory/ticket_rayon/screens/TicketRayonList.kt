package com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.screens

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.domaine.TicketRayon

@Composable
fun TicketRayonList(
    ticketRayonViewModel: TicketRayonViewModel,
    articleMapByBarCode: Map<String, Article>,
    ticketRayonList: List<TicketRayon>,
    haveTicketRayonAuthorisation: Boolean,
    listState: LazyListState
) {


    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        state = listState
    ) {
        items(
            //   items= filteredList.keys.toList()
            count = ticketRayonList.size,
            key = {
                ticketRayonList[it].aRTCode
            }

        ) { index ->



            TicketRayonItem(
                article = articleMapByBarCode[ticketRayonList[index].aRTCode]?: Article(aRTCodeBar = ticketRayonList[index].aRTCode, aRTCode = ticketRayonList[index].aRTCode, designation = ticketRayonList[index].aRTDesignation),
                ticketRayon = ticketRayonList[index],
                borderColor = if(!ticketRayonList[index].isSync) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.outline,
                setBottomSheetVisibility = {
                    ticketRayonViewModel.bottomSheetVisibility(it)
                },
                setSelectedTicketRayon = {
                    ticketRayonViewModel.onSelectedTicketRayonChange(it)
                },
                haveTicketRayonAuthorisation = haveTicketRayonAuthorisation,
            )
        }

    }
}