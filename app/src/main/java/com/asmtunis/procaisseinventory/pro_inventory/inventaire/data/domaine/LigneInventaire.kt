package com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


//@Entity(primaryKeys = ["LG_INV_Code_Inv", "LG_INV_Code_Article", "LG_INV_Code"])
@Entity(tableName = ProInventoryConstants.LIGNE_INVENTAIRE_TABLE)
@Serializable
data class LigneInventaire  (
    @PrimaryKey(autoGenerate = true)
    @Transient
    val id: Long = 0,
    
    @ColumnInfo(name = "LG_INV_Code_Inv")
    @SerialName("LG_INV_Code_Inv")
    var lGINVCodeInv: String = "",

    @ColumnInfo(name = "LG_INV_Code_Article")
    @SerialName("LG_INV_Code_Article")
    var lGINVCodeArticle: String = "",

    @ColumnInfo(name = "LG_INV_Code")
    @SerialName("LG_INV_Code")
    var lGINVCode: String = "",

    @SerialName("LG_INV_Qte_Stock")
    @ColumnInfo(name = "LG_INV_Qte_Stock")
    var lGINVQteStock: String? = null,

    @ColumnInfo(name = "LG_INV_Qte_Reel")
    @SerialName("LG_INV_Qte_Reel")
    var lGINVQteReel: String? = null,

    @ColumnInfo(name = "LG_INV_EtatLigne")
    @SerialName("LG_INV_EtatLigne")
    var lGINVEtatLigne: String? = null,

    @ColumnInfo(name = "LG_INV_ValidationLigne")
    @SerialName("LG_INV_ValidationLigne")
    var lGINVValidationLigne: String? = null,

    @ColumnInfo(name = "LG_INV_Station")
    @SerialName("LG_INV_Station")
    var lGINVStation: String? = null,

    @ColumnInfo(name = "LG_INV_Prix_CMP")
    @SerialName("LG_INV_Prix_CMP")
    var lGINVPrixCMP: String? = null,



    ): BaseModel()