package com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.cheque.text_validation

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateStringNotEmpty
import com.asmtunis.procaisseinventory.data.banques.domaine.Banque
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch




class ChequeTextValidationViewModel(
    private val validateIsNotEmptyString: ValidateStringNotEmpty = ValidateStringNotEmpty(),
) : ViewModel() {




    var stateAddCheque by mutableStateOf(AddChequeFormState())
    private val validationAddChequeEventChannel = Channel<ValidationAddChequeEvent>()
    val validationAddChequeEvents = validationAddChequeEventChannel.receiveAsFlow()

    fun onAddChequeEvent(event: AddChequeFormEvent) {
        when (event) {
         
           
                

            is AddChequeFormEvent.Bank ->  stateAddCheque = stateAddCheque.copy(bank = event.bank)
            is AddChequeFormEvent.EcheanceChanged -> stateAddCheque = stateAddCheque.copy(echeance = event.echeance)
            is AddChequeFormEvent.Montant -> stateAddCheque = stateAddCheque.copy(montant = event.montant)
            is AddChequeFormEvent.NumeroChequeChanged -> stateAddCheque = stateAddCheque.copy(numeroCheque = event.numeroCheque)
            AddChequeFormEvent.SubmitAddCheque -> submitAddChequeData()
        }
    }

    private fun submitAddChequeData() {
        val bankResult = validateIsNotEmptyString.execute(stateAddCheque.bank.bANCode)

        val echeanceResult = validateIsNotEmptyString.execute(stateAddCheque.echeance)
        val montantResult = validateIsNotEmptyString.execute(stateAddCheque.montant)


        val numeroChequeResult =
            validateIsNotEmptyString.execute(stateAddCheque.numeroCheque)



        val hasError = listOf(
            bankResult,
            echeanceResult,
            montantResult,
            numeroChequeResult,
        ).any { !it.successful }

        if (hasError) {
            stateAddCheque = stateAddCheque.copy(
                bankError = bankResult.errorMessage,
                echeanceError = echeanceResult.errorMessage,
                montantError = montantResult.errorMessage,
                numeroChequeError = numeroChequeResult.errorMessage,
                )
            return
        }
        viewModelScope.launch {
            validationAddChequeEventChannel.send(ValidationAddChequeEvent.AddCheque(stateAddCheque))
        }
    }

    sealed class ValidationAddChequeEvent {
        data class AddCheque(val addCheque: AddChequeFormState) : ValidationAddChequeEvent()
    }

    fun resetVariable(){
        stateAddCheque = stateAddCheque.copy(
             numeroCheque = "",
         numeroChequeError = null,

         echeance = "",
         echeanceError = null,

         bank  = Banque(),
         bankError = null,

         montant = "",
         montantError = null,
            )

    }

}