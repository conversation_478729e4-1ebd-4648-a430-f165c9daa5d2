package com.asmtunis.honeywellbarcodereader

import android.app.Activity
import com.asmtunis.honeywellbarcodereader.listener.BarcodeListener
import com.asmtunis.honeywellbarcodereader.readers.EdaReader
import com.asmtunis.honeywellbarcodereader.readers.PM80Reader


fun barCodeReaderScanner(
    activity: Activity,
    barCodeReader: BarCodeReaderManager?,
    ifSuccess: (String?) -> Unit,
    ifFail: (String?) -> Unit) {



    val edaReader = EdaReader(activity, object : BarcodeListener {
        override fun onSuccess(event: String?) {
            ifSuccess(event)

        }

        override fun onFail(event: String?) {
            ifFail(event)
        }
    })

    val pointMobileReader = PM80Reader(activity, object : BarcodeListener {
        override fun onSuccess(event: String?) {
            ifSuccess(event)

        }

        override fun onFail(event: String?) {
            ifFail(event)


        }
    })

    barCodeReader?.addReader(edaReader)?.addReader(pointMobileReader)
    barCodeReader?.startListener()
}