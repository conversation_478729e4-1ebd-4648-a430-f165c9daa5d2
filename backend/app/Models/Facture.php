<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property string $FACT_Num
 * @property string $FACT_Exerc
 * @property string $FACT_Date
 * @property string $FACT_CodeClient
 * @property string $FACT_NomPrenomCli
 * @property string $FACT_AdresseCli
 * @property string $FACT_MatFisc
 * @property string $FACT_ExoNum
 * @property string $FACT_ExoVal
 * @property float $FACT_MntHT
 * @property float $FACT_TauxRemise
 * @property float $FACT_MntRemise
 * @property float $FACT_MntNetHT
 * @property float $FACT_MntTva
 * @property float $FACT_Timbre
 * @property float $FACT_MntTTC
 * @property string $FACT_Etat
 * @property string $FACT_Type
 * @property boolean $FACT_Regler
 * @property boolean $FACT_Exonoration
 * @property string $FACT_TypeReg
 * @property string $FACT_ValiditeTraite
 * @property float $FACT_RetSource
 * @property string $FACT_NumBC
 * @property string $FACT_user
 * @property string $FACT_station
 * @property boolean $FACT_export
 * @property string $FACT_DDm
 * @property string $FACT_FactFournisseur
 * @property float $FACT_Total_HTVA
 * @property float $FACT_Total_HDC
 * @property string $FACT_Comptabiliser
 * @property string $FACT_Reg
 */
class Facture extends Model
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'facture';



    /**
     * @var array
     */
    protected $fillable = ['FACT_Date', 'FACT_CodeClient', 'FACT_NomPrenomCli', 'FACT_AdresseCli', 'FACT_MatFisc', 'FACT_ExoNum', 'FACT_ExoVal', 'FACT_MntHT', 'FACT_TauxRemise', 'FACT_MntRemise', 'FACT_MntNetHT', 'FACT_MntTva', 'FACT_Timbre', 'FACT_MntTTC', 'FACT_Etat', 'FACT_Type', 'FACT_Regler', 'FACT_Exonoration', 'FACT_TypeReg', 'FACT_ValiditeTraite', 'FACT_RetSource', 'FACT_NumBC', 'FACT_user', 'FACT_station', 'FACT_export', 'FACT_DDm', 'FACT_FactFournisseur', 'FACT_Total_HTVA', 'FACT_Total_HDC', 'FACT_Comptabiliser', 'FACT_Reg'];
}
