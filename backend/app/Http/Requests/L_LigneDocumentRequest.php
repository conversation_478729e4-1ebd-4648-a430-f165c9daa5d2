<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Symfony\Component\HttpFoundation\Response;

class L_LigneDocumentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'object' => 'nullable|array',
            'object.*.idLigneDocumentPere' => 'nullable|string',
            'object.*.numOrdre' => 'nullable|integer',
            'object.*.idArticle' => 'nullable|string',
            'object.*.codeArticle' => 'nullable|string',
            'object.*.libelleArticle' => 'nullable|string',
            'object.*.qte' => 'nullable|numeric',
            'object.*.puht' => 'nullable|numeric',
            'object.*.mntht' => 'nullable|numeric',
            'object.*.mntBrutht' => 'nullable|numeric',
            'object.*.tauxRemise' => 'nullable|numeric',
            'object.*.mntRemise' => 'nullable|numeric',
            'object.*.mntNetht' => 'nullable|numeric',
            'object.*.fodec' => 'nullable|numeric',
            'object.*.dc' => 'nullable|numeric',
            'object.*.assietteTva' => 'nullable|numeric',
            'object.*.mntTva' => 'nullable|numeric',
            'object.*.puttc' => 'nullable|numeric',
            'object.*.mntttc' => 'nullable|numeric',
            'object.*.ancienQte' => 'nullable|numeric',
            'object.*.idDocumentBase' => 'nullable|string',
            'object.*.idLigneDocumentBase' => 'nullable|string',
            'object.*.numeroDeSerie' => 'nullable|string',
            'object.*.note' => 'nullable|string',
            'object.*.etatLigne' => 'nullable|string',
            'object.*.datevalidite' => 'nullable|date',
            'object.*.idUniteVente' => 'nullable|string',
            'object.*.id' => 'nullable|string',

        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY)
        );
    }
}
