<?php

namespace App\Models\DuxInventory;




class P_Article extends BaseModel
{
    protected $connection = 'onthefly';
    public $incrementing = false;
    protected $table = 'P_Article';
    public $timestamps = false;
    protected $guarded = [];

    public function setLibelleMarqueAttribute($value)
    {
        $this->attributes['libellleMarque'] = $value;
    }

    protected $casts = [
        'soumisFodec' => 'boolean',
        'soumisDC' => 'boolean',
        'soumisTva' => 'boolean',
        'j_export' => 'boolean',
        'active' => 'boolean',
        'isStockable' => 'boolean',
        'NumSerie' => 'boolean',
        'joker' => 'boolean',
        'Garantie' => 'boolean',
        'MajAutCMP' => 'boolean',
        'MajAutCr' => 'boolean',
        'MajAutDPA' => 'boolean',
        'Vente_en_Ligne' => 'boolean',
        'MultiTailecouleur' => 'boolean',
        'MajAutConsomMoy' => 'boolean',
        'isArticleBalance' => 'boolean',
        'isGarantieCheck' => 'boolean',
        'isEcommenrce' => 'boolean',
        'E_EtatProduit' => 'boolean',
        'use_variant_couleur' => 'boolean',
        'use_qrcode_qte' => 'boolean',
        'use_qte_souhaite' => 'boolean',
        'groupByArticle' => 'boolean',
        'useunitesecondaire' => 'boolean',
        'MajAutDCR' => 'boolean',
        'isSync' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($model) {
            $model->version = 0;
            $model->margetheorique = $model->margetheorique ? ($model->margetheorique / 100) : null;
            $model->margeB = $model->margeB ? ($model->margeB / 100) : null;
            $model->RemiseMax = $model->RemiseMax ? ($model->RemiseMax / 100) : null;
        });

    }
}
