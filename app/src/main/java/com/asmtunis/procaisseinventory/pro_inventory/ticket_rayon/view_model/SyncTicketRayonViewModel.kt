package com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROINVENTORY_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.sync.SyncEntity
import com.asmtunis.procaisseinventory.core.sync.SyncManager
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryRemote
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.domaine.AddTicketRayonItem
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.domaine.TicketRayon
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject

/**
 * ViewModel for synchronizing TicketRayon (Department Ticket) data
 * Handles sync operations for department-specific tickets and pricing
 */
@HiltViewModel
class SyncTicketRayonViewModel @Inject constructor(
    private val syncManager: SyncManager,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    private val proInventoryRemote: ProInventoryRemote,
    private val proInventoryLocalDb: ProInventoryLocalDb,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val listenNetwork: ListenNetwork,
) : ViewModel() {

    // Sync state from SyncManager
    val syncState = syncManager.syncState

    // Auto-sync configuration
    private var autoSyncState by mutableStateOf(false)
    private val autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(
        key = PROINVENTORY_AUTO_SYNC_AUTHORISATION,
        default = true
    ).distinctUntilChanged()
    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()
    private var connected by mutableStateOf(false)

    // Sync response state
    var responseAddTicketRayonState: RemoteResponseState<List<AddTicketRayonItem>> by mutableStateOf(RemoteResponseState())
        private set

    // Unsynced data
    var ticketRayonNotSync: List<TicketRayon> by mutableStateOf(emptyList())
        private set
    var notSyncTicketRayonObj: String by mutableStateOf("")
        private set

    init {
        observeUnsyncedTicketRayon()
    }

    /**
     * Observe unsynced TicketRayon data and trigger auto-sync when conditions are met
     */
    private fun observeUnsyncedTicketRayon() {
        viewModelScope.launch {
            val ticketRayonNotSyncFlow = proInventoryLocalDb.ticketRayon.notSync().distinctUntilChanged()

            combine(networkFlow, ticketRayonNotSyncFlow, autoSyncFlow) { isConnected, ticketRayonNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                ticketRayonNotSyncList.ifEmpty { emptyList() }
            }.collectLatest { ticketRayonList ->
                if (ticketRayonList.isEmpty()) {
                    ticketRayonNotSync = emptyList()
                    return@collectLatest
                }
                ticketRayonNotSync = ticketRayonList
                if (connected && autoSyncState) {
                    syncTicketRayon()
                }
            }
        }
    }

    /**
     * Synchronize TicketRayon data with the server
     * @param selectedTicketRayon Optional specific TicketRayon to sync, if null syncs all unsynced items
     */
    fun syncTicketRayon(selectedTicketRayon: TicketRayon = TicketRayon()) {
        viewModelScope.launch(dispatcherIO) {
            try {
                responseAddTicketRayonState = RemoteResponseState(data = null, loading = true, error = null)

                // Try SyncManager first
                if (selectedTicketRayon == TicketRayon() && ticketRayonNotSync.isNotEmpty()) {
                    val result = syncManager.syncEntity(SyncEntity.TICKET_RAYON)
                    if (result.isSuccess) {
                        responseAddTicketRayonState = RemoteResponseState(
                            data = emptyList(),
                            loading = false,
                            error = null
                        )
                        return@launch
                    }
                }

                // Fallback to legacy sync method
                syncTicketRayonLegacy(selectedTicketRayon)

            } catch (exception: Exception) {
                responseAddTicketRayonState = RemoteResponseState(
                    data = null,
                    loading = false,
                    error = exception.message ?: "Unknown error"
                )
            }
        }
    }

    /**
     * Legacy sync method for TicketRayon
     */
    private suspend fun syncTicketRayonLegacy(selectedTicketRayon: TicketRayon) {
        val ticketsToSync = if (selectedTicketRayon != TicketRayon()) {
            listOf(selectedTicketRayon)
        } else {
            ticketRayonNotSync
        }

        val baseConfigObj = GenericObject(
            proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                ?.let { Json.decodeFromString(it) } ?: BaseConfig(),
            Json.encodeToJsonElement(ticketsToSync),
        )

        notSyncTicketRayonObj = Json.encodeToString(baseConfigObj)

        proInventoryRemote.ticketRayon.addTicketRayon(notSyncTicketRayonObj).onEach { result ->
            when (result) {
                is DataResult.Success -> {
                    result.data?.let { responses ->
                        for (ticketRayon in responses) {
                            if (ticketRayon.code == 10200) {
                                proInventoryLocalDb.ticketRayon.updateSyncTicketRayon(ticketRayon.aRTCode)
                            }
                        }
                    }
                    responseAddTicketRayonState = RemoteResponseState(
                        data = result.data,
                        loading = false,
                        error = null
                    )
                }

                is DataResult.Loading -> {
                    responseAddTicketRayonState = RemoteResponseState(
                        data = null,
                        loading = true,
                        error = null
                    )
                }

                is DataResult.Error -> {
                    responseAddTicketRayonState = RemoteResponseState(
                        data = null,
                        loading = false,
                        error = result.message,
                        message = selectedTicketRayon.aRTDesignation
                    )
                }
            }
        }.flowOn(dispatcherIO).launchIn(viewModelScope)
    }

    // ========================================
    // SYNCMANAGER INTEGRATION & CONVENIENCE METHODS
    // ========================================

    /**
     * Trigger manual sync for all TicketRayon entities using SyncManager
     */
    fun triggerManualSyncAll() {
        syncTicketRayon()
    }

    /**
     * Check if sync is currently in progress
     */
    val isSyncing: Boolean
        get() = responseAddTicketRayonState.loading

    /**
     * Get the current sync error if any
     */
    val syncError: String?
        get() = responseAddTicketRayonState.error

    /**
     * Get the count of unsynchronized items
     */
    val unsyncedCount: Int
        get() = ticketRayonNotSync.size

    /**
     * Reset all sync states to initial state
     */
    fun resetAllSyncStates() {
        responseAddTicketRayonState = RemoteResponseState()
    }

    /**
     * Get sync status summary for UI display
     */
    val syncStatusSummary: String
        get() = when {
            isSyncing -> "Synchronisation tickets rayon en cours..."
            syncError != null -> "Erreur de synchronisation: $syncError"
            unsyncedCount > 0 -> "$unsyncedCount tickets rayon non synchronisés"
            else -> "Tous les tickets rayon sont synchronisés"
        }

    /**
     * Get detailed sync information
     */
    val detailedSyncInfo: Map<String, Any>
        get() = mapOf(
            "totalItems" to ticketRayonNotSync.size,
            "isLoading" to isSyncing,
            "error" to (syncError ?: ""),
            "lastSyncObject" to notSyncTicketRayonObj,
            "networkConnected" to connected,
            "autoSyncEnabled" to autoSyncState
        )

    /**
     * Get ticket statistics by department
     */
    val ticketStatistics: Map<String, Int>
        get() {
            val ticketsByDepartment = ticketRayonNotSync.groupBy { it.aRTDesignation }
            return ticketsByDepartment.mapValues { it.value.size }
        }

    /**
     * Get tickets grouped by article code
     */
    val ticketsByArticle: Map<String, List<TicketRayon>>
        get() = ticketRayonNotSync.groupBy { it.aRTCode }

    /**
     * Sync specific tickets by article codes
     */
    fun syncTicketsByArticleCodes(articleCodes: List<String>) {
        val ticketsToSync = ticketRayonNotSync.filter { it.aRTCode in articleCodes }
        if (ticketsToSync.isNotEmpty()) {
            // For multiple specific tickets, we'll sync them one by one
            viewModelScope.launch {
                for (ticket in ticketsToSync) {
                    syncTicketRayon(ticket)
                }
            }
        }
    }

    /**
     * Get priority tickets (those with specific criteria)
     */
    val priorityTickets: List<TicketRayon>
        get() = ticketRayonNotSync.filter { ticket ->
            // Define priority criteria here
            ticket.aRTDesignation.isNotEmpty() && ticket.aRTCode.isNotEmpty()
        }
}
