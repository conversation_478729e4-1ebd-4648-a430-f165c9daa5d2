<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property string $Devise
 * @property string $Unite
 * @property string $Symbole
 * @property boolean $Principale
 * @property string $Cours
 * @property boolean $Activite
 * @property int $Nbre_Chiffre_virgule
 */
class Devise extends Model
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'Devise';

    /**
     * The primary key for the model.
     * 
     * @var string
     */
    protected $primaryKey = 'Devise';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    protected $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['Unite', 'Symbole', 'Principale', 'Cours', 'Activite', 'Nbre_Chiffre_virgule'];

}
