package com.asmtunis.procaisseinventory.shared_ui_components.theme

import androidx.activity.SystemBarStyle
import androidx.activity.enableEdgeToEdge
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.graphics.toArgb
import com.asmtunis.procaisseinventory.MainActivity

@Composable
 fun ChangeSystemBarsTheme(activity: MainActivity,lightTheme: Boolean) {
    val barColor = MaterialTheme.colorScheme.background.toArgb()
    LaunchedEffect(lightTheme) {
        if (lightTheme) {
            activity.enableEdgeToEdge(
                statusBarStyle = SystemBarStyle.light(
                    scrim = barColor, darkScrim = barColor,
                ),
                navigationBarStyle = SystemBarStyle.light(
                    scrim = barColor, darkScrim = barColor,
                ),
            )
        } else {
            activity.enableEdgeToEdge(
                statusBarStyle = SystemBarStyle.dark(
                    scrim = barColor,
                ),
                navigationBarStyle = SystemBarStyle.dark(
                    scrim = barColor,
                ),
            )
        }
    }
}
