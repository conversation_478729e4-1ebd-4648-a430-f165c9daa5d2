package com.asmtunis.procaisseinventory.view_model

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.data.article.domaine.ArticleCount
import com.asmtunis.procaisseinventory.articles.data.article.domaine.PaginationResponseArticle
import com.asmtunis.procaisseinventory.articles.data.article.domaine.PaginationResponseArticleCodeBare
import com.asmtunis.procaisseinventory.articles.data.unite_article.domaine.PaginationResponseUniteArticle
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Authorization
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals.PAGE_SIZE
import com.asmtunis.procaisseinventory.core.authorizations.AuthorizationFunction.addToAuthList
import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.Urls.GET_ARTICLES_PAGINATION
import com.asmtunis.procaisseinventory.core.ktor.Urls.GET_STOCK_ARTICLE_BY_STATIONS_PAGINATION
import com.asmtunis.procaisseinventory.core.ktor.Urls.GET_UNITE_ARTICLE_PAGINATION
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.ktor.insertNetworkError
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.ARTICLE_COUNT
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.IS_PROCAISSE_LICENSE_SELECTED
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.IS_PROINVENTORY_LICENSE_SELECTED
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.data.devise.domaine.Devise
import com.asmtunis.procaisseinventory.data.exercice.domaine.Exercice
import com.asmtunis.procaisseinventory.data.famille.domaine.Famille
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.data.parametrages.domaine.Parametrages
import com.asmtunis.procaisseinventory.data.prefixe.domaine.Prefixe
import com.asmtunis.procaisseinventory.data.station.domaine.PaginationResponseStationStockArticle
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.data.timbre.domaine.Timbre
import com.asmtunis.procaisseinventory.data.unite.domaine.Unite
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryRemote
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import java.io.IOException
import javax.inject.Inject


@HiltViewModel
class GetSharedDataViewModel @Inject constructor(
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proInventoryRemote: ProInventoryRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val proInventoryLocalDb: ProInventoryLocalDb
) : ViewModel() {
    private var authorizationList = ArrayList<Authorization>() // ArrayList<String>()
    private val grantedAuthorizationList = ArrayList<String>() // ArrayList<String>()


    var stationStockArticleState: RemoteResponseState<PaginationResponseStationStockArticle> by mutableStateOf(RemoteResponseState())
        private set

    var articleCountState: RemoteResponseState<ArticleCount> by mutableStateOf(RemoteResponseState())
        private set

    var stationState: RemoteResponseState<List<Station>> by mutableStateOf(RemoteResponseState())
        private set

    var parametragesState: RemoteResponseState<Parametrages> by mutableStateOf(RemoteResponseState())
        private set

    var timbreState: RemoteResponseState<List<Timbre>> by mutableStateOf(RemoteResponseState())
        private set

    var marqueState: RemoteResponseState<List<Marque>> by mutableStateOf(RemoteResponseState())
        private set

    var uniteState: RemoteResponseState<List<Unite>> by mutableStateOf(RemoteResponseState())
        private set

    var deviseState: RemoteResponseState<List<Devise>> by mutableStateOf(RemoteResponseState())
        private set

    var familleState: RemoteResponseState<List<Famille>> by mutableStateOf(RemoteResponseState())
        private set


    var prefixState: RemoteResponseState<List<Prefixe>> by mutableStateOf(RemoteResponseState())
        private set


    var articlesState: RemoteResponseState<List<Article>> by mutableStateOf(RemoteResponseState())
        private set

    var articlesPaginationState: RemoteResponseState<PaginationResponseArticle> by mutableStateOf(
        RemoteResponseState()
    )
        private set

    var uniteArticlesPaginationState: RemoteResponseState<PaginationResponseUniteArticle> by mutableStateOf(
        RemoteResponseState()
    )
        private set

    var articlesCodeBarePaginationState: RemoteResponseState<PaginationResponseArticleCodeBare> by mutableStateOf(
        RemoteResponseState()
    )
        private set

    var exericeState: RemoteResponseState<Exercice> by mutableStateOf(RemoteResponseState())
        private set


    private fun setAuthList(autorisationUser: List<Authorization>) {
        authorizationList.clear()
        grantedAuthorizationList.clear()
        authorizationList.addAll(addToAuthList(result = autorisationUser))
        grantedAuthorizationList.addAll(addToAuthList(result = autorisationUser).map { authorisation -> authorisation.AutoCodeAu })

    }

    suspend fun getSharedData(baseConfig: BaseConfig, utilisateur: Utilisateur) = coroutineScope {
        try {
            setAuthList(autorisationUser = utilisateur.autorisationUser)

            // List of tasks to be executed concurrently



            val tasks = ArrayList<Deferred<Unit>>()

            /**
             * Only put this control when ARTICLE_DRAWER_ITEM authorisation is added to procaisse authorisations
             */
            //    if (grantedAuthorizationList.contains(AuthorizationValuesProInventory.ARTICLE_DRAWER_ITEM)) {
            val articleTasks = listOf(
                async { getUnite(baseConfig) },
                async { getArticleCount(baseConfig) },
                async { getstockArticleByStation(baseConfig) },
                async { getArticl(baseConfig, utilisateur) },
                async { getArticleCodeBare(baseConfig) },
                async { getUniteArticle(baseConfig) }
            )
            //  }
            tasks.addAll(articleTasks)
            if (grantedAuthorizationList.isNotEmpty()) {
                val tasksToGet = listOf(
                    async { getTimbre(baseConfig, utilisateur) }
                )
                tasks.addAll(tasksToGet)
            }


            // Wait for all tasks to complete
            tasks.awaitAll()

        } catch (e: Exception) {
            // Handle the error and log it
            Log.e("getSharedData", e.message.toString())
        }
    }

    /**
     * Get data that dont require authorisations or additional param in base config
     */
    suspend fun getCommenSharedData(baseConfig: BaseConfig) = coroutineScope {
        try {
            val tasks = ArrayList<Deferred<Unit>>()

         //   if (grantedAuthorizationList.isNotEmpty()) {
                val tasksToGet = listOf(
                    async { getDevises(baseConfig) },
                    async { getFamille(baseConfig) },
                    async { getMarques(baseConfig) },
                    async { getPrefix(baseConfig) },
                    async { getExercice(baseConfig) },
                    async { getStations(baseConfig) },
                    async { getParametrages(baseConfig) }
                )
                tasks.addAll(tasksToGet)
            //}


            // Wait for all tasks to complete
            tasks.awaitAll()
        } catch (e: Exception) {
            // Handle the error and log it
            Log.e("getCommenSharedData", e.message.toString())
        }
    }


    private var currentArticlePage by mutableIntStateOf(1)

    fun onCurrentArticlePageChange(value: Int) {
        currentArticlePage = value
    }

    fun getArticl(
        baseConfig: BaseConfig,
        utilisateur: Utilisateur

    ) {
        try {

            viewModelScope.launch(dispatcherIO) {


                if (currentArticlePage == 1) {
                    proCaisseLocalDb.articles.deleteAll()
                }
                val getArticlesPagination = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    //  todo very important :     seee with ahmed to make getArticlesPaginationWithStation and
                    // todo normaly we have to use getArticlesPaginationWithStation not getArticlesPagination
// getArticlesPagination return the same result (for patrimoine)
                    proCaisseRemote.articles.getArticlesPaginationWithStation(
                        baseConfig = Json.encodeToString(baseConfigObj),
                        page = currentArticlePage.toString(),
                        limit = PAGE_SIZE,
                        ddm = "",
                        station = utilisateur.Station
                    )

                    /* proCaisseRemote.articles.getArticlesPagination(
                           baseConfig = Json.encodeToString(baseConfigObj),
                           page = currentArticlePage.toString(),
                           limit = PAGE_SIZE
                       )*/

                }
                getArticlesPagination.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            result.data!!.data?.let {
                                proCaisseLocalDb.articles.upsertAll(it)
                            }

                            articlesPaginationState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )


                            currentArticlePage = result.data.currentPage

                            if (result.data.lastPage != currentArticlePage) {
                                currentArticlePage += 1
                                getArticl(
                                    baseConfig = baseConfig,
                                    utilisateur = utilisateur
                                )

                            } else {
                                currentArticlePage = 1

                            }


                        }

                        is DataResult.Loading -> {
                            articlesPaginationState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = GET_ARTICLES_PAGINATION,
                                extraInfo = "Page size: $PAGE_SIZE  Current page: $currentArticlePage",
                                errorMessage = result.message
                            )
                            articlesPaginationState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.launchIn(this)

            }

        } catch (e: IOException) {
            viewModelScope.launch(dispatcherIO) {
                val isProCaisse =
                    proCaisseLocalDb.dataStore.getBoolean(IS_PROCAISSE_LICENSE_SELECTED).first()
                val isProInventory =
                    proCaisseLocalDb.dataStore.getBoolean(IS_PROINVENTORY_LICENSE_SELECTED).first()
// handle this
                if (isProInventory)
                    articlesPaginationState = RemoteResponseState(
                        data = null,
                        loading = false,
                        error = e.message.toString()
                    )
                if (isProCaisse)
                    articlesState = RemoteResponseState(
                        data = null,
                        loading = false,
                        error = e.message.toString()
                    )
                Log.e("getProcaisseData Error", e.message.toString())
// throw MyIoException("Error doing IO", e)
            }

        }
    }


    private var currentUniteArticlePage by mutableIntStateOf(1)

    fun onCurrentUniteArticlePageChange(value: Int) {
        currentUniteArticlePage = value
    }

    fun getUniteArticle(baseConfig: BaseConfig) {
        try {

            viewModelScope.launch(dispatcherIO) {


                if (currentUniteArticlePage == 1) {
                    proCaisseLocalDb.uniteArticles.deleteAll()
                }
                val getUniteArticlesPagination = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )


                    proCaisseRemote.uniteArticles.getUniteArticlesPagination(
                        baseConfig = Json.encodeToString(baseConfigObj),
                        page = currentUniteArticlePage.toString(),
                        limit = PAGE_SIZE
                    )
                }
                getUniteArticlesPagination.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            result.data!!.data?.let {
                                proCaisseLocalDb.uniteArticles.upsertAll(it)
                            }

                            uniteArticlesPaginationState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )


                            currentUniteArticlePage = result.data.currentPage

                            if (result.data.lastPage != currentUniteArticlePage) {
                                currentUniteArticlePage += 1
                                getUniteArticle(
                                    baseConfig = baseConfig,
                                )

                            } else {

                                currentUniteArticlePage = 1
                            }


                        }

                        is DataResult.Loading -> {
                            uniteArticlesPaginationState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = GET_UNITE_ARTICLE_PAGINATION,
                                extraInfo = "Page size: $PAGE_SIZE  Current page: $currentUniteArticlePage",
                                errorMessage = result.message
                            )
                            uniteArticlesPaginationState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.launchIn(this)

            }

        } catch (e: IOException) {
            viewModelScope.launch(dispatcherIO) {
                uniteArticlesPaginationState =
                    RemoteResponseState(data = null, loading = false, error = e.message.toString())
                Log.e("getProcaisseData Error", e.message.toString())
// throw MyIoException("Error doing IO", e)
            }

        }
    }


    private var currentArticleCodeBarePage by mutableIntStateOf(1)

    fun onCurrentArticleCodeBarePageChange(value: Int) {
        currentArticleCodeBarePage = value
    }

    fun getArticleCodeBare(baseConfig: BaseConfig) {
        try {

            viewModelScope.launch(dispatcherIO) {


                if (currentUniteArticlePage == 1) {
                    proCaisseLocalDb.articlesBarCode.deleteAll()
                }
                val getArticlesCodeBarePagination = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )


                    proCaisseRemote.articles.getArticlesCodeBarePagination(
                        baseConfig = Json.encodeToString(baseConfigObj),
                        page = currentArticleCodeBarePage.toString(),
                        limit = PAGE_SIZE
                    )
                }
                getArticlesCodeBarePagination.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            result.data!!.data?.let {
                                proCaisseLocalDb.articlesBarCode.upsertAll(it)
                            }

                            articlesCodeBarePaginationState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )


                            currentArticleCodeBarePage = result.data.currentPage

                            if (result.data.lastPage != currentArticleCodeBarePage) {
                                currentArticleCodeBarePage += 1
                                getArticleCodeBare(
                                    baseConfig = baseConfig,
                                )

                            } else currentArticleCodeBarePage = 1


                        }

                        is DataResult.Loading -> {
                            articlesCodeBarePaginationState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = GET_UNITE_ARTICLE_PAGINATION,
                                extraInfo = "Page size: $PAGE_SIZE  Current page: $currentArticleCodeBarePage",
                                errorMessage = result.message
                            )
                            articlesCodeBarePaginationState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.launchIn(this)

            }

        } catch (e: IOException) {
            viewModelScope.launch(dispatcherIO) {
                articlesCodeBarePaginationState =
                    RemoteResponseState(data = null, loading = false, error = e.message.toString())
                Log.e("getProcaisseData Error", e.message.toString())
// throw MyIoException("Error doing IO", e)
            }

        }
    }


    private var currentStockArticleByStationPage by mutableIntStateOf(1)

    fun onCurrentStockArticleByStationPageChange(value: Int) {
        currentStockArticleByStationPage = value
    }



    fun getstockArticleByStation(baseConfig: BaseConfig) {

        try {

            viewModelScope.launch(dispatcherIO) {


                if (currentStockArticleByStationPage == 1) {
                    proCaisseLocalDb.articlesBarCode.deleteAll()
                }
                val getArticlesCodeBarePagination = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )


                    proInventoryRemote.stations.getstockArticlePagination(
                        baseConfig = Json.encodeToString(baseConfigObj),
                        page = currentStockArticleByStationPage.toString(),
                        limit = PAGE_SIZE
                    )
                }
                getArticlesCodeBarePagination.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            result.data?.data?.let {
                                proInventoryLocalDb.stationsArticle.upsertAll(it)
                            }

                            stationStockArticleState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )


                            currentStockArticleByStationPage = result.data?.currentPage?: 0

                            if (result.data?.lastPage != currentStockArticleByStationPage) {
                                currentStockArticleByStationPage += 1
                                getstockArticleByStation(
                                    baseConfig = baseConfig,
                                )

                            } else currentStockArticleByStationPage = 1


                        }

                        is DataResult.Loading -> {
                            stationStockArticleState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = GET_STOCK_ARTICLE_BY_STATIONS_PAGINATION,
                                extraInfo = "Page size: $PAGE_SIZE  Current page: $currentStockArticleByStationPage",
                                errorMessage = result.message
                            )
                            stationStockArticleState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.launchIn(this)

            }

        } catch (e: IOException) {
            viewModelScope.launch(dispatcherIO) {
                stationStockArticleState =
                    RemoteResponseState(data = null, loading = false, error = e.message.toString())
                Log.e("getProcaisseData Error", e.message.toString())
// throw MyIoException("Error doing IO", e)
            }

        }
    }



    fun getArticleCount(baseConfig: BaseConfig) {
        try {
            viewModelScope.launch {


                val getCountArticle = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.articles.getCountArticle(Json.encodeToString(baseConfigObj))
                }

                getCountArticle.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {


                            proCaisseLocalDb.dataStore.putInt(
                                ARTICLE_COUNT,
                                result.data!!.countArticle
                            )
                            articleCountState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )

                        }

                        is DataResult.Loading -> {
                            articleCountState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_COUNT_ARTICLE,
                                errorMessage = result.message
                            )
                            articleCountState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)


            }
        } catch (e: IOException) {
// handle this

            Log.e("getProInventoryData Error", e.message.toString())
// throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
// handle this too
throw MyOtherException("Error doing something", e)*/
    }

    fun getPrefix(baseConfig: BaseConfig) {
        try {
            viewModelScope.launch {


                val getPrefix = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proCaisseRemote.prefix.getPrefixes(Json.encodeToString(baseConfigObj))
                }
                getPrefix.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.prefix.deleteAll()
                            proCaisseLocalDb.prefix.upsertAll(result.data!!)

                            prefixState =
                                RemoteResponseState(
                                    data = result.data,
                                    loading = false,
                                    error = null
                                )
                        }

                        is DataResult.Loading -> {
                            prefixState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_PREFIXES,
                                errorMessage = result.message
                            )
                            prefixState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)


            }

        } catch (e: IOException) {
// handle this

            Log.e("getProcaisseData Error", e.message.toString())
// throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
// handle this too
throw MyOtherException("Error doing something", e)
}*/
    }

    fun getExercice(
        baseConfig: BaseConfig,
    ) {
        try {
            viewModelScope.launch {
                val getExercice = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )
                    proCaisseRemote.exercice.getExercice(Json.encodeToString(baseConfigObj))
                }

                getExercice.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.exercice.deleteAll()
                            proCaisseLocalDb.exercice.upsert(result.data ?: Exercice())

                            exericeState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )
                        }

                        is DataResult.Loading -> {
                            exericeState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_EXERCICE,
                                errorMessage = result.message
                            )
                            exericeState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }

        } catch (e: IOException) {
// handle this

            Log.e("getProcaisseData Error", e.message.toString())
// throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
// handle this too
throw MyOtherException("Error doing something", e)
}*/
    }



    fun getStations(
        baseConfig: BaseConfig,
    ) {
        try {
            viewModelScope.launch {
//  runBlocking {

                val getStations = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proInventoryRemote.stations.getStations(Json.encodeToString(baseConfigObj))
                }

                getStations.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proInventoryLocalDb.stations.deleteAll()
                            proInventoryLocalDb.stations.upsertAll(result.data!!)
                            stationState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )
                        }

                        is DataResult.Loading -> {
                            stationState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_STATIONS,
                                errorMessage = result.message
                            )
                            stationState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)


            }
        } catch (e: IOException) {
// handle this

            Log.e("getProInventoryData Error", e.message.toString())
// throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
// handle this too
throw MyOtherException("Error doing something", e)*/
    }



    fun getParametrages(
        baseConfig: BaseConfig,
    ) {
        try {
            viewModelScope.launch {
                val getParametrages = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )
                    proCaisseRemote.parametrages.getParametrage(Json.encodeToString(baseConfigObj))
                }
                getParametrages.await().onEach { result ->
                    parametragesState = when (result) {
                        is DataResult.Success -> {


                            proInventoryLocalDb.parametrage.deleteAll()
                            result.data?.let { proInventoryLocalDb.parametrage.upsert(it) }
                            //  }
                            RemoteResponseState(data = result.data, loading = false, error = null)
                        }

                        is DataResult.Loading -> {
                            RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_PARAMETRAGE,
                                errorMessage = result.message
                            )

                            RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }

        } catch (e: IOException) {
// handle this

            Log.e("getProcaisseData Error", e.message.toString())
// throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
// handle this too
throw MyOtherException("Error doing something", e)
}*/
    }



    fun getTimbre(baseConfig: BaseConfig, utilisateur: Utilisateur) {
        try {

            viewModelScope.launch {
                val getAsyncTimbre = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        Json.encodeToJsonElement(utilisateur.codeUt)
                    )
                    proCaisseRemote.timbre.getTimbres(
                        baseConfig = Json.encodeToString(baseConfigObj)
                    )

                }
                getAsyncTimbre.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.timbre.deleteAll()

                            proCaisseLocalDb.timbre.upsertAll(result.data!!)

                            timbreState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )

                        }

                        is DataResult.Loading -> {

                            timbreState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_ALL_TIMBRE,
                                errorMessage = result.message
                            )
                            timbreState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
// handle this

            Log.e("getProcaisseData Error", e.message.toString())
// throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
// handle this too
throw MyOtherException("Error doing something", e)
}*/

    }


    fun getFamille(
        baseConfig: BaseConfig,
    ) {
        try {
            viewModelScope.launch {
                val getFamille = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proInventoryRemote.famille.getFamilles(Json.encodeToString(baseConfigObj))
                }

                getFamille.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proInventoryLocalDb.famille.deleteAll()
                            proInventoryLocalDb.famille.upsertAll(result.data!!)

                            familleState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )

                        }

                        is DataResult.Loading -> {

                            familleState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_FAMILLES,
                                errorMessage = result.message
                            )
                            familleState =
                                RemoteResponseState(
                                    data = null,
                                    loading = false,
                                    error = result.message
                                )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }
        } catch (e: IOException) {
// handle this

            Log.e("getProInventoryData Error", e.message.toString())
// throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
// handle this too
throw MyOtherException("Error doing something", e)*/
    }


    fun getMarques(
        baseConfig: BaseConfig,
    ) {
        try {
            viewModelScope.launch {


                val getMarques = async {
                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proInventoryRemote.marque.getMarques(Json.encodeToString(baseConfigObj))
                }

                getMarques.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proInventoryLocalDb.marque.deleteAll()
                            proInventoryLocalDb.marque.upsertAll(result.data!!)

                            marqueState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )
                        }

                        is DataResult.Loading -> {

                            marqueState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_MARQUES,
                                errorMessage = result.message
                            )
                            marqueState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)


            }
        } catch (e: IOException) {
// handle this

            Log.e("getProInventoryData Error", e.message.toString())
// throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
// handle this too
throw MyOtherException("Error doing something", e)*/
    }


    fun getDevises(
        baseConfig: BaseConfig
    ) {
        try {
            viewModelScope.launch {
                val getAsyncDevise = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )
                    proCaisseRemote.devise.getDevises(
                        baseConfig = Json.encodeToString(baseConfigObj)
                    )

                }
                getAsyncDevise.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proCaisseLocalDb.devise.deleteAll()

                            proCaisseLocalDb.devise.upsertAll(result.data!!)

                            deviseState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )

                        }

                        is DataResult.Loading -> {

                            deviseState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {

                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_DEVISES,
                                errorMessage = result.message
                            )

                            deviseState =
                                RemoteResponseState(
                                    data = null,
                                    loading = false,
                                    error = result.message
                                )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)

            }


        } catch (e: IOException) {
// handle this

            Log.e("getProcaisseData Error", e.message.toString())
// throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
// handle this too
throw MyOtherException("Error doing something", e)
}*/

    }


    fun getUnite(baseConfig: BaseConfig) {
        try {
            viewModelScope.launch {


                val getTva = async {

                    val baseConfigObj = GenericObject(
                        baseConfig,
                        null
                    )

                    proInventoryRemote.unite.getUnite(Json.encodeToString(baseConfigObj))
                }

                getTva.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            proInventoryLocalDb.unite.deleteAll()
                            proInventoryLocalDb.unite.upsertAll(result.data!!)

                            uniteState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )

                        }

                        is DataResult.Loading -> {

                            uniteState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            insertNetworkError(
                                proCaisseLocalDb = proCaisseLocalDb,
                                url = Urls.GET_UNITE,
                                errorMessage = result.message
                            )
                            uniteState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)


            }
        } catch (e: IOException) {
// handle this

            Log.e("getProInventoryData Error", e.message.toString())
// throw MyIoException("Error doing IO", e)
        } /*catch (e: AnotherException) {
// handle this too
throw MyOtherException("Error doing something", e)*/
    }
}