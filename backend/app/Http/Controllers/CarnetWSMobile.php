<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;

class CarnetWSMobile extends Controller
{
    	
	
	
	public function getCarnetByID(Request $request)
	 {
		  $data = $request->json()->all();

	 	$connection = DatabaseConnection::setConnection($data);
		   return response()->json($connection->table('Carnet')->where('CAR_IdCarnet', $data["object"])->get()->first());
	 }
	


	
}





