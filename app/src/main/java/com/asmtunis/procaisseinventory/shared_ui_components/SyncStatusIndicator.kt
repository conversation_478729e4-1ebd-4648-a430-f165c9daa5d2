package com.asmtunis.procaisseinventory.shared_ui_components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.asmtunis.procaisseinventory.core.sync.EntitySyncState
import com.asmtunis.procaisseinventory.core.sync.SyncEntity
import com.asmtunis.procaisseinventory.core.sync.SyncManager
import com.asmtunis.procaisseinventory.core.sync.SyncState
import java.text.SimpleDateFormat
import java.util.*

/**
 * Composable that displays the current sync status with visual indicators
 */
@Composable
fun SyncStatusIndicator(
    syncManager: SyncManager,
    modifier: Modifier = Modifier,
    onSyncClick: (() -> Unit)? = null,
    showDetails: Boolean = false
) {
    val syncState by syncManager.syncState.collectAsState()
    val entityStates by syncManager.entitySyncStates.collectAsState()

    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Main sync status row
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { onSyncClick?.invoke() },
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Sync status icon and text
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    SyncStatusIcon(syncState = syncState)
                    Spacer(modifier = Modifier.width(12.dp))
                    Column {
                        Text(
                            text = getSyncStatusText(syncState),
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Medium
                        )
                        if (syncState.lastSyncEndTime != null) {
                            Text(
                                text = "Dernière sync: ${formatTime(syncState.lastSyncEndTime!!)}",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }

                // Network and auto-sync indicators
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    NetworkIndicator(isConnected = syncState.isNetworkConnected)
                    Spacer(modifier = Modifier.width(8.dp))
                    AutoSyncIndicator(
                        proCaisseEnabled = syncState.isProCaisseAutoSyncEnabled,
                        proInventoryEnabled = syncState.isProInventoryAutoSyncEnabled
                    )
                }
            }

            // Error message if present
            if (syncState.hasError) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = syncState.error ?: "Erreur inconnue",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error,
                    modifier = Modifier.fillMaxWidth()
                )
            }

            // Detailed entity status if requested
            if (showDetails && entityStates.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                EntitySyncDetails(entityStates = entityStates)
            }
        }
    }
}

/**
 * Icon that represents the current sync status
 */
@Composable
private fun SyncStatusIcon(syncState: SyncState) {
    val infiniteTransition = rememberInfiniteTransition(label = "sync_rotation")
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "sync_rotation"
    )

    val (icon, color, shouldRotate) = when {
        syncState.isSyncing -> Triple(Icons.Default.Sync, MaterialTheme.colorScheme.primary, true)
        syncState.hasError -> Triple(Icons.Default.SyncProblem, MaterialTheme.colorScheme.error, false)
        !syncState.isNetworkConnected -> Triple(Icons.Default.CloudOff, MaterialTheme.colorScheme.onSurfaceVariant, false)
        !syncState.isProCaisseAutoSyncEnabled && !syncState.isProInventoryAutoSyncEnabled -> 
            Triple(Icons.Default.SyncDisabled, MaterialTheme.colorScheme.onSurfaceVariant, false)
        else -> Triple(Icons.Default.Sync, MaterialTheme.colorScheme.onSurfaceVariant, false)
    }

    Icon(
        imageVector = icon,
        contentDescription = "Sync Status",
        tint = color,
        modifier = Modifier
            .size(24.dp)
            .then(if (shouldRotate) Modifier.rotate(rotation) else Modifier)
    )
}

/**
 * Network connectivity indicator
 */
@Composable
private fun NetworkIndicator(isConnected: Boolean) {
    val (icon, color) = if (isConnected) {
        Icons.Default.Wifi to MaterialTheme.colorScheme.primary
    } else {
        Icons.Default.WifiOff to MaterialTheme.colorScheme.error
    }

    Icon(
        imageVector = icon,
        contentDescription = if (isConnected) "Connecté" else "Déconnecté",
        tint = color,
        modifier = Modifier.size(16.dp)
    )
}

/**
 * Auto-sync status indicator
 */
@Composable
private fun AutoSyncIndicator(
    proCaisseEnabled: Boolean,
    proInventoryEnabled: Boolean
) {
    val color = when {
        proCaisseEnabled && proInventoryEnabled -> MaterialTheme.colorScheme.primary
        proCaisseEnabled || proInventoryEnabled -> MaterialTheme.colorScheme.tertiary
        else -> MaterialTheme.colorScheme.onSurfaceVariant
    }

    Box(
        modifier = Modifier
            .size(8.dp)
            .background(color, CircleShape)
    )
}

/**
 * Detailed view of entity sync states
 */
@Composable
private fun EntitySyncDetails(entityStates: Map<SyncEntity, EntitySyncState>) {
    Column {
        Text(
            text = "Détails par module:",
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(modifier = Modifier.height(8.dp))

        entityStates.forEach { (entity, state) ->
            EntitySyncRow(entity = entity, state = state)
        }
    }
}

/**
 * Individual entity sync status row
 */
@Composable
private fun EntitySyncRow(entity: SyncEntity, state: EntitySyncState) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = entity.displayName,
            style = MaterialTheme.typography.bodySmall,
            modifier = Modifier.weight(1f)
        )

        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (state.unsyncedCount > 0) {
                Text(
                    text = "${state.unsyncedCount}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary,
                    fontSize = 10.sp
                )
                Spacer(modifier = Modifier.width(4.dp))
            }

            val (icon, color) = when {
                state.isLoading -> Icons.Default.Sync to MaterialTheme.colorScheme.primary
                state.hasError -> Icons.Default.Error to MaterialTheme.colorScheme.error
                state.needsSync -> Icons.Default.SyncProblem to MaterialTheme.colorScheme.tertiary
                else -> Icons.Default.Sync to MaterialTheme.colorScheme.onSurfaceVariant
            }

            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(12.dp)
            )
        }
    }
}

/**
 * Get human-readable sync status text
 */
private fun getSyncStatusText(syncState: SyncState): String {
    return when {
        syncState.isSyncing -> "Synchronisation en cours..."
        syncState.hasError -> "Erreur de synchronisation"
        !syncState.isNetworkConnected -> "Hors ligne"
        !syncState.isProCaisseAutoSyncEnabled && !syncState.isProInventoryAutoSyncEnabled -> "Sync automatique désactivée"
        syncState.lastSyncResult != null -> syncState.lastSyncResult
        else -> "Prêt à synchroniser"
    }
}

/**
 * Format timestamp to readable time
 */
private fun formatTime(timestamp: Long): String {
    val formatter = SimpleDateFormat("HH:mm", Locale.getDefault())
    return formatter.format(Date(timestamp))
}
