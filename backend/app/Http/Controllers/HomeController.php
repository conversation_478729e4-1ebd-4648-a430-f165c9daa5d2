<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use PragmaRX\Version\Package\Version;

class HomeController extends Controller
{
    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index(Request $request)

    {
        DB::connection()->getDatabaseName() ? $db = true : $db = false;
        $Schemas = [
            "Users" => Schema::hasTable('users'),
            "Settings" => Schema::hasTable('Settings'),
            "types" => Schema::hasTable('types'),
            "Logs" => Schema::hasTable('Logs'),
            "article" => Schema::hasTable('article'),
            "depence_caisse" => Schema::hasTable('depence_caisse'),
            "ligne_bon_entree_Mobile" => Schema::hasTable('ligne_bon_entree_Mobile'),
            "ticket_rayon_mobile" => Schema::hasTable('ticket_rayon_mobile'),
            "bon_entree_mobile" => Schema::hasTable('bon_entree_mobile'),
            "Prefixe" => Schema::hasTable('Prefixe'),
            "password_resets" => Schema::hasTable('password_resets'),
            "failed_jobs" => Schema::hasTable('failed_jobs'),
            "personal_access_tokens" => Schema::hasTable('personal_access_tokens'),
            "fournisseur" => Schema::hasTable('fournisseur'),
            "Bon_Retour" => Schema::hasTable('Bon_Retour'),
            "SessionCaisse" => Schema::hasTable('SessionCaisse'),
            "Ticket" => Schema::hasTable('Ticket'),
            "LigneTicket" => Schema::hasTable('LigneTicket'),
            "Ligne_devis" => Schema::hasTable('Ligne_devis'),
            "Devis" => Schema::hasTable('Devis'),
            "Reglement" => Schema::hasTable('Reglement'),
            "ReglementCaisse" => Schema::hasTable('ReglementCaisse'),
        ];
        if ($request->ajax() || $request->isJson()) {
            return $this->successResponse([
                'has_pdo_sqlsrv' => !!extension_loaded('pdo_sqlsrv'),
                'has_sqlsrv' => !!extension_loaded('sqlsrv'),
                'has_valid_php_version' => version_compare(PHP_VERSION, '7.4.0') >= 0,
                'min_version_code' => env('MIN_VERSION_CODE'),
                'has_valid_version' => version_compare(PHP_VERSION, '7.4.0') >= 0,
                'min_db_version' => env('MIN_DB_VERSION'),
                'php_version' => PHP_VERSION,
                'app_version' => (new Version())->format('full'),
                'app_name' => config('app.name'),
                'auth' => false,
                'db' => $db,
                'Schemas' => $Schemas,
            ]);
        } else {
            return view('welcome', [
                'has_pdo_sqlsrv' => !!extension_loaded('pdo_sqlsrv'),
                'has_sqlsrv' => !!extension_loaded('sqlsrv'),
                'has_valid_php_version' => version_compare(PHP_VERSION, '7.4.0') >= 0,
                'min_version_code' => env('MIN_VERSION_CODE'),
                'has_valid_version' => version_compare(PHP_VERSION, '7.4.0') >= 0,
                'min_db_version' => env('MIN_DB_VERSION'),
                'php_version' => PHP_VERSION,
                'app_version' => (new Version())->format('full'),
                'app_name' => config('app.name'),
                'auth' => false,
                'db' => $db,
                'Schemas' => $Schemas,
            ]);
        }

    }
}
