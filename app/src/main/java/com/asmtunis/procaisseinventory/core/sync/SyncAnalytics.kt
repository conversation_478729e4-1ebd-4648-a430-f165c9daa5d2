package com.asmtunis.procaisseinventory.core.sync

import android.util.Log
import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Analytics service for tracking sync operations and performance metrics
 */
@Singleton
class SyncAnalytics @Inject constructor(
    private val dataStoreRepository: DataStoreRepository,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher
) {
    companion object {
        private const val TAG = "SyncAnalytics"
        private const val SYNC_STATS_KEY = "sync_analytics_stats"
        private const val SYNC_EVENTS_KEY = "sync_analytics_events"
        private const val MAX_EVENTS_STORED = 100
    }

    private val analyticsScope = CoroutineScope(ioDispatcher + SupervisorJob())

    // Current analytics state
    private val _syncStats = MutableStateFlow(SyncAnalyticsStats())
    val syncStats: StateFlow<SyncAnalyticsStats> = _syncStats.asStateFlow()

    private val _recentEvents = MutableStateFlow<List<SyncEvent>>(emptyList())
    val recentEvents: StateFlow<List<SyncEvent>> = _recentEvents.asStateFlow()

    init {
        loadStoredAnalytics()
    }

    /**
     * Track the start of a sync operation
     */
    fun trackSyncStart(entity: SyncEntity? = null, itemCount: Int = 0) {
        analyticsScope.launch {
            val event = SyncEvent(
                type = SyncEventType.SYNC_STARTED,
                entity = entity,
                timestamp = System.currentTimeMillis(),
                itemCount = itemCount
            )
            
            addEvent(event)
            updateStats { stats ->
                stats.copy(
                    totalSyncAttempts = stats.totalSyncAttempts + 1,
                    lastSyncStartTime = event.timestamp
                )
            }
            
            Log.d(TAG, "Sync started: entity=$entity, items=$itemCount")
        }
    }

    /**
     * Track successful sync completion
     */
    fun trackSyncSuccess(
        entity: SyncEntity? = null,
        itemCount: Int = 0,
        duration: Long = 0
    ) {
        analyticsScope.launch {
            val event = SyncEvent(
                type = SyncEventType.SYNC_SUCCESS,
                entity = entity,
                timestamp = System.currentTimeMillis(),
                itemCount = itemCount,
                duration = duration
            )
            
            addEvent(event)
            updateStats { stats ->
                val newSuccessfulSyncs = stats.successfulSyncs + 1
                val newTotalDuration = stats.totalSyncDuration + duration
                val newAverageDuration = if (newSuccessfulSyncs > 0) {
                    newTotalDuration / newSuccessfulSyncs
                } else 0L
                
                stats.copy(
                    successfulSyncs = newSuccessfulSyncs,
                    totalSyncDuration = newTotalDuration,
                    averageSyncDuration = newAverageDuration,
                    totalItemsSynced = stats.totalItemsSynced + itemCount,
                    lastSuccessfulSyncTime = event.timestamp
                )
            }
            
            Log.d(TAG, "Sync success: entity=$entity, items=$itemCount, duration=${duration}ms")
        }
    }

    /**
     * Track sync failure
     */
    fun trackSyncFailure(
        entity: SyncEntity? = null,
        error: String,
        duration: Long = 0
    ) {
        analyticsScope.launch {
            val event = SyncEvent(
                type = SyncEventType.SYNC_FAILED,
                entity = entity,
                timestamp = System.currentTimeMillis(),
                error = error,
                duration = duration
            )
            
            addEvent(event)
            updateStats { stats ->
                stats.copy(
                    failedSyncs = stats.failedSyncs + 1,
                    lastFailureTime = event.timestamp,
                    lastFailureReason = error
                )
            }
            
            Log.w(TAG, "Sync failed: entity=$entity, error=$error, duration=${duration}ms")
        }
    }

    /**
     * Track sync throttling
     */
    fun trackSyncThrottled(reason: String = "Rate limited") {
        analyticsScope.launch {
            val event = SyncEvent(
                type = SyncEventType.SYNC_THROTTLED,
                timestamp = System.currentTimeMillis(),
                error = reason
            )
            
            addEvent(event)
            updateStats { stats ->
                stats.copy(throttledSyncs = stats.throttledSyncs + 1)
            }
            
            Log.d(TAG, "Sync throttled: $reason")
        }
    }

    /**
     * Track network connectivity changes
     */
    fun trackNetworkChange(isConnected: Boolean) {
        analyticsScope.launch {
            val event = SyncEvent(
                type = if (isConnected) SyncEventType.NETWORK_CONNECTED else SyncEventType.NETWORK_DISCONNECTED,
                timestamp = System.currentTimeMillis()
            )
            
            addEvent(event)
            
            Log.d(TAG, "Network ${if (isConnected) "connected" else "disconnected"}")
        }
    }

    /**
     * Track auto-sync setting changes
     */
    fun trackAutoSyncChange(module: String, enabled: Boolean) {
        analyticsScope.launch {
            val event = SyncEvent(
                type = SyncEventType.AUTO_SYNC_CHANGED,
                timestamp = System.currentTimeMillis(),
                error = "$module: ${if (enabled) "enabled" else "disabled"}"
            )
            
            addEvent(event)
            
            Log.d(TAG, "Auto-sync changed: $module = $enabled")
        }
    }

    /**
     * Get sync performance metrics
     */
    suspend fun getPerformanceMetrics(): SyncPerformanceMetrics = withContext(ioDispatcher) {
        val stats = _syncStats.value
        val events = _recentEvents.value
        
        val recentSuccesses = events.filter { 
            it.type == SyncEventType.SYNC_SUCCESS && 
            it.timestamp > System.currentTimeMillis() - 24 * 60 * 60 * 1000 // Last 24 hours
        }
        
        val recentFailures = events.filter { 
            it.type == SyncEventType.SYNC_FAILED && 
            it.timestamp > System.currentTimeMillis() - 24 * 60 * 60 * 1000 // Last 24 hours
        }
        
        SyncPerformanceMetrics(
            successRate = if (stats.totalSyncAttempts > 0) {
                stats.successfulSyncs.toFloat() / stats.totalSyncAttempts
            } else 0f,
            averageSyncDuration = stats.averageSyncDuration,
            totalItemsSynced = stats.totalItemsSynced,
            recentSuccessCount = recentSuccesses.size,
            recentFailureCount = recentFailures.size,
            lastSyncTime = stats.lastSuccessfulSyncTime,
            mostCommonErrors = recentFailures
                .mapNotNull { it.error }
                .groupingBy { it }
                .eachCount()
                .toList()
                .sortedByDescending { it.second }
                .take(5)
        )
    }

    /**
     * Export analytics data for debugging or reporting
     */
    suspend fun exportAnalyticsData(): String = withContext(ioDispatcher) {
        val data = SyncAnalyticsExport(
            stats = _syncStats.value,
            events = _recentEvents.value.takeLast(50) // Last 50 events
        )
        Json.encodeToString(data)
    }

    /**
     * Clear all analytics data
     */
    suspend fun clearAnalytics() = withContext(ioDispatcher) {
        _syncStats.value = SyncAnalyticsStats()
        _recentEvents.value = emptyList()
        
        dataStoreRepository.putString(SYNC_STATS_KEY, "")
        dataStoreRepository.putString(SYNC_EVENTS_KEY, "")
        
        Log.d(TAG, "Analytics data cleared")
    }

    /**
     * Add a new event to the recent events list
     */
    private suspend fun addEvent(event: SyncEvent) {
        val currentEvents = _recentEvents.value.toMutableList()
        currentEvents.add(event)
        
        // Keep only the most recent events
        if (currentEvents.size > MAX_EVENTS_STORED) {
            currentEvents.removeAt(0)
        }
        
        _recentEvents.value = currentEvents
        saveEvents()
    }

    /**
     * Update sync statistics
     */
    private suspend fun updateStats(update: (SyncAnalyticsStats) -> SyncAnalyticsStats) {
        _syncStats.value = update(_syncStats.value)
        saveStats()
    }

    /**
     * Load stored analytics data
     */
    private fun loadStoredAnalytics() {
        analyticsScope.launch {
            try {
                // Load stats
                val statsJson = dataStoreRepository.getString(SYNC_STATS_KEY).first()
                if (!statsJson.isNullOrEmpty()) {
                    _syncStats.value = Json.decodeFromString<SyncAnalyticsStats>(statsJson)
                }
                
                // Load events
                val eventsJson = dataStoreRepository.getString(SYNC_EVENTS_KEY).first()
                if (!eventsJson.isNullOrEmpty()) {
                    _recentEvents.value = Json.decodeFromString<List<SyncEvent>>(eventsJson)
                }
                
                Log.d(TAG, "Analytics data loaded")
            } catch (exception: Exception) {
                Log.e(TAG, "Failed to load analytics data", exception)
            }
        }
    }

    /**
     * Save statistics to persistent storage
     */
    private suspend fun saveStats() {
        try {
            val statsJson = Json.encodeToString(_syncStats.value)
            dataStoreRepository.putString(SYNC_STATS_KEY, statsJson)
        } catch (exception: Exception) {
            Log.e(TAG, "Failed to save stats", exception)
        }
    }

    /**
     * Save events to persistent storage
     */
    private suspend fun saveEvents() {
        try {
            val eventsJson = Json.encodeToString(_recentEvents.value)
            dataStoreRepository.putString(SYNC_EVENTS_KEY, eventsJson)
        } catch (exception: Exception) {
            Log.e(TAG, "Failed to save events", exception)
        }
    }
}

/**
 * Comprehensive sync analytics statistics
 */
@Serializable
data class SyncAnalyticsStats(
    val totalSyncAttempts: Int = 0,
    val successfulSyncs: Int = 0,
    val failedSyncs: Int = 0,
    val throttledSyncs: Int = 0,
    val totalSyncDuration: Long = 0,
    val averageSyncDuration: Long = 0,
    val totalItemsSynced: Int = 0,
    val lastSyncStartTime: Long? = null,
    val lastSuccessfulSyncTime: Long? = null,
    val lastFailureTime: Long? = null,
    val lastFailureReason: String? = null
)

/**
 * Individual sync event for tracking
 */
@Serializable
data class SyncEvent(
    val type: SyncEventType,
    val entity: SyncEntity? = null,
    val timestamp: Long,
    val itemCount: Int = 0,
    val duration: Long = 0,
    val error: String? = null
)

/**
 * Types of sync events that can be tracked
 */
@Serializable
enum class SyncEventType {
    SYNC_STARTED,
    SYNC_SUCCESS,
    SYNC_FAILED,
    SYNC_THROTTLED,
    NETWORK_CONNECTED,
    NETWORK_DISCONNECTED,
    AUTO_SYNC_CHANGED
}

/**
 * Performance metrics derived from analytics data
 */
data class SyncPerformanceMetrics(
    val successRate: Float,
    val averageSyncDuration: Long,
    val totalItemsSynced: Int,
    val recentSuccessCount: Int,
    val recentFailureCount: Int,
    val lastSyncTime: Long?,
    val mostCommonErrors: List<Pair<String, Int>>
)

/**
 * Export format for analytics data
 */
@Serializable
data class SyncAnalyticsExport(
    val stats: SyncAnalyticsStats,
    val events: List<SyncEvent>
)
