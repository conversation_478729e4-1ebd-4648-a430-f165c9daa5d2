package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentCheckResponse
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class InventaireBatimentApiImpl(private val client: HttpClient) : InventaireBatimentApi {
    override suspend fun affectCodeBareBatiment(baseConfig: String): Flow<DataResult<BatimentCheckResponse>> = flow {

        val result = executePostApiCall<BatimentCheckResponse>(
            client = client,
            endpoint = Urls.AFFECT_CODE_BARE_BATIMENT,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

}