package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.navigation.PromotionDetailRoute
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.PromotionViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.text_validation.VcTextValidationViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.view_model.VeilleConcurentielViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.CameraViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.ListItem
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PullToRefreshLazyColumn
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel

@Composable
fun PromotionScreen (
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    listState: LazyListState,
    isConnected: Boolean,
    selectedBaseconfig: BaseConfig,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    vcViewModel: VeilleConcurentielViewModel,
    cameraViewModel: CameraViewModel,
    promotionViewModel: PromotionViewModel,
    mainViewModel: MainViewModel,
    textValidationViewModel: VcTextValidationViewModel = hiltViewModel()
){


    val promotionList = promotionViewModel.promotionVCListstate.lists

    val promoState  = getProCaisseDataViewModel.promoState


   if(promotionList.isNotEmpty()) {
       ListAutre (
           navigate = { navigate(it) },
           popBackStack = { popBackStack() },
           isConnected = isConnected,
           selectedBaseconfig = selectedBaseconfig,
           getProCaisseDataViewModel = getProCaisseDataViewModel,
           listState = listState,
           textValidationViewModel = textValidationViewModel,
           promotionList = promotionList,
           vcViewModel = vcViewModel,
           promotionViewModel = promotionViewModel,
           cameraViewModel = cameraViewModel,
           promoState = promoState,
           mainViewModel = mainViewModel,
           utilisateur = mainViewModel.utilisateur
       )
   }
    else Column(
       modifier = Modifier.fillMaxSize(),
       verticalArrangement = Arrangement.Center,
       horizontalAlignment = Alignment.CenterHorizontally
   ) {
       LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
   }
}

@Composable
fun ListAutre (
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    isConnected: Boolean,
    promoState: RemoteResponseState<List<PromoVC>>,
    cameraViewModel: CameraViewModel,
    selectedBaseconfig: BaseConfig,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    listState: LazyListState,
    textValidationViewModel: VcTextValidationViewModel,
    promotionList: List<PromoVCWithImages>,
    vcViewModel: VeilleConcurentielViewModel,
    promotionViewModel: PromotionViewModel,
    mainViewModel: MainViewModel,
    utilisateur: Utilisateur
){
    val isRefreshing  = promoState.loading

    val prefixList = mainViewModel.prefixList
    PullToRefreshLazyColumn(
        items = promotionList,
        lazyListState = listState,
        isRefreshing = isRefreshing,
        pullToRefreshEnabled = !promotionList.any { it.promoVC?.isSync != true } && isConnected,
        onRefresh = { getProCaisseDataViewModel.getVCPromo(baseConfig = selectedBaseconfig) },
        key = { promoList -> promoList.promoVC?.id?: "" },
        content = { promo ->
            ListItem(
                onItemClick={
                    val prefixe = prefixList.firstOrNull { it.pREIdTable == "VCPrix" }?.pREPrefixe?: "VC_PR_M_IMG"
                    val prefixImage = prefixList.firstOrNull { it.pREIdTable == "VC_Image" }?.pREPrefixe?: "VC_PR_M"

                    mainViewModel.generateCodeM(utilisateur = utilisateur,
                        prefix = prefixe,
                        prefixImage = prefixImage
                    )


                    cameraViewModel.addListImageUri(promo.imageList?: emptyList())
                    promotionViewModel.onselectedPromotionChange(promo)
                    promotionViewModel.onModifyChange(false)
                   // cameraViewModel.addImageUri(imgUri = EMPTY_IMAGE_URI, context = context)
                    navigate(PromotionDetailRoute)

                    textValidationViewModel.resetVariable()
                },
                firstText = promo.promoVC?.codeVCPromo?: "N/A",
                secondText = stringResource(id = R.string.type_communication_value, vcViewModel.typeCommunicationVCList.firstOrNull { it.codeTypeCom == promo.promoVC?.codeTypeCom }?.typeCommunication?: promo.promoVC?.codeTypeCom?: "N/A"),
                thirdText = stringResource(id = R.string.art_concurrent, promo.promoVC?.articleConcur?: "N/A"),


                dateText = promo.promoVC?.dateOp.toString(),
                isSync = promo.promoVC?.isSync == true,
                status = promo.promoVC?.status.toString(),
                onResetDeletedClick = {
                    promotionViewModel.restDeletedPromotion(promo)
                },
                onMoreClick = {
                    promotionViewModel.onselectedPromotionChange(promo)
                    vcViewModel.onShowCustomModalBottomSheetChange(true)
                }
            )
        },
    )

}