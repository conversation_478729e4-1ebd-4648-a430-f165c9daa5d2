<?php

namespace App\Models\DuxInventory;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class BaseModel extends Model
{
    protected $idClasse;
    protected $useCode;

    protected $casts = [
        'isSync' => 'boolean',
        'isDefault' => 'boolean',
        'active' => 'boolean',
        'j_export' => 'boolean',
    ];

    public static function getID()
    {
        return str_replace("-", "", Str::uuid());
    }

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->handleCreating();
        });
        static::updating(function ($model) {
            $model->handleUpdating();
        });
        static::saving(function ($model) {
            $model->handleSaving();
        });
        static::created(function ($model) {
            $model->handleCreated();
        });
    }

    protected function handleSaving()
    {
        if ($this->isDefault) {
            self::where('id', '!=', $this->getKey())
                ->where('isDefault', true)
                ->update(['isDefault' => false]);
        }
        $this->j_ddm = Carbon::now()->format('Y-m-d H:i:s');
        $this->isSync = true;
        if (!$this->j_codeStation || !$this->P_codeStation) {
            if ($this->j_codeUser) {
               $this->setStationAttributes($this->j_codeUser);
            }
        }
    }

    protected function handleUpdating()
    {
        $this->j_operation = "modification";
    }

    protected function handleCreating()
    {
        $this->{$this->getKeyName()} = self::getID();

        if($this->useCode){
            if (!$this->code && !$this->idClasse) {
                throw new \Exception('No prefix found to generate code');
            }
            if (!$this->code && $this->idClasse) {
                $this->code = P_Prefixe::getCode($this->idClasse);
                if($this->code==null){
                    throw new \Exception('No prefix found to generate code');
                }
            }
        }
        $this->P_codeExercice = P_Exercice::where('active','1')->value('code');
        $this->j_export= false;
        $this->j_operation = "insertion";
    }

    protected function handleCreated()
    {
        if($this->useCode) {
            $prefixe = new P_Prefixe();
            $prefixe->updateCompteur($this->idClasse);
        }
    }


    public function setStationAttributes($codeUser)
    {
        $dataUser = P_Utilisateur::findOrFail($codeUser);
        $this->j_codeStation = $dataUser->idStation ?? null;
        if ($this->j_codeStation) {
            $dataStation = P_Station::findOrFail($this->j_codeStation);
            $this->P_codeStation = $dataStation->code ?? null;
        }
    }



}
