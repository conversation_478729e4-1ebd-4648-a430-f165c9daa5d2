package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


@Entity(tableName = ProCaisseConstants.PRIX_TABLE)
@Serializable
data class PrixVC(
    @PrimaryKey(autoGenerate = true)
    @Transient
    val id: Long = 0,
    @ColumnInfo(name = "CodeVCPrix")
    @SerialName("CodeVCPrix")
    var codeVCPrix: String = "",

    @ColumnInfo(name = "CodeVCPrixM")
    @SerialName("CodeVCPrixM")
    var codeVCPrixM: String = "",

    @ColumnInfo(name = "CodeArtLocal")
    @SerialName("CodeArtLocal")
    var codeArtLocal: String? = "",

    @ColumnInfo(name = "ArticleConcur")
    @SerialName("ArticleConcur")
    var articleConcur: String = "",

    @ColumnInfo(name = "DateOp")
    @SerialName("DateOp")
    var dateOp: String = "",

    @ColumnInfo(name = "CodeConcur")
    @SerialName("CodeConcur")
    var codeConcur: String? = "",

    @ColumnInfo(name = "NoteOp")
    @SerialName("NoteOp")
    var noteOp: String = "",

    @ColumnInfo(name = "PrixConcur")
    @SerialName("PrixConcur")
    var prixConcur: Double? = 0.0,

    @ColumnInfo(name = "CodeUser")
    @SerialName("CodeUser")
    var codeUser: Int? = 0,

    @ColumnInfo(name = "InfoOp1")
    @SerialName("InfoOp1")
    var infoOp1: String? = "",

    @ColumnInfo(name = "CodeTypeCom")
    @SerialName("CodeTypeCom")
    var codeTypeCom: String? = ""

) : BaseModel()
