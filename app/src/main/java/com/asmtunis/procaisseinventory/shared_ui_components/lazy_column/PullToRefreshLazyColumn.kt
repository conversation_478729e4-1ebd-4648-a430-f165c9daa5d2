
@file:OptIn(ExperimentalMaterial3Api::class)
package com.asmtunis.procaisseinventory.shared_ui_components.lazy_column




import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun <T> PullToRefreshLazyColumn(
    items: List<T>,
    isRefreshing: <PERSON><PERSON>an,
    pullToRefreshEnabled: Boolean = true,
    onRefresh: () -> Unit,
    modifier: Modifier = Modifier,
    lazyListState: LazyListState,
    key: (T) -> Any,
    content: @Composable (T) -> Unit,
) {
    val state = rememberPullToRefreshState()
  /*  val pullToRefreshState = rememberPullToRefreshState(enabled = { pullToRefreshEnabled })
    Box(
        modifier = modifier.nestedScroll(pullToRefreshState.nestedScrollConnection)
    ) {
        LazyColumn(
            state = lazyListState,
          //  contentPadding = PaddingValues(8.dp),
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(items = items,
                key = key
            ) {
                content(it)
            }
        }

        if(pullToRefreshState.isRefreshing) {
            LaunchedEffect(true) {
                onRefresh()
            }
        }

        LaunchedEffect(isRefreshing) {
            if(isRefreshing) {
                pullToRefreshState.startRefresh()
            } else {
                pullToRefreshState.endRefresh()
            }
        }

        if(pullToRefreshState.verticalOffset> 0)
        PullToRefreshContainer(
            state = pullToRefreshState,
            modifier = Modifier.align(Alignment.TopCenter),
        )
    }*/


    PullToRefreshBox(
      //  modifier = Modifier.padding(it),
        state = state,
        isRefreshing = isRefreshing,
        onRefresh = { if(pullToRefreshEnabled) onRefresh() },
    ) {
        LazyColumn(
            state = lazyListState,
            //  contentPadding = PaddingValues(8.dp),
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(items = items,
                key = key
            ) {
                content(it)
            }

             item {
                Spacer(modifier = Modifier.height(80.dp))

            }
        }
    }
}