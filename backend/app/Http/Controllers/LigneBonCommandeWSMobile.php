<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use App\Helpers\AppHelper;
use App\Helpers\Enum;
use App\Models\Client;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Helpers\DB;
use App\Models\VCImage;

class LigneBonCommandeWSMobile extends Controller
{

    public function getLigneBonCommande(Request $request)
    {
        $data = $request->json()->all();


        $connection = DatabaseConnection::setConnection($data);
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfMonth = Carbon::now()->endOfMonth();

        $query = $connection->table('ligne_devis')
            ->whereBetween('ligne_devis.LG_DEV_DDm', [$startOfMonth, $endOfMonth]);

        if (($request->has('zone') && $request->input('zone') === true) || ($request->input('zone') === 'true' && $request->hasHeader('user'))) {
            $clients = array_column(Client::query()
                ->join('Zone', 'Client.Clt_Circuit', '=', 'Zone.CodeZone')
                ->join('ZoneUtilisateur', 'Zone.CodeZone', '=', 'ZoneUtilisateur.CodeZone')
                ->where('Zone.EtatZone', '=', true)
                ->where('ZoneUtilisateur.CodeUtilisateur', '=', $request->header('user'))
                ->select('Client.CLI_Code')
                ->get()->toArray(), 'CLI_Code');

            $devis = array_column($connection->table('Devis')
                ->whereIn('DEV_CodeClient', $clients)->get()->toArray(), 'DEV_Num');

            $query->whereIn('LG_DEV_NumBon', $devis);
        }


        return response()->json($query->get(), 200, ['Content-type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function addBatchLigneBonCommande($items, $connection, $numBC, $nbLigneBC, $devInfo3 = "", $devEtat = "", $devUser = null)
    {
        $testLigneBc = true;
        $countligneBc = 0;
        if ($devInfo3 == 'entree') {
            $countLigneDepOut = $connection->table('ligne_devis')->where("LG_DEV_NumBon", $items[0]['LG_DEV_N_DEV_IN'])->count();
            $LigneInInserted = 0;
        }


        foreach ($items as $data) {
            //get article
            $article = $connection->table('article')->where("ART_Code", $data["LG_DEV_CodeArt"])->first();
            $tva = ($article->ART_TVA) / 100;
            //get unites article
            $Unite_article = $connection->table('Unite_article')
                //->where("UNITE_ARTICLE_CodeUnite", $data["LG_BonTrans_Unite"])
                ->where("UNITE_ARTICLE_CodeArt", $data["LG_DEV_CodeArt"])
                ->get();

            if (!!$Unite_article) {
                //if this article has a lot of unity so we have to select the one who has UNITE_ARTICLE_IsUnitaire == 1
                if (sizeof($Unite_article) > 1) {
                    foreach ($Unite_article as $unite) {
                        if ($unite->UNITE_ARTICLE_IsUnitaire == 1) {
                            $data["LG_DEV_Unite"] = $unite->UNITE_ARTICLE_CodeUnite;
                            $data["LG_DEV_QtePiece"] = $unite->UNITE_ARTICLE_QtePiece;
                            $data['LG_DEV_PUHT'] = $unite->UNITE_ARTICLE_PrixVenteTTC / (1 + $tva);
                        }
                    }

                } else {
                    //select the defaut unity
                    foreach ($Unite_article as $unite) {
                        $data["LG_DEV_Unite"] = $unite->UNITE_ARTICLE_CodeUnite;
                        $data["LG_DEV_QtePiece"] = $unite->UNITE_ARTICLE_QtePiece;
                        $data['LG_DEV_PUHT'] = $unite->UNITE_ARTICLE_PrixVenteTTC / (1 + $tva);

                    }
                }
            }
            $data['LG_DEV_Tva'] = $article->ART_TVA;

            $data['LG_DEV_NumBon'] = $numBC;
            $data["LG_DEV_MntBrutHT"] = floatval($data["LG_DEV_MntBrutHT"]);
            $data["LG_DEV_Code_M"] = $data["LG_DEV_Code_M"] ?? null;
            $data["LG_DEV_MntHT"] = floatval($data["LG_DEV_MntHT"]);
            $data["LG_DEV_MntTva"] = floatval($data["LG_DEV_MntTva"]);
            $data["LG_DEV_MntTTC"] = floatval($data["LG_DEV_MntTTC"]);
            if (array_key_exists("LG_DEV_PUTTC", $data)) {
                $data["LG_DEV_PUTTC"] = floatval($data["LG_DEV_PUTTC"]);
            }

            $data['LG_DEV_DDm'] = Carbon::now();

            $data['LG_DEV_Netht'] = $data['LG_DEV_Qte'] * $data['LG_DEV_PUHT'];


            if ($devInfo3 == 'entree') {
                $LigneInInserted++;
            }
            $successInsertLigneBc = $connection->table('ligne_devis')->insert($data);

            if ($successInsertLigneBc) {
                $countligneBc++;

            }
        }

        if ((strtolower($devEtat) == "immobilisation") && (strtolower($devInfo3) == 'entree') && ($LigneInInserted == $countLigneDepOut)) {
            $connection->table('devis')
                ->where('DEV_Num', '=', $items[0]['LG_DEV_N_DEV_IN'])
                ->update(['DEV_EtatBon' => '2']);
            $connection->table('devis')
                ->where('DEV_Num', '=', $numBC)
                ->update(['DEV_EtatBon' => '2']);
        }

        if ($countligneBc < $nbLigneBC) {
            $testLigneBc = false;
        }
        return $testLigneBc;
    }


    public function deleteBatchLigneBonEntrees(Request $request)
    {
        $items = $request->json()->all();

        if (!empty($items)) {
            //    $data['object']['CLI_Code'] =(new PrefixWSMobile)->getPrefix($data,"Client");
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {

                $c = $connection->table('ligne_bon_entree_Mobile')
                    ->where('LIG_BonEntree_NumBon', $data["LIG_BonEntree_NumBon"])->where('LIG_BonEntree_CodeArt', $data["LIG_BonEntree_CodeArt"])->delete();

                if ($c) {
                    $result[] = $data;
                } else {
                    $result[] = null;
                }
            }
        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }

    public function updateBatchLigneBonEntrees(Request $request)
    {
        $items = $request->json()->all();

        if (!empty($items)) {
            //    $data['object']['CLI_Code'] =(new PrefixWSMobile)->getPrefix($data,"Client");
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {
                $c = $connection->table('ligne_bon_entree_Mobile')->where('LIG_BonEntree_NumBon', $data["LIG_BonEntree_NumBon"])->where('LIG_BonEntree_CodeArt', $data["LIG_BonEntree_CodeArt"])->update($data);

                if ($c) {
                    $result[] = $data;
                } else {
                    $result[] = null;
                }
            }
        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }

    public function addBatchLigneBonEntrees(Request $request)
    {
        $items = $request->json()->all();

        if (!empty($items)) {
            //    $data['object']['CLI_Code'] =(new PrefixWSMobile)->getPrefix($data,"Client");
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {
                //$codebar=$connection->table('article_code_bar')->where('Fils_CodeBar','=',$data["LIG_BonEntree_CodeArt"])->get();
                //    $article=$connection->table('article')->where('ART_CodeBar','=',($codebar[0]->Parent_CodeBar))->get();
                //var_dump($banque);
                //    $data["LIG_BonEntree_CodeArt"]=$article[0]->LIG_BonEntree_CodeArt;
                $c = $connection->table('ligne_bon_entree_Mobile')->insert($data);

                if ($c) {
                    $result[] = $data;
                } else {
                    $result[] = null;
                }
            }
        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }


    public function upload(Request $request)
    {
        $result = [];
        $object = $request->json()->all();
        if (!empty($object)) {
            $connection = DatabaseConnection::setConnection($object);

            foreach ($object["object"] as &$data) {
                try {

                    $numDevis = $data["parent"]['DEV_Num'];
                    $devis = $connection->table('devis')->where('DEV_Code_M', '=', $numDevis)->first();

                    $devEtat = $devis->DEV_Etat;
                    $devInfo3 = $devis->DEV_info3;

                    if ((strtolower($devEtat) == "immobilisation")) {

                        foreach ($data["children"] as $item) {
                            $connection->beginTransaction();


                            $item['Code_TypeVC'] = $item['Code_Mob'];
                            $item['Type_VC'] = $devEtat . ';' . $devInfo3;


                            $image = $item["Image"];
                            unset($item["Image"]);
                            $image = str_replace('data:image/png;base64,', '', $image);
                            $image = str_replace(' ', '+', $image);
                            $imageName = str_random(10) . '.' . 'png';
                            $storage = (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') ? Storage::disk('proCaisse_path') : Storage::disk('public');
                            $storage->put($item["Code_IMG"] . '/' . $imageName, base64_decode($image));
                            $item["Chemin_Img"] = $storage->getDriver()->getAdapter()->getPathPrefix() . $item["Code_IMG"] . '/' . $imageName;
                            $url = explode('/', str_replace('\\', '/', $item["Chemin_Img"]));
                            $item["imgUrl"] = 'storage/' . $url[sizeof($url) - 2] . '/' . $url[sizeof($url) - 1];


                            $inserted = $connection->table('VC_Image')->updateOrInsert(["Code_IMG" => $item["Code_IMG"]], $item);

                            if (!$inserted) {

                                $result[] = $this->setGlobalResult("Code_IMG", $item["Code_IMG"],
                                    "Erreur dans l'insertion d'image VCon", Enum::Error_Upload_Image_VC
                                );
                                Log::error("Erreur dans l'insertion d'image VCon", $result);
                                throw new Exception("", Enum::Error_Upload_Image_VC);
                            } else {
                                $result[] = $this->setGlobalResult("Code_IMG", $item["Code_IMG"],
                                    "INSERTED", Enum::Success_Code
                                );
                            }


                        }
                    }

                    $connection->commit();
                } catch (\Exception|\Throwable $e) {
                    $connection->rollBack();
                }
            }
        }
        return json_encode($result);
    }

    public function getImages(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $query = VCImage::query();
        if ($request->hasAny('LG_DEV_Code_M') && !!$request->input('LG_DEV_Code_M')) {
            $query->where('Code_TypeVC', '=', $request->input('LG_DEV_Code_M'));
        }
        return response()->json($query->get());
    }


}
