package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


@Entity(
    tableName = ProCaisseConstants.LIGNE_TICKET_TABLE/*,
    primaryKeys = ["LT_NumTicket", "LT_IdCarnet", "LT_Exerc", "LT_CodArt", "LT_Unite"]*/
)

@Serializable
data class LigneTicket(
    @PrimaryKey(autoGenerate = true)
    @Transient
    val id: Long = 0,

    @SerialName("CB1")
    @ColumnInfo(name = "CB1")
    val cB1: String? = "",


    @SerialName("CB2")
    @ColumnInfo(name = "CB2")
    val cB2: String? = "",

    @SerialName("CMP_Globale")
    @ColumnInfo(name = "CMP_Globale")
    val cMPGlobale: String? = "",

    @SerialName("CodeBarFils")
    @ColumnInfo(name = "CodeBarFils")
    val codeBarFils: String = "null",

    @SerialName("DD_Garantie")
    @ColumnInfo(name = "DD_Garantie")
    val dDGarantie: String? = "",

    @SerialName("DDmM")
    @ColumnInfo(name = "DDmM")
    val dDmM: String? = "",

    @SerialName("DF_Garantie")
    @ColumnInfo(name = "DF_Garantie")
    val dFGarantie: String? = "",

    @SerialName("Depart")
    @ColumnInfo(name = "Depart")
    val depart: String? = "",

    @SerialName("Etat_LT")
    @ColumnInfo(name = "Etat_LT")
    val etatLT: String? = "",

    @SerialName("exportM")
    @ColumnInfo(name = "exportM")
    val exportM: String? = "",

    @SerialName("Final")
    @ColumnInfo(name = "Final")
    val `final`: String? = "",

    @SerialName("LT_Annuler")
    @ColumnInfo(name = "LT_Annuler")
    val lTAnnuler: String = "",

    @SerialName("LT_BonEntree")
    @ColumnInfo(name = "LT_BonEntree")
    val lTBonEntree: String? = "",

    @SerialName("LT_BonEntreeExerc")
    @ColumnInfo(name = "LT_BonEntreeExerc")
    val lTBonEntreeExerc: String? = "",

    @SerialName("LT_CodArt")
    @ColumnInfo(name = "LT_CodArt")
    val lTCodArt: String = "",

    @SerialName("LT_CodeGroupe")
    @ColumnInfo(name = "LT_CodeGroupe")
    val lTCodeGroupe: String? = "",

    @SerialName("LT_Commande")
    @ColumnInfo(name = "LT_Commande")
    val lTCommande: String = "",

    @SerialName("LT_Commentaire")
    @ColumnInfo(name = "LT_Commentaire")
    val lTCommentaire: String? = "",

    @SerialName("LT_DDm")
    @ColumnInfo(name = "LT_DDm")
    val lTDDm: String = "",

    @SerialName("LT_Exerc")
    @ColumnInfo(name = "LT_Exerc")
    val lTExerc: String = "",

    @SerialName("LT_ExercFacture")
    @ColumnInfo(name = "LT_ExercFacture")
    val lTExercFacture: String? = "",

    @SerialName("LT_export")
    @ColumnInfo(name = "LT_export")
    val lTExport: String? = "",

    @SerialName("LT_IdCarnet")
    @ColumnInfo(name = "LT_IdCarnet")
    val lTIdCarnet: String = "",

    @SerialName("LT_MtHT")
    @ColumnInfo(name = "LT_MtHT")
    val lTMtHT: String = "",

    @SerialName("LT_MtTTC")
    @ColumnInfo(name = "LT_MtTTC")
    val lTMtTTC: String = "",

    @SerialName("LT_NumFacture")
    @ColumnInfo(name = "LT_NumFacture")
    val lTNumFacture: String? = "",

    @SerialName("LT_NumOrdre")
    @ColumnInfo(name = "LT_NumOrdre")
    val lTNumOrdre: String = "",

    @SerialName("LT_NumTicket")
    @ColumnInfo(name = "LT_NumTicket")
    val lTNumTicket: String = "",

    @SerialName("LT_NumTicket_M")
    @ColumnInfo(name = "LT_NumTicket_M")
    val lTNumTicketM: String = "",

    @SerialName("LT_NumeroBL")
    @ColumnInfo(name = "LT_NumeroBL")
    val lTNumeroBL: String? = "",

    @SerialName("LT_Ordre")
    @ColumnInfo(name = "LT_Ordre")
    val lTOrdre: String? = "",

    @SerialName("LT_Ordresupp")
    @ColumnInfo(name = "LT_Ordresupp")
    val lTOrdresupp: String? = "",

    @SerialName("LT_PACHAT")
    @ColumnInfo(name = "LT_PACHAT")
    val lTPACHAT: String = "",

    @SerialName("LT_PACHATTC")
    @ColumnInfo(name = "LT_PACHATTC")
    val lTPACHATTC: String? = "",

    @SerialName("LT_Pachat_PrixFacturee")
    @ColumnInfo(name = "LT_Pachat_PrixFacturee")
    val lTPachatPrixFacturee: String? = "",

    @SerialName("LT_Pachat_Res")
    @ColumnInfo(name = "LT_Pachat_Res")
    val lTPachatRes: String? = "",

    @SerialName("LT_PrixEncaisse")
    @ColumnInfo(name = "LT_PrixEncaisse")
    val lTPrixEncaisse: String = "",

    @SerialName("LT_PrixVente")
    @ColumnInfo(name = "LT_PrixVente")
    val lTPrixVente: String = "",

    @SerialName("LT_Qte")
    @ColumnInfo(name = "LT_Qte")
    val lTQte: String = "",

    @SerialName("LT_QteFacturee")
    @ColumnInfo(name = "LT_QteFacturee")
    val lTQteFacturee: String? = "",

    @SerialName("LT_QtePiece")
    @ColumnInfo(name = "LT_QtePiece")
    val lTQtePiece: String = "",

    @SerialName("LT_Remise")
    @ColumnInfo(name = "LT_Remise")
    val lTRemise: String = "",

    @SerialName("LT_Source")
    @ColumnInfo(name = "LT_Source")
    val lTSource: String? = "",

    @SerialName("LT_station")
    @ColumnInfo(name = "LT_station")
    val lTStation: String? = "",

    @SerialName("LT_TVA")
    @ColumnInfo(name = "LT_TVA")
    val lTTVA: String = "",

    @SerialName("LT_Tarif")
    @ColumnInfo(name = "LT_Tarif")
    val lTTarif: String = "",

    @SerialName("LT_Taux_Remise")
    @ColumnInfo(name = "LT_Taux_Remise")
    val lTTauxRemise: String = "",

    @SerialName("LT_Unite")
    @ColumnInfo(name = "LT_Unite")
    val lTUnite: String = "",

    @SerialName("LT_user")
    @ColumnInfo(name = "LT_user")
    val lTUser: String? = "",


    @SerialName("NumSerie")
    @ColumnInfo(name = "NumSerie")
    val numSerie: String? = "",

    @SerialName("Observation_Garantie")
    @ColumnInfo(name = "Observation_Garantie")
    val observationGarantie: String? = "",


    @SerialName("Prestataire")
    @ColumnInfo(name = "Prestataire")
    val prestataire: String? = "",

    @SerialName("authorizedDiscount")
    @ColumnInfo(name = "authorizedDiscount")
    val authorizedDiscount: Boolean? = null


) : BaseModel()

