import java.text.SimpleDateFormat
import java.util.Date

plugins {
    alias(libs.plugins.androidApplication)
    alias(libs.plugins.hilt.android)
    alias(libs.plugins.mapsplatform)
    alias(libs.plugins.kotlin)
    alias(libs.plugins.ksp)
    alias(libs.plugins.google.services)
    alias(libs.plugins.firebase.crashlytics)

    alias(libs.plugins.compose.compiler)
  //  id ("kotlin-android")

    // alias(libs.plugins.serializationX)
  id ("org.jetbrains.kotlin.plugin.serialization")
}


//apply("${project.rootDir}/buildscripts/toml-updater-config.gradle")






android {
    val date = SimpleDateFormat("dd-MM-yyyy_hh-mm").format(Date())
    val formattedDate = date.format("dd.MM.yyyy_HH.mm")


    val versionMajor = libs.versions.versionMajor.get().toInt()
    val versionMinor = libs.versions.versionMinor.get().toInt()
    val versionPatch = libs.versions.versionPatch.get().toInt()



    val minSdkVersion = libs.versions.minSdk.get().toInt()
    val targetSdkVersion = libs.versions.targetSdk.get().toInt()

    val code = versionMajor * 10000 + versionMinor * 100 + versionPatch + (minSdkVersion * targetSdkVersion)


    val name = "${versionMajor}.${versionMinor}.${versionPatch} "


    namespace = libs.versions.packagename.get()
    compileSdk = targetSdkVersion

    defaultConfig {
        applicationId = libs.versions.packagename.get()
        minSdk= minSdkVersion
        targetSdk = targetSdkVersion
        versionCode = code
        versionName = name
        multiDexEnabled = true
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

   buildOutputs.all {
        val variantOutputImpl = this as com.android.build.gradle.internal.api.BaseVariantOutputImpl
        variantOutputImpl.outputFileName =  libs.versions.packagename.get()  + "_" + code + "_" + name + "_" + formattedDate +".apk"
    }

    buildTypes {

        release {
          //  isMinifyEnabled = true
            proguardFiles (
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro")


            buildConfigField("long", "VERSION_CODE", "${defaultConfig.versionCode}")
            buildConfigField("String","VERSION_NAME","\"${defaultConfig.versionName}\"")
        }

        debug {
            isDebuggable = true // to test app performance in release mode

            isMinifyEnabled = false


            buildConfigField("long", "VERSION_CODE", "${defaultConfig.versionCode}")
            buildConfigField("String","VERSION_NAME","\"${defaultConfig.versionName}\"")
        }
    }
    compileOptions {
        // For AGP 4.1+
        isCoreLibraryDesugaringEnabled = true
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = libs.versions.jvmTarget.get()
    }
    buildFeatures {
        compose = true
        buildConfig = true
    }



    packaging {
        resources.excludes.add("META-INF/versions/9/previous-compilation-data.bin")
        resources.excludes.add("META-INF/native-image/reflect-config.json") // added for itextpdf = "8.0.4" try to remove for next versions
        resources.excludes.add("META-INF/native-image/resource-config.json")// itextpdf = "8.0.4"

        // Handle duplicate files from libraries with same namespace
        resources.pickFirsts.add("AndroidManifest.xml")
    }



    testOptions {
        unitTests {
            isReturnDefaultValues = true
            isIncludeAndroidResources = true
        }
        unitTests.all {
            it.useJUnitPlatform()
        }
    }

    // Tests can be Robolectric or instrumented tests
    // sourceSets configuration removed - using default test directories


}




configurations.all {
    resolutionStrategy {
        force("androidx.test.ext:junit:1.2.1")
        force("androidx.test:runner:1.6.2")
    }
}

dependencies {

    // Dependency constraints to resolve version conflicts
    constraints {
        implementation("androidx.test.ext:junit:1.2.1") {
            because("Force consistent test library versions")
        }
    }

    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar", "*.aar"))))

    implementation(project(":countrycodepicker"))
    implementation(project(":blovedreampda"))
    implementation(project(":honeywellbarcodereader"))
    //implementation(project(":escpos4k")) //  implementation("cz.multiplatform.escpos4k:escpos4k:0.3.0")

    implementation ("com.sunmi:printerlibrary:1.0.18")
    implementation ("com.sunmi:printerx:1.0.17")


  //  implementation(files("libs/Blovedream_SDK_release_v2.2.1.jar"))

    implementation (libs.accompanist.adaptive)


 //   debugImplementation("com.squareup.leakcanary:leakcanary-android:2.14")
    implementation (libs.core.ktx)
    implementation (libs.lifecycle.ktx)
    implementation (libs.androidx.activity)

    implementation(platform(libs.compose.bom))
    implementation(libs.bundles.compose)


    implementation(libs.androidx.lifecycle.service)
    implementation(libs.androidx.runner)
    implementation(libs.androidx.window.core)



    implementation(libs.androidx.compose.material3.adaptive)
    implementation(libs.androidx.compose.material3.adaptive.navigation)
    implementation(libs.androidx.compose.material3.adaptive.navigation.layout)
    implementation(libs.androidx.compose.material3.adaptive.navigation.suite)


    // Override Material Design 3 library version with a pre-release version
    //implementation (libs.material3)


    implementation(libs.kotlin.test.junit)

    testImplementation (libs.mockk)
    testImplementation(libs.slf4j.simple)

    testImplementation (libs.kotlinx.coroutines.test)

    testImplementation(libs.kotest.assertions.core)
    testImplementation(libs.kotest.framework.engine)
    testImplementation(libs.kotest.framework.datatest)
    testImplementation(libs.kotest.property)
    testImplementation(libs.kotest.runner.junit5)



    androidTestImplementation ("androidx.test.ext:junit:1.2.1")
    androidTestImplementation (libs.espresso.core)

    // WorkManager
    implementation (libs.workruntime)
    //Hilt extension
    implementation (libs.hiltwork)

    // compose viewmodel
    implementation (libs.androidx.lifecycle)
    implementation(libs.androidx.navigation)


    /** Dagger hilt */
    implementation (libs.dagger.hilt.android)
    implementation (libs.dagger.hilt.navigation.compose)
    ksp (libs.dagger.hilt.compiler)
    ksp (libs.dagger.hilt.androidx.compiler)


    /** DataStore*/
    implementation (libs.datastore.preferences)
    implementation("androidx.datastore:datastore-preferences:1.1.6")
 implementation ("com.sunmi:printerx:latest.release")

    implementation (libs.room.runtime)
    ksp (libs.room.compiler)
    implementation (libs.room.ktx)

    implementation(libs.androidx.paging.compose)
    implementation(libs.androidx.paging.runtime)
    implementation(libs.androidx.room.paging)
  //  ksp (libs.room.paging.compiler)


    // ktor client
    implementation (libs.ktor.client.core)
    implementation (libs.ktor.client.android)
   // implementation ("io.ktor:ktor-client-serialization:2.2.2")
    //to remove rep with kotn serialisation
    implementation (libs.ktor.serialization)
    implementation (libs.ktor.client.contentNegotiation)

    implementation (libs.ktor.client.logging)
//

    // kotlin serialization
    implementation (libs.kotlinx.serialization)






    //Custom Toast
    implementation (libs.sonnerToast)

    // Location Services
    implementation (libs.android.gms.location)



    //lottiefiles
    implementation (libs.lottie)

    //permission handling in compose
    implementation (libs.permissions)



    //  Camerax dependencies
    implementation (libs.camera.core)
    implementation (libs.camera.camera2)
    implementation (libs.camera.lifecycle)
    implementation (libs.camera.view)
    implementation (libs.camera.extensions)

    //MLKit
    implementation (libs.camera.mlkit.vision)

// Barcode model
    implementation (libs.barcode.scanning)
// TO GENERATE BARCODE MAYBE SEE IF  I CAN USE : com.google.mlkit:barcode-scanning
    implementation (libs.zxing)

    //Coil
    implementation (libs.coil)


    //Pager
   // implementation (libs.pager)

    //Splash Screen
    implementation (libs.core.splashscreen)


    //date Time
    implementation(libs.kotlinx.datetime)
    coreLibraryDesugaring(libs.desugar)


    implementation (libs.androidx.lifecycle.runtime.compose)


    //implementation (libs.charts)
    implementation (libs.graphscomposable)


   //google map
    implementation (libs.android.gms.map)
    implementation (libs.android.gms.map.compose)
    implementation (libs.android.gms.map.compose.utils)
    implementation (libs.android.gms.map.compose.widgets)


    //wifi print
    implementation (libs.itextpdf)
    implementation (libs.itextpdf.kernel)
    implementation (libs.itextpdf.layout)




    //firebase
    implementation(platform(libs.firebas.bom))
    implementation(libs.bundles.firebase)

    //in app update
    implementation(libs.update.app)
    implementation(libs.update.app.ktx)

   // implementation ("androidx.activity:activity:1.9.1")


    implementation(libs.kmp.ui.kit.android)



    implementation(libs.escposkmp)
}




hilt {
    enableAggregatingTask = true
}

// Task to help debug dependency conflicts
tasks.register("debugDependencies") {
    doLast {
        configurations.forEach { config ->
            if (config.isCanBeResolved) {
                println("Configuration: ${config.name}")
                try {
                    config.resolvedConfiguration.resolvedArtifacts.forEach { artifact ->
                        if (artifact.moduleVersion.id.name.contains("junit")) {
                            println("  ${artifact.moduleVersion.id}")
                        }
                    }
                } catch (e: Exception) {
                    println("  Could not resolve: ${e.message}")
                }
            }
        }
    }
}




