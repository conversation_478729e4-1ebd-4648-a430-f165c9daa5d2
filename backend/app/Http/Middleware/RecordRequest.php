<?php


namespace App\Http\Middleware;


use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class RecordRequest
{

    public function handle(Request $request, Closure $next)
    {

        try {
            $content = "-----" . $request->fullUrl() . "-----\n\n";
            $content .= "-----" . $request->userAgent() . "-----\n\n";
            $content .= "-----" . json_encode($request->getClientIps()) . "-----\n\n";
            $content .= "-----" . Carbon::now() . "-----\n\n";
            $content .= print_r($request->getContent(), true);

            $now = Carbon::now();
            Storage::put($now->format('y_m_d') . '/' . $now->timestamp . '_' . ((string)Str::uuid()) . '.txt',
                $content);

        } catch (\Exception $e) {

        }

        return $next($request);
    }

}