package com.asmtunis.procaisseinventory.auth.base_config.data.local.repository

import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.base_config.data.local.dao.BaseConfigDAO
import kotlinx.coroutines.flow.Flow

class BaseConfigRepositoryImpl(
    private val baseConfigDAO: BaseConfigDAO
) : BaseConfigLocalRepository {
    override fun upsert(value: BaseConfig) = baseConfigDAO.upsert(value)

    override fun upsertAll(value: List<BaseConfig>) = baseConfigDAO.upsertAll(value)

    override fun deletebyid(value: String) = baseConfigDAO.deleteFidCardById(value)
    override fun deletebyProduit(value: String) = baseConfigDAO.deleteByProduit(value)


    override fun deleteAll() = baseConfigDAO.deleteAll()

    override fun getAll(): Flow<List<BaseConfig>> = baseConfigDAO.get()

    override fun getByTwoID(idbaseconfig: String, password: String): Flow<BaseConfig> = baseConfigDAO.getById(idbaseconfig, password)
}
