package com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.inv.repository

import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.Inventaire
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.inv.dao.InventaireDAO
import kotlinx.coroutines.flow.Flow


class InventaireLocalRepositoryImpl(
        private val inventaireDAO: InventaireDAO
    ) : InventaireLocalRepository {
    override fun upsertAll(value: List<Inventaire>) = inventaireDAO.insertAll(value)

    override fun upsert(value: Inventaire) = inventaireDAO.insert(value)

    override fun setToInserted(codeM: String) = inventaireDAO.setToInserted(codeM)

    override fun deleteAll() = inventaireDAO.deleteAll()

    override fun delete(inventaire: Inventaire)  = inventaireDAO.delete(inventaire = inventaire)

    override fun getAll(): Flow<List<Inventaire>>  = inventaireDAO.all

    override fun count(): Flow<Int> = inventaireDAO.count()
    override fun counts(): Flow<Map<Inventaire, List<LigneInventaire>>> =   inventaireDAO.counts()

    override fun getNotSync(): Flow<List<Inventaire>> = inventaireDAO.nonSync

    override fun updateNumAndEtat(code: String, codeLocal: String, etat: String) = inventaireDAO.updateNumAndEtat(code, codeLocal, etat)

    override fun noSynced(): Flow<Map<Inventaire, List<LigneInventaire>>> = inventaireDAO.noSynced()

    override fun getNewCode(prefix: String): Flow<String>  = inventaireDAO.getNewCode(prefix)

    override fun getAllFiltred(
        isAsc: Int,
        filterByEtatInv: String,
        sortBy: String
    ): Flow<Map<Inventaire, List<LigneInventaire>>>  =
        inventaireDAO.getAllFiltred(
            isAsc = isAsc,
            filterByEtatInv = filterByEtatInv,
            sortBy = sortBy
        )

    override fun filterByInvCode(
        searchString: String,
        filterByEtatInv: String,
        sortBy: String,
        isAsc: Int
    ): Flow<Map<Inventaire, List<LigneInventaire>>>  =
        inventaireDAO.filterByInvCode(
            searchString = searchString,
            filterByEtatInv = filterByEtatInv,
            sortBy = sortBy,
            isAsc = isAsc
        )
}