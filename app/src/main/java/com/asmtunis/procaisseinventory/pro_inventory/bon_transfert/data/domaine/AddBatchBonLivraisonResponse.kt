package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class AddBatchBonLivraisonResponse(
    @SerialName("BON_Trans_Exerc")
    val bonTransExerc: String,
    @SerialName("BON_Trans_Num")
    val bonTransNum: String,

    @SerialName("BON_Trans_Num_M")
    val bonTransNumM: String,

    @SerialName("code")
    val code: Int,
    @SerialName("message")
    val message: String
)