package com.asmtunis.procaisseinventory.shared_ui_components.tables.five_column

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.shared_ui_components.CustomModifiers.customWidth
import com.asmtunis.procaisseinventory.shared_ui_components.swipe_actions.swipe_to_delete.SwipeToDeleteContainer
import com.asmtunis.procaisseinventory.shared_ui_components.swipe_actions.swipe_to_delete.rememberDeleteState
import com.simapps.ui_kit.ModifiersUtils.detectTapGestures
import kotlinx.coroutines.delay


@Composable
fun FiveColumnTable(
    showFilterLine: Boolean = false,
    onShowFilterLineChange: (Boolean) -> Unit = {},
    fiterValue: String = "",
    onFilterValueChange: (String) -> Unit = {},
    selectedListArticle : List<SelectedArticle>,
    onLongPress: (SelectedArticle) -> Unit = {},
    onSwipeToDelete: (SelectedArticle) -> Unit = {},
    onTap: () -> Unit = {},
    canModify: Boolean = false,
    onPress: (SelectedArticle) -> Unit = {},
    rowTitls: List<String> = listOf("N°", "Article", "Prix (Dt)", "Qté", "Total (Dt)"),
    firstColumn: (SelectedArticle) -> String,
    secondColumn: (SelectedArticle) -> String,
    thirdColumn: (SelectedArticle) -> String,
    forthColumn: (SelectedArticle) -> String,
    infoText: (SelectedArticle) -> String
) {

    val keyboardController = LocalSoftwareKeyboardController.current
    val focusRequester = remember { FocusRequester() }

    LaunchedEffect(key1 = focusRequester, key2 = showFilterLine) {
        if(!showFilterLine) return@LaunchedEffect
        focusRequester.requestFocus()
        delay(100) // Make sure you have delay here
        keyboardController?.show()

    }

    val density = LocalDensity.current

    val listState = rememberLazyListState()




    HorizontalDivider(thickness = 2.dp, color = MaterialTheme.colorScheme.outline)
    FiveTableHeaderTitles(
        rowTitls = rowTitls,
        showFilterLine = showFilterLine,
        onShowFilterLineChange = { onShowFilterLineChange(it) }
    )

    AnimatedVisibility(
        visible = showFilterLine,
        enter = slideInVertically { with(density) { 40.dp.roundToPx() } } + fadeIn(),
        exit = fadeOut(animationSpec = keyframes { this.durationMillis = 120 })
    ) {
        OutlinedTextField(
            value = fiterValue,
            onValueChange = onFilterValueChange,
            label = { Text(stringResource(id = R.string.filter)) },
            modifier = Modifier.focusRequester(focusRequester).fillMaxWidth().padding(18.dp),
            trailingIcon = {
                Row(
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    AnimatedVisibility(
                        visible = fiterValue.isNotEmpty(),
                        enter = fadeIn(),
                        exit = fadeOut(),
                    ) {
                        IconButton(onClick = { onFilterValueChange("") }) {
                            Icon(
                                imageVector = Icons.Filled.Clear,
                                contentDescription = stringResource(id = R.string.your_divice_id),
                            )
                        }
                    }


                }
            },
            keyboardActions = KeyboardActions(
                onSearch = {
                    keyboardController?.hide()
                }
            ),
            keyboardOptions = KeyboardOptions(
                imeAction = ImeAction.Search,
                keyboardType = KeyboardType.Password
            )
        )
    }
    HorizontalDivider(thickness = 2.dp, color = MaterialTheme.colorScheme.outline)

    val resources = LocalContext.current.resources
    val screenHeightDp = resources.displayMetrics.heightPixels/ resources.displayMetrics.density


    LazyColumn(
        verticalArrangement = Arrangement.Top,
        horizontalAlignment = Alignment.CenterHorizontally,
        //userScrollEnabled = false,
        state = listState,
        modifier = Modifier.fillMaxWidth()
            .padding(start = 3.dp, end = 3.dp)
             .heightIn(min = 40.dp, max = screenHeightDp.dp - (screenHeightDp * 0.45).dp )
       //     .heightIn(min = 40.dp, max = screenHeightDp.dp - 300.dp )
    ) {
        items(
            items = selectedListArticle,
           key = { item -> "${item.article.aRTCode}_${item.prixCaisse}_${item.discount}_${item.quantity}_${selectedListArticle.indexOf(item)}" } // Unique key with index to prevent duplicates
        ) { item ->
          /*  SwipeToReveal(
                canModify = canModify,
                modifier = Modifier.fillMaxWidth().animateItemPlacement(),
                onDelete = {
                    onSwipeToDelete(item)
                },
                content = {
                Column (
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .detectTapGestures(
                            onPress = {
                                if (canModify)
                                    onPress(item)


                            },
                            onDoubleTap = {

                            },
                            onLongPress = {
                                if (canModify)
                                    onLongPress(item)


                            },
                            onTap = {
                                if (canModify)
                                    onTap()
                            }
                        )
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        modifier = Modifier
                            .heightIn(40.dp, 100.dp)
                            .fillMaxWidth()

                    ) {
                        Text(
                            text = (selectedListArticle.indexOf(item)+1).toString(),
                            color = MaterialTheme.colorScheme.outlineVariant,
                            textAlign = TextAlign.Center,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.fillMaxWidth(0.05f)
                        )

                        Text(
                            text = firstColumn(item),
                            textAlign = TextAlign.Center,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.fillMaxWidth(0.4f)
                        )


                        Text(
                            text = secondColumn(item),
                            style = MaterialTheme.typography.bodySmall,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth(0.2f)
                        )

                        Text(
                            text = thirdColumn(item),
                            textAlign = TextAlign.Center,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.fillMaxWidth(0.1f)
                        )



                        Text(
                            text = forthColumn(item),
                            style = MaterialTheme.typography.bodySmall,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth(0.2f)
                        )
                    }


                    if (infoText(item).isNotEmpty())
                        Text(
                            text = infoText(item),
                            style = MaterialTheme.typography.bodySmall,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth(),
                            color = MaterialTheme.colorScheme.secondary
                        )
                    HorizontalDivider(color = MaterialTheme.colorScheme.outline)

                }
            })*/
           val currentItem by rememberUpdatedState(item)
            SwipeToDeleteContainer (
                canModify = canModify,
                state = rememberDeleteState(onDelete = { onSwipeToDelete(currentItem) }),
                content = {
                    Column (
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier
                            .detectTapGestures(
                                key1 = item,
                                onPress = { if (canModify) onPress(item) },
                                onDoubleTap = {},
                                onLongPress = { if (canModify) onLongPress(item) },
                                onTap = { if (canModify) onTap() }
                            )
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.SpaceEvenly,
                            modifier = Modifier
                                .heightIn(40.dp, 100.dp)
                                .fillMaxWidth()
                        ) {
                            Text(
                                text = (selectedListArticle.indexOf(item)+1).toString(),
                                color = MaterialTheme.colorScheme.outlineVariant,
                                textAlign = TextAlign.Center,
                                style = MaterialTheme.typography.bodySmall,
                                modifier = Modifier.customWidth(0.05f)
                            )

                            Text(
                                text = firstColumn(item),
                                textAlign = TextAlign.Center,
                                style = MaterialTheme.typography.bodySmall,
                                modifier = Modifier.customWidth(0.4f)
                            )


                            Text(
                                text = secondColumn(item),
                                style = MaterialTheme.typography.bodySmall,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.customWidth(0.2f)
                            )

                            Text(
                                text = thirdColumn(item),
                                textAlign = TextAlign.Center,
                                style = MaterialTheme.typography.bodySmall,
                                modifier = Modifier.customWidth(0.1f)
                            )



                            Text(
                                text = forthColumn(item),
                                style = MaterialTheme.typography.bodySmall,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.customWidth(0.25f)
                            )
                        }


                        if (infoText(item).isNotEmpty())
                            Text(
                                text = infoText(item),
                                style = MaterialTheme.typography.bodySmall,
                                textAlign = TextAlign.Start,
                                modifier = Modifier.fillMaxWidth(),
                                color = MaterialTheme.colorScheme.secondary
                            )
                        HorizontalDivider(color = MaterialTheme.colorScheme.outline)

                    }
                }
            )



        }
    }


}

