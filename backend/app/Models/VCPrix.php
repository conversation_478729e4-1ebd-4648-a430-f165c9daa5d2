<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property string $CodeVCPrix
 * @property string $CodeVCPrixM
 * @property string $CodeArtLocal
 * @property string $ArticleConcur
 * @property string $DateOp
 * @property string $CodeConcur
 * @property string $NoteOp
 * @property float $PrixConcur
 * @property int $CodeUser
 * @property string $InfoOp1
 * @property string $CodeTypeCom
 */
class VCPrix extends Model
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'VCPrix';

    /**
     * The primary key for the model.
     * 
     * @var string
     */
    protected $primaryKey = 'CodeVCPrix';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['CodeVCPrixM', 'CodeArtLocal', 'ArticleConcur', 'DateOp', 'CodeConcur', 'NoteOp', 'PrixConcur', 'CodeUser', 'InfoOp1', 'CodeTypeCom'];

    public function VC_Image()
    {
        $this->hasMany(VCImage::class);
    }
}
