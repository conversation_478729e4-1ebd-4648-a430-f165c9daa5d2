<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Symfony\Component\HttpFoundation\Response;

class BaseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */

    public function rules()
    {
        return [
            'object' => 'required|array',
            'object.*.idMobile' => 'nullable|string|max:255',
            'object.*.libelle' => 'nullable|string|max:255',
            'object.*.j_codeUser' => 'nullable|string|max:255',
            'object.*.isDefault' => 'nullable|boolean',
            'object.*.active' => 'nullable|boolean',
            'object.*.idFamilleParente' => 'nullable|string|max:255',
            'object.*.code' => 'nullable|string|max:255',
            'object.*.table' => 'nullable|string|max:255',
            'object.*.id' => 'nullable|string|max:255',
            'object.*.file' => 'nullable|string|regex:/^data:image\/[a-zA-Z]+;base64,/',
            'object.*.name' => 'nullable|string|max:255',
            'object.*.idArticle' => 'nullable|string|max:255',
            'object.*.libelleArticle' => 'nullable|string|max:255',
            'object.mois' => 'nullable|integer',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY)
        );
    }
}
