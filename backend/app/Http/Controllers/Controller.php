<?php

namespace App\Http\Controllers;

use App\Traits\ApiResponser;
use App\Traits\BackUpHelper;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

/**
 * @OA\Info(
 *     title="Unite Mobile API",
 *     version="1.0.0",
 *     description="API for managing unites.",
 *     @OA\Contact(
 *         email="<EMAIL>"
 *     ),
 *     @OA\License(
 *         name="MIT",
 *         url="https://opensource.org/licenses/MIT"
 *     )
 * )
 */

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests ,ApiResponser, BackUpHelper;
    
}
