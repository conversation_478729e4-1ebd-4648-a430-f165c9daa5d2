package com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.local.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.TICKET_RAYON_TABLE
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.domaine.TicketRayon
import kotlinx.coroutines.flow.Flow


@Dao
interface TicketRayonDAO {
    @get:Query("SELECT * FROM $TICKET_RAYON_TABLE ORDER By timestamp DESC")
    val all: Flow<List<TicketRayon>>

    @get:Query("SELECT * FROM $TICKET_RAYON_TABLE ORDER By timestamp DESC")
    val allLiveData: Flow<List<TicketRayon>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<TicketRayon>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(items: TicketRayon)

    @Delete
    fun delete(ticketRayon: TicketRayon)

    @Query("DELETE FROM $TICKET_RAYON_TABLE")
    fun deleteAll()

    @Query("SELECT * FROM $TICKET_RAYON_TABLE where ART_Code = :code LIMIT 1")
    fun getByConsultationBarCode(code: String): Flow<TicketRayon>

    @Query("SELECT COUNT(*) FROM $TICKET_RAYON_TABLE  where  isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    fun count(): Flow<Int>

    @get:Query("SELECT * FROM  $TICKET_RAYON_TABLE  where  isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ORDER By timestamp DESC")
    val nonSync: Flow<List<TicketRayon>>


    @Query("UPDATE $TICKET_RAYON_TABLE SET isSync = 1, Status= 'SELECTED' where ART_Code = :code")
    fun updateSyncTicketRayon(code: String)
}
