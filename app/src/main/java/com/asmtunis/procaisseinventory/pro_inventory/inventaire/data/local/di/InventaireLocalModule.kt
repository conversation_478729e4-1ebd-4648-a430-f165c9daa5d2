package com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.inv.dao.InventaireDAO
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.inv.repository.InventaireLocalRepository
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.inv.repository.InventaireLocalRepositoryImpl
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.ligne_inv.dao.LigneInventaireDAO
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.ligne_inv.repository.LigneInventaireLocalRepository
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.ligne_inv.repository.LigneInventaireLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton



    @Module
    @InstallIn(SingletonComponent::class)
    class InventaireLocalModule {

        @Provides
        @Singleton
        fun provideInventaireDao(
            proInventoryDataBase: ProCaisseDataBase
        ) = proInventoryDataBase.inventaireDAO()

        @Provides
        @Singleton
        @Named("Inventaire")
        fun provideInventaireRepository(
            inventaireDAO: InventaireDAO
        ): InventaireLocalRepository = InventaireLocalRepositoryImpl(
            inventaireDAO = inventaireDAO
        )

        @Provides
        @Singleton
        fun provideLigneInventaireDao(
            proInventoryDataBase: ProCaisseDataBase
        ) = proInventoryDataBase.ligneInventaireDAO()

        @Provides
        @Singleton
        @Named("LigneInventaire")
        fun provideLigneInventaireRepository(
            ligneInventaireDAO: LigneInventaireDAO
        ): LigneInventaireLocalRepository = LigneInventaireLocalRepositoryImpl(
            ligneInventaireDAO = ligneInventaireDAO
        )

}