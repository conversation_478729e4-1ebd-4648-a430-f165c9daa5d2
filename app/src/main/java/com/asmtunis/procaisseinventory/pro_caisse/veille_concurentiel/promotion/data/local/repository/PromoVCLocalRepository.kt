package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.local.repository


import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVCWithImages
import kotlinx.coroutines.flow.Flow



interface PromoVCLocalRepository {
    fun upsertAll(value: List<PromoVC>)
    fun upsert(value: PromoVC)

    fun noSyncedToAddOrUpdate(): Flow<List<PromoVC>>
    fun noSyncedToDelete(): Flow<List<PromoVC>>


    fun updateCloudCode(code: String, codeMobile: String)

    fun restDeleted(code: String,status : String, isSync:Boolean)
    fun setDeleted(code: String, codeMobile: String)
    fun deleteAll()

    fun deleteByCode(codeAutre: String)

    fun getAll(): Flow<List<PromoVC>>


    fun filterByNum(searchString: String, filterByTypComm : String, sortBy: String, isAsc: Int): Flow<List<PromoVCWithImages>>
    fun filterByArtConcurrent(searchString: String, filterByTypComm : String, sortBy: String?, isAsc: Int?): Flow<List<PromoVCWithImages>>
    fun filterByCodeArtLocal(searchString: String, sortBy: String, filterByTypComm : String, isAsc: Int): Flow<List<PromoVCWithImages>>
    fun getAllFiltred(isAsc: Int, filterByTypComm : String, sortBy: String): Flow<List<PromoVCWithImages>>

}