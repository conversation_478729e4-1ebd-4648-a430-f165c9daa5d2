package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class ResponseVC(
    @SerialName("CodeAutre")
    val CodeAutre: String = "",
    @SerialName("CodeVCPrix")
    val CodeVCPrix: String = "",
    @SerialName("CodeVCPromo")
    val CodeVCPromo: String = "",
    @SerialName("CodeVCLanP")
    val CodeVCLanP: String = "",
    @SerialName("message")
    val message: String = "",
    @SerialName("DELETED")
    val DELETED: String = "",
    @SerialName("code")
    val code: String = "",
    @SerialName("codeM")
    val codeM: String = "",
    @SerialName("table")
    val table: String = ""
)