<?php
namespace App\Traits;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;

trait BackUpHelper  {

    protected function makeBackup($idBaseConfig, $module, $fileName, $data,$method): void
    {
        $cusLog = new Logger('stack');
        try {
            $cusLog->pushHandler(new StreamHandler(storage_path('logs/'.$module .'/'.$idBaseConfig.'/'.$fileName.'.log')));
        } catch (\Exception $e) {
        }
        $cusLog->info($method, $data);
    }
}
