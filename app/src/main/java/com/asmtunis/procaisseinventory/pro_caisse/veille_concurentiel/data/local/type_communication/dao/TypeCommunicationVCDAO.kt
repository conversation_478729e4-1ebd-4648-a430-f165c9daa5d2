package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.type_communication.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.TYPE_COMMUNICATION_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.TypeCommunicationVC
import kotlinx.coroutines.flow.Flow


@Dao
interface TypeCommunicationVCDAO {
    @get:Query("SELECT * FROM $TYPE_COMMUNICATION_TABLE")
    val all: Flow<List<TypeCommunicationVC>?>



    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(vcTypeCommunications: List<TypeCommunicationVC>)

    @Query("delete from $TYPE_COMMUNICATION_TABLE")
    fun deleteAll()
}