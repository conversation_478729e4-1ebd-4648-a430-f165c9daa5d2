<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

class FirebaseMessaging extends Controller
{
    function sendNotification($FcmToken, $FcmTitle, $FcmBody) {
        $url = 'https://fcm.googleapis.com/fcm/send';
         $serverKey
            = 'AAAAWlS6qXw:APA91bHFjfPY2aaUVUU_nQqJb-n8Wsh8l6uC3vP-HsK5N9RzhLKaTpMD4hAJjJ-JFgkmnC8BfWgsXnda2rFdoabCbN1_HH1z2QK6VEW7qmKXIitk7XVKB0owVJyrvzK9LAPFjAgcDCbN';

        $data = [
            "registration_ids" => $FcmToken,
            "notification" => [
                "title" => $FcmTitle,
                "body" => $FcmBody,
            ]
        ];

        $encodedData = json_encode($data);

        $headers = [
            'Authorization:key=' . $serverKey,
            'Content-Type: application/json',
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        // Disabling SSL Certificate support temporarly
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $encodedData);

        // Execute post
        return   $result = curl_exec($ch);

        // Close connection
        curl_close($ch);

        // FCM response
        //    dd($result);
    }

}
