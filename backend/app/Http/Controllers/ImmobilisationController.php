<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use Illuminate\Http\Request;

class ImmobilisationController extends Controller
{
    public function getImmobilisation(Request $request)
    {
        $data = $request->json()->all();;
        $connection = DatabaseConnection::setConnection($data);
        $immobilisation = $connection->table('client')
            ->join('TypeEmpImmo', 'client.Clt_ImoTypEmp', '=', 'TypeEmpImmo.TyEmpImCode')
            ->where('Clt_Immo', '=', true)
            ->get(["CLI_Code", "CLI_NomPren", "Clt_Immo", "Clt_ImoCodeP", "Clt_ImoTypEmp",
                "Clt_ImoCB", "TyEmpImNom","CLI_Station"]);
        return response()->json($immobilisation);
    }


    public function getBatimentByUser(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        date_default_timezone_set('Africa/Tunis');
        $clients = [];
        $idUser = null;

        if (!!Request::createFromGlobals()->hasHeader('user')) {
            $idUser = $request->header('user');
            $clients = $connection->select(" select * from client where CLI_Code in  (select CodeSit  from UserSite
                where CodeUtilisateur =" . $idUser . ")");
        }


        return response()->json($clients);
    }
}
