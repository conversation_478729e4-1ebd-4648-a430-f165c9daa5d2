<?php

namespace App\Http\Controllers;

use GuzzleHttp\Client;
use Illuminate\Http\Request;

class LicenseController extends Controller
{
    public function CheckLicense($idDevice)
    {
        $client = new Client();
        $produits = ["ProInventory Mobile", "ProCaisse Mobility"];
        $response = array();
        $finalResult = [
            "BaseConfig" => [],
            "demandes" => []
        ];
        $baseConfigs = array();
        foreach ($produits as $produit) {
            //Check Licence
            $result = $client->request('POST', 'http://as.asmhost.net/licence/public/api/checkLicense', [
                'json' => [
                    'iddevice' => $idDevice,
                    'produit' => $produit,
                ]
            ]);

            $response[$produit] = json_decode($result->getBody()->getContents(), true);
            //check demands
            if (count((array)$response[$produit]) == 0) {
                $resultGetLicences = $client->request('POST', 'http://as.asmhost.net/licence/public/api/getLicenses', [
                    'json' => [
                        'iddevice' => $idDevice,
                        'produit' => $produit,
                    ]
                ]);

                $getLicences = json_decode($resultGetLicences->getBody()->getContents(), true);

                count($getLicences['demandes']) > 0 ? $finalResult["demandes"] = $getLicences['demandes']
                    : $finalResult["demandes"];

            } else {

                $baseConfig = $client->request('POST', 'http://as.asmhost.net/licence/reclamation/WS/selection_base_config.php', [
                    'form_params' => [
                        'id_device' => $idDevice,
                        'produit' => $produit,
                    ]
                ]);

                $baseConfigs[] = json_decode($baseConfig->getBody()->getContents(), true);


            }


        }




        if (count($baseConfigs) > 1 ) {
            foreach ($this->distinctBaseConfig($baseConfigs) as $item) {
                $products = explode(';', $item['produit']);
                foreach ($products as $product) {
                    $item['licences'][] = $response[$product];
                }
                $finalResult['BaseConfig'][] = $item;
            }
        } else if (count($baseConfigs)==0) {

            return response()->json($finalResult);

        }else
        {
            foreach ($baseConfigs[0] as $baseConfig) {
                foreach ($produits as $product) {
                    if($response[$product] != null)
                    {
                        $baseConfig['licences'][] = $response[$product];

                    }
                }
                $finalResult['BaseConfig'][] = $baseConfig;
            }
        }
        return response()->json($finalResult);

    }

    public function distinctBaseConfig($array)
    {

        $test = array_merge($array[0], $array[1]);
        $merge_array = $array[0] + $array[1];
        $nameCounts = array_count_values(array_map(static function ($object) {
            return $object['dbName'];
        }, $test));

        $result = array();

        foreach ($merge_array as $item) {
            foreach ($nameCounts as $key => $value) {
                if ($item['dbName'] === $key && $value > 1) {

                    $item['produit'] = "ProInventory Mobile;ProCaisse Mobility";
                }

            }
            $result[] = $item;
        }

        return $result;

    }
}

