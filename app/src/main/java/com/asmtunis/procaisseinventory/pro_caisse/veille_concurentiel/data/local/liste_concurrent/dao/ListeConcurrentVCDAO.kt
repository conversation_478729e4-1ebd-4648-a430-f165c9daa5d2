package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.liste_concurrent.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.LISTE_CONCURRENT_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ConcurrentVC
import kotlinx.coroutines.flow.Flow


@Dao
interface ListeConcurrentVCDAO {
    @get:Query("SELECT * FROM $LISTE_CONCURRENT_TABLE")
    val all: Flow<List<ConcurrentVC>?>

    @Query("SELECT concurrent FROM $LISTE_CONCURRENT_TABLE WHERE Codeconcurrent=:code")
    fun getConcurrent(code: String): String

    @Query("SELECT Codeconcurrent FROM $LISTE_CONCURRENT_TABLE WHERE concurrent=:code")
    fun getConcurrentbyname(code: String): String

    @Query("SELECT * FROM $LISTE_CONCURRENT_TABLE WHERE Codeconcurrent=:code")
    fun getListConcurrent(code: String?): ConcurrentVC

    @Query("delete from $LISTE_CONCURRENT_TABLE")
    fun deleteAll()

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(vcListeConcurrents: List<ConcurrentVC>)
}
