<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;

class ExerciceWSMobile extends Controller
{
    public function getExercice(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $exercice = $connection->table("Exercice")
            ->where("Exercice_Etat",'=',"actif")
            ->select("Exercice_Code")->first();


        return response()->json(empty($exercice) ? null : $exercice);
    }
}
