package com.asmtunis.procaisseinventory.pro_inventory.inventaire.view_model

import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.print.PrintingData
import com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.Inventaire
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class InventairePrintViewModel @Inject constructor(
    private val sunmiPrintManager: SunmiPrintManager
) : ViewModel() {

    private val _printStatus = MutableStateFlow<String?>(null)
    val printStatus: StateFlow<String?> = _printStatus.asStateFlow()

    var isPrinting by mutableStateOf(false)
        private set

    /**
     * Print an inventory using the SUNMI printer
     */
    fun printInventaireSunmi(
        context: Context,
        inventaire: Inventaire,
        lgInventaire: List<LigneInventaire>,
        articleMapByBarCode: Map<String, Article>,
        station: Station,
        utilisateur: Utilisateur,
        printParams: PrintingData
    ) {
        viewModelScope.launch {
            isPrinting = true
            try {
                sunmiPrintManager.printInventaire(
                    context = context,
                    inventaire = inventaire,
                    lgInventaire = lgInventaire,
                    articleMapByBarCode = articleMapByBarCode,
                    station = station,
                    utilisateur = utilisateur,
                    printParams = printParams
                )
                _printStatus.value = "Printing inventory..."
            } catch (e: Exception) {
                _printStatus.value = "Error: ${e.message}"
            } finally {
                isPrinting = false
            }
        }
    }

    fun clearPrintStatus() {
        _printStatus.value = null
    }
}
