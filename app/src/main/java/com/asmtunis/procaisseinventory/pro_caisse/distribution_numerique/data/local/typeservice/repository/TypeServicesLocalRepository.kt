package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.typeservice.repository

import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypeServicesDn
import kotlinx.coroutines.flow.Flow



interface TypeServicesLocalRepository {

    fun upsert(value: TypeServicesDn)

    fun upsertAll(value: List<TypeServicesDn>)


    fun deleteAll()

    fun getAll(): Flow<List<TypeServicesDn>>

}