package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.superficie.repository


import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.SuperficieDn
import kotlinx.coroutines.flow.Flow

interface SuperficieLocalRepository {

    fun upsert(value: SuperficieDn)

    fun upsertAll(value: List<SuperficieDn>)


    fun deleteAll()

    fun getAll(): Flow<List<SuperficieDn>>

}
