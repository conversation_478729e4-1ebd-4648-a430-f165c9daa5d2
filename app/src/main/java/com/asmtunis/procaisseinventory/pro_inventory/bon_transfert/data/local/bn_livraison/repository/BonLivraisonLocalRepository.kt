package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.bn_livraison.repository

import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.BonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraisonWithArticle
import kotlinx.coroutines.flow.Flow

interface BonLivraisonLocalRepository {
    fun upsertAll(value: List<BonLivraison>)

    fun upsert(value: BonL<PERSON>raison)

    fun deleteAll()

    fun delete(bonLivraison: BonL<PERSON>raison)

    fun getNewCode(prefix: String): Flow<String>

    fun updateState(
        code: String,
        codeM: String,
        exercice: String,
    )

    fun getAll(): Flow<List<BonLivraison>>

    fun count(): Flow<Int>

    fun getNotSync(): Flow<List<BonLivraison>>

    fun noSynced(): Flow<Map<BonLivraison, List<LigneBonLivraison>>?>

    fun filterByCodeStationDestination(
        searchString: String,
        filterByStationSource: String,
        filterByStationDestination: String,
        filterByEtatBnTransfert: String,
        sortBy: String,
        isAsc: Int,
    ): Flow<Map<BonLivraison, List<LigneBonLivraisonWithArticle>>>

    fun filterByCodeStationSource(
        searchString: String,
        filterByStationSource: String,
        filterByStationDestination: String,
        filterByEtatBnTransfert: String,
        sortBy: String,
        isAsc: Int,
    ): Flow<Map<BonLivraison, List<LigneBonLivraisonWithArticle>>>

    fun filterByBontransfertNum(
        searchString: String,
        filterByStationSource: String,
        filterByStationDestination: String,
        filterByEtatBnTransfert: String,
        sortBy: String,
        isAsc: Int,
    ): Flow<Map<BonLivraison, List<LigneBonLivraisonWithArticle>>>

    fun getAllFiltred(
        isAsc: Int,
        filterByStationSource: String,
        filterByStationDestination: String,
        filterByEtatBnTransfert: String,
        sortBy: String,
    ): Flow<Map<BonLivraison, List<LigneBonLivraisonWithArticle>>>
}
