package com.asmtunis.procaisseinventory.shared_ui_components.searchview.search

import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SegmentedButton
import androidx.compose.material3.SegmentedButtonDefaults
import androidx.compose.material3.SingleChoiceSegmentedButtonRow
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R

@ExperimentalMaterial3Api
@Composable
fun SearchSection(
    modifier: Modifier = Modifier,
    filerList: Array<String>,
    listSearch: ListSearch = ListSearch.FirstSearch(),
    onFilterChange: (ListSearch) -> Unit
) {
    Column(modifier = modifier) {
        Text(
            text = stringResource(R.string.search_by),
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.outline,
            fontWeight = FontWeight.Bold
        )
                Spacer(modifier = Modifier.height(9.dp))

        SingleChoiceSegmentedButtonRow(
            modifier = Modifier.fillMaxWidth()
        ) {

            SegmentedButton(
                shape = SegmentedButtonDefaults.itemShape(index = 0, count = filerList.size),
                onClick = { onFilterChange(ListSearch.FirstSearch()) },
                selected = listSearch is ListSearch.FirstSearch
            ) {
                Text(
                    modifier = Modifier.horizontalScroll(rememberScrollState()),
                    text= filerList.first(),
                    style = MaterialTheme.typography.bodySmall,
                    softWrap = false,
                    maxLines = 1
                )
            }

            if (filerList.lastIndex != 0)  {
                if (filerList[1]!="") {
                    SegmentedButton(

                        shape = SegmentedButtonDefaults.itemShape(index = 1, count = filerList.size),
                        onClick = { onFilterChange(ListSearch.SecondSearch()) },
                        selected = listSearch is ListSearch.SecondSearch
                    ) {
                        Text(
                            modifier = Modifier.padding(3.dp).horizontalScroll(rememberScrollState()),
                            text = filerList[1],
                            style = MaterialTheme.typography.bodySmall,
                            softWrap = false,
                            maxLines = 1
                        )
                    }
                }
            }


            if (filerList.lastIndex == 2)  {// if list seach size == 2 Not 3
                if (filerList[2] != ""){
                    SegmentedButton(
                        shape = SegmentedButtonDefaults.itemShape(index = 2, count = filerList.size),
                        onClick = { onFilterChange(ListSearch.ThirdSearch()) },
                        selected = listSearch is ListSearch.ThirdSearch
                    ) {
                        Text(
                            modifier = Modifier.horizontalScroll(rememberScrollState()),
                            text = filerList[2],
                            style = MaterialTheme.typography.bodySmall,
                            softWrap = false,
                            maxLines = 1
                        )
                    }

                }
        }
        }

    }
}
