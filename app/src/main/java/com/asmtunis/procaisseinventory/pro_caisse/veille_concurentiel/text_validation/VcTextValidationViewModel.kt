package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.text_validation

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateDoubleNotZero
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateEmail
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateList
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidatePassword
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidatePhoneNumber
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateStringNotEmpty
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.TypeVc
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ConcurrentVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.TypeCommunicationVC
import kotlinx.coroutines.launch


class VcTextValidationViewModel(
    private val validateIsNotEmptyString: ValidateStringNotEmpty = ValidateStringNotEmpty(),
    private val validateList: ValidateList = ValidateList(),
    private val validateEmail: ValidateEmail = ValidateEmail(),
    private val validatePassword: ValidatePassword = ValidatePassword(),
    private val validatePhoneNumber: ValidatePhoneNumber = ValidatePhoneNumber(),
    private val validateDoubleNotZero: ValidateDoubleNotZero = ValidateDoubleNotZero()
) : ViewModel() {
    /**
     * Add VC input edit text validation
     */

    var stateAddNewVc by mutableStateOf(VcFormState())

    var validationAddVcEvents by mutableStateOf(ValidationAddNewVcEvent())




    fun resetVariable(){
        onAddNewVcEvent(VcFormEvent.produitChanged(""))
       onAddNewVcEvent(VcFormEvent.prixChanged(""))
        onAddNewVcEvent(VcFormEvent.tauxChanged(""))
        onAddNewVcEvent(VcFormEvent.noteChanged(""))




        onAddNewVcEvent(VcFormEvent.typeCommunicationChanged(TypeCommunicationVC()))
        onAddNewVcEvent(VcFormEvent.LocalProductChanged(Article()))
        onAddNewVcEvent(VcFormEvent.fournisseurChanged(ConcurrentVC()))

    }
    fun onAddNewVcEvent(event: VcFormEvent) {
        when (event) {



            is VcFormEvent.fournisseurChanged ->
                stateAddNewVc = stateAddNewVc.copy(concurrent = event.fournisseur)

            is VcFormEvent.noteChanged ->
                stateAddNewVc = stateAddNewVc.copy(note = event.note)

            is VcFormEvent.prixChanged ->
                stateAddNewVc = stateAddNewVc.copy(prix = event.prix)

            is VcFormEvent.produitChanged ->
                stateAddNewVc = stateAddNewVc.copy(produit = event.produit)

            is VcFormEvent.tauxChanged ->
                stateAddNewVc = stateAddNewVc.copy(taux = event.taux)

            is VcFormEvent.typeCommunicationChanged ->
                stateAddNewVc = stateAddNewVc.copy(typeCommunication = event.typeCommunication)

            is VcFormEvent.LocalProductChanged ->
                stateAddNewVc = stateAddNewVc.copy(localProduct = event.localProduct)

            is VcFormEvent.autreNoteChanged ->

                stateAddNewVc = stateAddNewVc.copy(autreNote = event.autreNote)


            VcFormEvent.SubmitAddNewProductVc -> submitAddVcData(TypeVc.VCLANCEMENTNP.typeVC)
            VcFormEvent.SubmitAddPromotionVc -> submitAddVcData(TypeVc.VCPROMO.typeVC)
            VcFormEvent.SubmitAddAutreVc -> submitAddVcData(TypeVc.VCAUTRE.typeVC)
            VcFormEvent.SubmitAddPrixVc -> submitAddVcData(TypeVc.VCPRIX.typeVC)
        }
    }

    private fun submitAddVcData(from : String) {
        val noteResult = validateIsNotEmptyString.execute(stateAddNewVc.note)

        val produitResult = validateIsNotEmptyString.execute(stateAddNewVc.produit)
        val tauxResult = validateDoubleNotZero.execute(stringToDouble(stateAddNewVc.taux))
        val prixResult = validateDoubleNotZero.execute(stringToDouble(stateAddNewVc.prix))
        val autreNoteResult = validateIsNotEmptyString.execute(stateAddNewVc.autreNote)


        val concurrentResult = validateIsNotEmptyString.execute(stateAddNewVc.concurrent.codeconcurrent)
        val typeCommunicationResult = validateIsNotEmptyString.execute(stateAddNewVc.typeCommunication.codeTypeCom)
        val localProductResult = validateIsNotEmptyString.execute(stateAddNewVc.localProduct.aRTCodeBar)



        val hasError = when (from) {
            TypeVc.VCLANCEMENTNP.typeVC -> listOf(
                noteResult,
                produitResult,
                tauxResult,
                prixResult,
                concurrentResult,
                typeCommunicationResult
            ).any { !it.successful }

            TypeVc.VCPRIX.typeVC -> {
                listOf(
                    noteResult,
                    produitResult,
                    prixResult,
                    localProductResult,
                    typeCommunicationResult
                ).any { !it.successful }
            }
            TypeVc.VCPROMO.typeVC ->
                listOf(
                    noteResult,
                    produitResult,
                    tauxResult,
                    prixResult,
                    localProductResult,
                    typeCommunicationResult
                ).any { !it.successful }
            else -> listOf(
                noteResult,
                produitResult,
                concurrentResult,
                autreNoteResult,
                typeCommunicationResult
            ).any { !it.successful }
        }

        if (hasError) {
            stateAddNewVc = stateAddNewVc.copy(
                noteError = noteResult.errorMessage,
                produitError = produitResult.errorMessage,
                tauxError = tauxResult.errorMessage,
                localProductError = localProductResult.errorMessage,
                prixError = prixResult.errorMessage,
                typeCommunicationError = typeCommunicationResult.errorMessage,
                concurrentError = concurrentResult.errorMessage,
                autreNoteError =  autreNoteResult.errorMessage

            )
            return
        }
        viewModelScope.launch {
            validationAddVcEvents = ValidationAddNewVcEvent.AddNewVc(stateAddNewVc)
        }
    }


}
