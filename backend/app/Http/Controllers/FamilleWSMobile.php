<?php

namespace App\Http\Controllers;

use App\Helpers\Enum;
use Exception;
use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;

class FamilleWSMobile extends Controller
{


    public function getFamilleByCode(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('famille')->where('FAM_Code', $data["object"])->first());

    }


    /*fonction get data from database*/

    public function getFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $famille = $connection->table('famille')->select([
            'FAM_Code', 'FAM_Lib', 'FAM_User', 'FAM_Station', 'FAM_IsTaktile', 'FAM_Couleur', 'FAM_DesgCourte', 'FAM_NumOrdre', 'FAM_export',
            'FAM_DDm', 'FAM_Alphabetique', 'photo_Path', 'SousFAM_Code', 'NumAppel', 'FAMParent_Code', 'FAMParent_Etat',
            'is_FAMParent', 'FAM_NOrdreEC', 'FAM_Etat', 'FAMP_NOrdreEC', 'exportM', 'DDmM', 'FAM_NoteBtk', 'FAM_CodeM'
        ])->get();

        return response()->json($famille);
    }


    public function getFamilleByX(Request $request)
    {
        $items = $request->json()->all();
        $connection = DatabaseConnection::setConnection($items);
        return response()->json($connection->table('famille')->where($request->field, $request->value)->get());
    }


    public function addFamilleMobile(Request $request)
    {
        $items = $request->json()->all();
        $connection = DatabaseConnection::setConnection($items);

        if (!empty ($items)) {
            $result = array();
            foreach ($items["object"] as $data) {
                $connection->beginTransaction();
                try {
                    //Change the Value of nullable fields
                    if ($data["FAM_Image"] == "NULL")
                        $data["FAM_Image"] = NULL;
                    if ($data["FAM_DDm"] == "NULL")
                        $data["FAM_DDm"] = NULL;
                    if ($data["FAM_Couleur"] == "NULL")
                        $data["FAM_Couleur"] = NULL;
                    if ($data["FAM_export"] == "NULL")
                        $data["FAM_export"] = NULL;
                    if ($data["FAM_IsTaktile"] == "NULL")
                        $data["FAM_IsTaktile"] = NULL;
                    if ($data["FAM_Alphabetique"] == "NULL")
                        $data["FAM_Alphabetique"] = NULL;
                    if ($data["NumAppel"] == "NULL")
                        $data["NumAppel"] = NULL;
                    //test if this record already exist
                    $exist = $connection->table('famille')
                        ->where('FAM_Code', '=', $data['FAM_Code'])
                        ->first();
                    if ($exist) {
                        $updated = $connection->table('famille')
                            ->where('FAM_Code', $data['FAM_Code'])
                            ->update([
                                'FAM_Couleur' => $data['FAM_Couleur'],
                                'FAM_DDm' => $data['FAM_DDm'],
                                'FAM_DesgCourte' => $data['FAM_DesgCourte'],
                                'FAM_export' => $data['FAM_export'],
                                'FAM_Image' => $data['FAM_Image'],
                                'FAM_Lib' => $data['FAM_Lib'],
                                'FAM_NumOrdre' => $data['FAM_NumOrdre'],
                                'FAM_Station' => $data['FAM_Station'],
                                'FAM_User' => $data['FAM_User'],
                                'FAM_IsTaktile' => $data['FAM_IsTaktile']
                            ]);
                        if ($updated) {
                            $result[] = $this->setGlobalResult('FAM_Code', $data['FAM_Code'], "UPDATED");

                        } else {
                            $result[] = $this->setGlobalResult('FAM_Code', $data['FAM_Code'],
                                "ERROR IN MODIFICATION LEVEL", Enum::Error_Code);
                            throw new Exception("ERROR IN MODIFICATION LEVEL", Enum::Error_Code);
                        }
                    } else {
                        $inserted = $connection->table('famille')->insert($data);
                        if ($inserted) {
                            $result[] = $this->setGlobalResult('FAM_Code',
                                $data['FAM_Code'], "INSERTED");

                        } else {
                            $result[] = $this->setGlobalResult('FAM_Code', $data['FAM_Code'],
                                "ERROR AT THE INSERTION LEVEL", Enum::Error_Code);
                            throw new Exception("ERROR AT THE INSERTION LEVEL", Enum::Error_Code);
                        }
                    }
                    $connection->commit();
                } catch (Exception $ex) {
                    $connection->rollBack();
                }

            }
            return response()->json($result);

        } else {
            return (false);
        }


    }


    public function addFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        if (!empty ($data)) {
            $f = $connection->table('famille')->insert($data);

            return response()->json($f);

        } else {
            return (false);
        }


    }


    public function updateFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);


        $connection->table('famille')->where('FAM_Code', $data["FAM_Code"])->update($data);

        return ("Data modified");

    }


    public function deleteFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $connection->table('famille')->where('FAM_Code', $data["object"])->delete();
        return ("Data Deleted");
    }

    public function updateOrCreate($connection, $data, $table, $whereClauses)
    {
        $exists = $connection->table($table)->where($whereClauses)->first();

        if ($exists) {
            $connection->table($table)->where($whereClauses)->delete();
        }

        return $connection->table($table)->insert($data);

    }
}





