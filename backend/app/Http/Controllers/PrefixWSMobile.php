<?php

namespace App\Http\Controllers;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;
use Illuminate\Support\Str;

class PrefixWSMobile extends Controller
{
    public function getPrefixes(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        return response()->json($connection->table('Prefixe')->select(['PRE_Id_table', 'PRE_Préfixe as PRE_Prefixe', 'PRE_Désignation as PRE_Designation'])->get(), 200, ['Content-type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }


    public function getPrefix($connection, $table_name, $exercice, $station)
    {


        $prefix = collect($connection->select("select PRE_Préfixe as PRE_Prefixe, PRE_NameChamp, PRE_Station from prefixe where PRE_Id_table = '$table_name'"))->first();
        $exercice = substr($exercice, 2);
        if (!is_null($prefix) && !is_null($prefix->PRE_Prefixe))
            $prefixId = $prefix->PRE_Prefixe . $exercice . "/" . $station;
        $prefix = collect($connection->select("SELECT MAX(CONVERT(INT,SUBSTRING($prefix->PRE_NameChamp,len('$prefixId') + 1 ,len($prefix->PRE_NameChamp)))) as prefix FROM  $table_name
WHERE substring( $prefix->PRE_NameChamp , 0 ,len('$prefixId')+1) = '$prefixId' "))->first();

        if (is_null($prefix->prefix)) {
            //var_dump($prefixId);
            return $prefixId . "0001";
        } else {
            $inc = $prefix->prefix + 1;
            $number = str_pad($inc, 4, '0', STR_PAD_LEFT);
            return $prefixId . $number;
        }
    }


    public function getReglementPrefix($connection, $table_name, $exercice, $id_caisse_champ, $id_caisse)
    {
        // $connection = DatabaseConnection::setConnection($params);

        //var_dump($exercice);
        $prefix = collect($connection->select("select PRE_Préfixe as PRE_Prefixe, PRE_NameChamp, PRE_ChampExercice,
       PRE_Station from prefixe where PRE_Id_table = '$table_name'"))->first();

        if (!is_null($prefix) && !is_null($prefix->PRE_Prefixe))
            $prefixId = $prefix->PRE_Prefixe;
        $prefix = collect($connection->select("SELECT MAX(CONVERT(INT,SUBSTRING($prefix->PRE_NameChamp,len('$prefixId') + 1 ,len($prefix->PRE_NameChamp)))) as prefix FROM  $table_name
            WHERE substring( $prefix->PRE_NameChamp , 0 ,len('$prefixId')+1) = '$prefixId' AND REGC_Exercice = '$exercice' AND REGC_IdSCaisse = '$id_caisse'"))->first();
        if (is_null($prefix->prefix)) {
            return $prefixId . "000001";
        } else {
            $inc = $prefix->prefix + 1;
            $number = str_pad($inc, 6, '0', STR_PAD_LEFT);
            return $prefixId . $number;
        }
    }

    public function getRetourPrefix($params, $table_name, $exercice, $station, $prefix_tab, $id_user)
    {
        $connection = DatabaseConnection::setConnection($params);
        //var_dump($exercice);
        $prefix = collect($connection->select("select PRE_Préfixe as PRE_Prefixe, PRE_NameChamp, PRE_Station from prefixe where PRE_Id_table = '$prefix_tab'"))->first();
        $exercice = substr($exercice, 2);
        //$prefixe = $prefixe + $exercice + "/" + $station;
        if (!is_null($prefix) && !is_null($prefix->PRE_Prefixe))
            $prefixId = $prefix->PRE_Prefixe . $exercice . $station . $id_user;

        $prefix = collect($connection->select("SELECT MAX(CONVERT(INT,SUBSTRING($prefix->PRE_NameChamp,len('$prefixId') + 1 ,len($prefix->PRE_NameChamp)))) as prefix FROM  $table_name
WHERE substring( $prefix->PRE_NameChamp , 0 ,len('$prefixId')+1) = '$prefixId' "))->first();

        if (is_null($prefix->prefix)) {
            //var_dump($prefixId);
            return $prefixId . "0001";
        } else {
            $inc = $prefix->prefix + 1;
            $number = str_pad($inc, 5, '0', STR_PAD_LEFT);
            return $prefixId . $number;
        }
    }

    public function getCommandePrefix($connection, $table_name, $exercice, $station, $user,$PRE_Type)
    {
        if($PRE_Type==null){
            $PRE_Type = 'BCC_Client';
        }
        $prefix = collect($connection->select("select PRE_Préfixe as PRE_Prefixe, PRE_NameChamp, PRE_Station from 
                                                                  PrefixeFacture where PRE_Id_table = '$table_name'
                                                                                and PRE_Type = '$PRE_Type'"))->first();

        $exercice = substr($exercice, 2);

        //$prefixe = $prefixe + $exercice + "/" + $station;

        if (!is_null($prefix) && !is_null($prefix->PRE_Prefixe))
            $prefixId = $prefix->PRE_Prefixe . $exercice . $station . $user;

        $prefix = collect($connection->select("SELECT MAX(CONVERT(INT,SUBSTRING($prefix->PRE_NameChamp,len('$prefixId') + 1 
    ,len($prefix->PRE_NameChamp)))) as prefix FROM  $table_name
        WHERE substring( $prefix->PRE_NameChamp , 0 ,len('$prefixId')+1) = '$prefixId' "))->first();

        if (is_null($prefix->prefix)) {
            return $prefixId . "00001";
        } else {
            $inc = $prefix->prefix + 1;
            $number = str_pad($inc, 5, '0', STR_PAD_LEFT);
            return $prefixId . $number;
        }
    }

    public function getBonTransferPrefix($connection, $exercice, $station, $user)
    {

        $table_name = "bon_transfert";
        $prefix = collect($connection->select("select PRE_Préfixe as PRE_Prefixe, PRE_NameChamp, PRE_Station from prefixe where PRE_Id_table = '$table_name'"))->first();
        $exercice = substr($exercice, 2);
        if (!is_null($prefix) && !is_null($prefix->PRE_Prefixe))
            $prefixId = $prefix->PRE_Prefixe . $exercice . $station . $user;
        $new_prefix = collect($connection->select("SELECT MAX(
    CONVERT(INT,SUBSTRING($prefix->PRE_NameChamp,len('$prefixId') + 1 ,len($prefix->PRE_NameChamp)))) as prefix FROM  $table_name
        WHERE substring( $prefix->PRE_NameChamp , 0 ,len('$prefixId')+1) = '$prefixId' "))->first();
        if (is_null($new_prefix->prefix)) {
            return $prefixId . $new_prefix->prefix."00001";
        } else {
            $inc = $new_prefix->prefix + 1;
            $number = str_pad($inc, 5, '0', STR_PAD_LEFT);
            return $prefixId . $number;
        }
    }

    public function getBonEntreePrefix($params, $exercice, $station, $user)
    {
        $connection = DatabaseConnection::setConnection($params);
        $table_name = "bon_entree";
        $mobile_table = "bon_entree_mobile";
        $beginPrefix = collect($connection->select("select PRE_Préfixe as PRE_Prefixe, PRE_NameChamp, PRE_Station from prefixe where PRE_Id_table = '$table_name' "))->first();
        $exercice = substr($exercice, 2);
        if (!is_null($beginPrefix) && !is_null($beginPrefix->PRE_Prefixe))
            $prefixId = $beginPrefix->PRE_Prefixe . $exercice . $station . $user;

        $prefix = collect($connection->select("SELECT MAX(CONVERT(INT,SUBSTRING($beginPrefix->PRE_NameChamp,len('$prefixId') + 1 ,len($beginPrefix->PRE_NameChamp)))) as prefix FROM  $table_name
        WHERE substring( $beginPrefix->PRE_NameChamp , 0 ,len('$prefixId')+1) = '$prefixId' "))->first();
        $prefix_mobile = collect($connection->select("SELECT MAX(CONVERT(INT,SUBSTRING($beginPrefix->PRE_NameChamp,len('$prefixId') + 1 ,len($beginPrefix->PRE_NameChamp)))) as prefix FROM   $mobile_table
        WHERE substring( $beginPrefix->PRE_NameChamp , 0 ,len('$prefixId')+1) = '$prefixId' "))->first();
        if ((!is_null($prefix->prefix) && !is_null($prefix->prefix) &&
                floatval($prefix_mobile->prefix) > floatval($prefix->prefix)) || is_null($prefix->prefix)) {
            $prefix = $prefix_mobile;
        }
        if (is_null($prefix->prefix)) {
            return $prefixId . '00001';
        } else {
            $inc = $prefix->prefix + 1;
            $number = str_pad($inc, 5, '0', STR_PAD_LEFT);
            return $prefixId . $number;
        }
    }

    public function getInventairePrefix($connection, $exercice, $station, $user)
    {

        $table_name = "Inventaire";
        //var_dump($exercice);
        $prefix = $connection->table("Prefixe")->select("PRE_Préfixe as PRE_Prefixe", "PRE_NameChamp", "PRE_Station ")->where('PRE_Id_table',
            'Inventaire')->first();



        if ($prefix != null && !is_null($prefix->PRE_Prefixe)) {
            $prefixInv=$prefix->PRE_Prefixe;
            $prefixInv= $prefixInv[0].$prefixInv[1].$prefixInv[2]."P_";
            $prefixId = $prefixInv .$exercice. $station.$user;
            $prefix = collect($connection->select("SELECT MAX(CONVERT(INT,SUBSTRING($prefix->PRE_NameChamp,len('$prefixId') + 1 ,len($prefix->PRE_NameChamp)))) as prefix FROM  $table_name
        WHERE substring( $prefix->PRE_NameChamp , 0 ,len('$prefixId')+1) = '$prefixId' "))->first();
        }
        if (is_null($prefix->prefix)) {
            return $prefixId . "00001";
        } else {
            $inc = $prefix->prefix + 1;
            $number = str_pad($inc, 5, '0', STR_PAD_LEFT);
            return $prefixId . $number;
        }
    }


    public function getFacturePrefix($connection, $year, $month)
    {

        $table_name = "facture";
        $beginPrefix = collect($connection->select("select PRE_Préfixe as 
    PRE_Prefixe,PRE_ChampStation,PRE_NameChamp, PRE_Station from 
                                                                PrefixeFacture where PRE_Id_table 
                                                                                         = '$table_name' and
                                                                                   PRE_Type ='Client' "))->first();


        if (!is_null($beginPrefix) && !is_null($beginPrefix->PRE_Prefixe)) {
            $fact_chahia = $connection->table('ville')->pluck('FactChahia')->first();
            //$prefixId = !!$fact_chahia ? $beginPrefix->PRE_Prefixe . $year . $month . $beginPrefix->PRE_ChampStation : $beginPrefix->PRE_Prefixe . "/" . $beginPrefix->PRE_ChampStation ;
            $prefixId = $beginPrefix->PRE_Prefixe;
            $prefixExec = $beginPrefix->PRE_Prefixe;
            //$prefixExec = $beginPrefix->PRE_Prefixe . $year;


            $prefix = collect($connection->select("SELECT
    (isnull(MAX(CONVERT(INT,SUBSTRING(FACT_Num,5 ,6))),0))  as prefix 
FROM  facture 

        WHERE substring( FACT_Num ,0,len('$prefixExec')+1) = '$prefixExec' AND  FACT_Type = 'Client' "))->first();






            if (is_null($prefix->prefix)) {
                return $prefixId . '000001';
            } else {
                $inc = $prefix->prefix + 1;
                $number = str_pad($inc, 6, '0', STR_PAD_LEFT);

                return $prefixId . $number;


            }


        }
        /*else
        {
            if (!is_null($beginPrefix) && !is_null($beginPrefix->PRE_Prefixe))

                $prefixId = $beginPrefix->PRE_Prefixe.$exercice."/";
                
            $prefix = collect($connection->select("SELECT MAX(CONVERT(INT,SUBSTRING($beginPrefix->PRE_NameChamp,len('$prefixId') + 1 ,len($beginPrefix->PRE_NameChamp)))) as prefix FROM  $table_name

        WHERE substring( $beginPrefix->PRE_NameChamp , 0 ,len('$prefixId')+1) = '$prefixId'"))->first();

            if (is_null($prefix->prefix)) {
                return $prefixId . '000001';
            } else {
                $inc = $prefix->prefix + 1;
                $number = str_pad($inc, 6, '0', STR_PAD_LEFT);
                return $prefixId . $number;
            }
        }*/

    }
    public function getFacturePrefix2($connection, $year, $month)
    {

        $table_name = "facture";
        $beginPrefix = collect($connection->select("select PRE_Préfixe as 
    PRE_Prefixe,PRE_ChampStation,PRE_NameChamp, PRE_Station from 
                                                                PrefixeFacture where PRE_Id_table 
                                                                                         = '$table_name' and
                                                                                   PRE_Type ='Client' "))->first();

        if (!is_null($beginPrefix) && !is_null($beginPrefix->PRE_Prefixe)) {
            $prefixId = $beginPrefix->PRE_Prefixe . "/" . $beginPrefix->PRE_ChampStation;
            $prefixExec = $beginPrefix->PRE_Prefixe . $year;


            $prefix = collect($connection->select("SELECT
    (isnull(MAX(CONVERT(INT,SUBSTRING(TIK_NumeroBL,10 ,6))),0))  as prefix 
FROM  Ticket 

        WHERE substring( TIK_NumeroBL ,0,len('$prefixExec')+1) = '$prefixExec' "))->first();



            if (is_null($prefix->prefix)) {
                return $prefixId . '000001';
            } else {
                $inc = $prefix->prefix + 1;
                $number = str_pad($inc, 6, '0', STR_PAD_LEFT);
                return $prefixId . $number;
            }
        }

    }

    public function getPrefixSessionCaisse($params, $table_name, $exercice, $user)
    {
        $connection = DatabaseConnection::setConnection($params);

        $prefix = collect($connection->select("select PRE_Préfixe as PRE_Prefixe, PRE_NameChamp, PRE_Station from prefixe where PRE_Id_table = '$table_name'"))->first();
        $exercice = substr($exercice, 2);
        if (!is_null($prefix) && !is_null($prefix->PRE_Prefixe))
            $prefixId = $prefix->PRE_Prefixe . $exercice . "/" . $user;
        $prefix = collect($connection->select("SELECT MAX(CONVERT(INT,SUBSTRING($prefix->PRE_NameChamp,len('$prefixId') + 1 ,len($prefix->PRE_NameChamp)))) as prefix FROM  $table_name
WHERE substring( $prefix->PRE_NameChamp , 0 ,len('$prefixId')+1) = '$prefixId' "))->first();

        if (is_null($prefix->prefix)) {
            //var_dump($prefixId);
            return $prefixId . "000001";
        } else {
            $inc = $prefix->prefix + 1;
            $number = str_pad($inc, 6, '0', STR_PAD_LEFT);
            return $prefixId . $number;
        }
    }

    public function getPrefixOrdreMission($connection, $table_name, $exercice, $station, $user)
    {


        $prefix = collect($connection->select("select PRE_Préfixe as PRE_Prefixe, PRE_NameChamp, PRE_Station from prefixe where PRE_Id_table = '$table_name'"))->first();
        $exercice = substr($exercice, 2);
        if (!is_null($prefix) && !is_null($prefix->PRE_Prefixe))
            $prefixId = $prefix->PRE_Prefixe . $exercice . $station . $user;
        $prefix = collect($connection->select("SELECT MAX(CONVERT(INT,SUBSTRING($prefix->PRE_NameChamp,len('$prefixId') + 1 ,len($prefix->PRE_NameChamp)))) as prefix FROM  $table_name
        WHERE substring( $prefix->PRE_NameChamp , 0 ,len('$prefixId')+1) = '$prefixId' "))->first();

        if (is_null($prefix->prefix)) {
            //var_dump($prefixId);
            return $prefixId . "000001";
        } else {
            $inc = $prefix->prefix + 1;
            $number = str_pad($inc, 6, '0', STR_PAD_LEFT);
            return $prefixId . $number;
        }
    }


    public function getPrefixVconcu($params, $table_name, $user)
    {
        $connection = DatabaseConnection::setConnection($params);

        $prefix = collect($connection->select("select PRE_Préfixe as PRE_Prefixe, PRE_NameChamp, PRE_Station from prefixe where PRE_Id_table = '$table_name'"))->first();

        if (!is_null($prefix) && !is_null($prefix->PRE_Prefixe))
            $prefixId = $prefix->PRE_Prefixe . $user;

        $prefix = collect($connection->select("SELECT MAX(CONVERT(INT,SUBSTRING($prefix->PRE_NameChamp,len('$prefixId') + 1 ,len($prefix->PRE_NameChamp)))) as prefix FROM  $table_name
            WHERE substring( $prefix->PRE_NameChamp , 0 ,len('$prefixId')+1) = '$prefixId' "))->first();

        if (is_null($prefix->prefix)) {
            //var_dump($prefixId);
            return $prefixId . "000001";
        } else {
            $inc = $prefix->prefix + 1;
            $number = str_pad($inc, 6, '0', STR_PAD_LEFT);
            return $prefixId . $number;
        }
    }


    public function getDNVisitePrefix($connection, $table_name, $exercice, $user)
    {
        $prefix = collect($connection->select("select PRE_Préfixe as PRE_Prefixe, PRE_NameChamp, PRE_Station from prefixe where PRE_Id_table = '$table_name'"))->first();

        $exercice = substr($exercice, 2);
        if (!is_null($prefix) && !is_null($prefix->PRE_Prefixe))
            $prefixId = $prefix->PRE_Prefixe . $exercice . $user;
        $prefix = collect($connection->select("SELECT MAX(CONVERT(INT,SUBSTRING($prefix->PRE_NameChamp,len('$prefixId') + 1 ,len($prefix->PRE_NameChamp)))) as prefix FROM  $table_name
WHERE substring( $prefix->PRE_NameChamp , 0 ,len('$prefixId')+1) = '$prefixId' "))->first();

        if (is_null($prefix->prefix)) {
            //var_dump($prefixId);
            return $prefixId . "000001";
        } else {
            $inc = $prefix->prefix + 1;
            $number = str_pad($inc, 6, '0', STR_PAD_LEFT);
            return $prefixId . $number;
        }
    }

    public function getDepenseCaissePrefix($connection, $table_name, $idSCession, $user)
    {
        $prefix = collect($connection->select("select PRE_Préfixe as PRE_Prefixe, PRE_NameChamp, PRE_Station from prefixe where PRE_Id_table = '$table_name'"))->first();
        if (!is_null($prefix) && !is_null($prefix->PRE_Prefixe))
            $prefixId = $prefix->PRE_Prefixe . $idSCession . $user;
        $prefix = collect($connection->select("SELECT MAX(CONVERT(INT,SUBSTRING($prefix->PRE_NameChamp,len('$prefixId') + 1 ,len($prefix->PRE_NameChamp)))) as prefix FROM  $table_name
        WHERE substring( $prefix->PRE_NameChamp , 0 ,len('$prefixId')+1) = '$prefixId' "))->first();

        if (is_null($prefix->prefix)) {
            //var_dump($prefixId);
            return $prefixId . "000001";
        } else {
            $inc = $prefix->prefix + 1;
            $number = str_pad($inc, 6, '0', STR_PAD_LEFT);
            return $prefixId . $number;
        }
    }
    public function getDepensePrefix($connection, $table_name, $idStation)
    {
        $prefix = collect($connection->select("select PRE_Préfixe as PRE_Prefixe, PRE_NameChamp, PRE_Station from prefixe where PRE_Id_table = '$table_name'"))->first();
        if (!is_null($prefix) && !is_null($prefix->PRE_Prefixe))
            // $exercice=  Carbon::now()->year;
            $exercice = substr(Carbon::now()->year, 2);

        $prefixId = $prefix->PRE_Prefixe.$exercice."/" . $idStation ;
        $prefix = collect($connection->select("SELECT MAX(CONVERT(INT,SUBSTRING($prefix->PRE_NameChamp,len('$prefixId') + 1 ,len($prefix->PRE_NameChamp)))) as prefix FROM  $table_name
        WHERE substring( $prefix->PRE_NameChamp , 0 ,len('$prefixId')+1) = '$prefixId' "))->first();

        if (is_null($prefix->prefix)) {
            //var_dump($prefixId);
            return $prefixId . "0001";
        } else {
            $inc = $prefix->prefix + 1;
            $number = str_pad($inc, 4, '0', STR_PAD_LEFT);
            return $prefixId . $number;
        }
    }

}
