package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.ligne_ticket.repository

import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import kotlinx.coroutines.flow.Flow



interface LigneTicketLocalRepository {
    fun upsertAll(value: List<LigneTicket>)
    fun upsert(value: LigneTicket)

    fun updateNumTicket(codeM: String, newCode: Int, exercice: String, carnet: String)
    fun deleteAll()

    fun deleteByCodeM(code: String, exercice: String)

  fun  deleteByNumTicket(numTicket: String, ltExercice: String)
    fun getAll(): Flow<List<LigneTicket>>

}