package com.asmtunis.procaisseinventory.pro_caisse.inventaire.view_model

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.model.NestedItem
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.InvPatBatchResponse
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.Constants.IMMOBILISATION
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.Constants.PATRIMOINE
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePatrimoine
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.AddPieceJointInventaire
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.LigneBonCommandeWithImageList
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.Parent
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject

@FlowPreview
@HiltViewModel
class SyncInvPatrimoineViewModel @Inject constructor(
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val listenNetwork: ListenNetwork,
    // app: Application
) :
    ViewModel() {
    private val samplingInterval = 60000L // Emit every 60 seconds (reduced frequency)
    private var isSyncInProgress by mutableStateOf(false) // Prevent concurrent syncs

    /**
     * Disable auto-sync temporarily (useful when working offline)
     */
    fun disableAutoSync() {
        viewModelScope.launch {
            proCaisseLocalDb.dataStore.putBoolean(PROCAISSE_AUTO_SYNC_AUTHORISATION, false)
        }
    }

    /**
     * Enable auto-sync
     */
    fun enableAutoSync() {
        viewModelScope.launch {
            proCaisseLocalDb.dataStore.putBoolean(PROCAISSE_AUTO_SYNC_AUTHORISATION, true)
        }
    }

    /**
     * Check if currently syncing
     */
    fun isSyncing(): Boolean = isSyncInProgress


    private var autoSyncState by mutableStateOf(false)
    private var autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(
        key = PROCAISSE_AUTO_SYNC_AUTHORISATION,
        default = true
    ).distinctUntilChanged()
    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()

    private var connected by mutableStateOf(false)


    init {
        getNotSyncAffectation()
        getNotSyncAffectationBatiment()

        getImageNotSyncAffectationBatiment()

        getNotSyncDepOut()
        getNotSyncDepOutBatiment()
        getImageNotSyncDepOutBatiment()

        getNotSyncDepIn()
        getNotSyncDepInBatiment()
        getImageNotSyncDepInBatiment()

        getNotSyncInventaire()
        getNotSyncInventaireBatiment()
        getImageNotSyncInventaireBatiment()
    }


    /**
     ************************************************** A F F E C T A T I O N *************************************************
     */
    var responseAddAffectationState: RemoteResponseState<List<InvPatBatchResponse>> by mutableStateOf(
        RemoteResponseState()
    )
        private set


    var affectationNotSync: Map<BonCommande, List<LigneBonCommandeWithImageList>> by mutableStateOf(
        emptyMap()
    )
        private set

    var notSyncAffectationObj: String by mutableStateOf("")
        private set

    private fun getNotSyncAffectation() {

        viewModelScope.launch {
            val invPatFlow = proCaisseLocalDb.invePatrimoine.notSynced(
                typeInv = TypePatrimoine.AFFECTATION.typePat,
                devEtat = PATRIMOINE
            ).distinctUntilChanged()


            combine(networkFlow, invPatFlow, autoSyncFlow) { isConnected, invPatList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                invPatList.ifEmpty { emptyMap() }
            }.sample(samplingInterval).collect {

                if (it.isEmpty()) {
                    affectationNotSync = emptyMap()
                    return@collect
                }
                affectationNotSync = it

                // Only auto-sync if connected, auto-sync enabled, not already syncing, and not loading
                if (connected && autoSyncState && !isSyncInProgress && !responseAddAffectationState.loading) {
                    isSyncInProgress = true
                    syncAffectation()
                }
            }


        }
    }


    fun syncAffectation(selectedInvPatrimoine: Map<BonCommande, List<LigneBonCommande>> = emptyMap()) {
        viewModelScope.launch(dispatcherIO) {

            val listVisiteWithLinesDn = ArrayList<NestedItem<BonCommande, List<LigneBonCommande>>>()

            affectationNotSync.forEach { (key, value) ->
                run {
                    listVisiteWithLinesDn.add(
                        NestedItem(
                            parent = key,
                            children = value.map { it.ligneBonCommande!! }.toList()
                        )
                    )
                }
            }


            if (selectedInvPatrimoine != emptyMap<BonCommande, List<LigneBonCommande>>()) {
                listVisiteWithLinesDn.clear()

                selectedInvPatrimoine.forEach { (key, value) ->
                    run {
                        listVisiteWithLinesDn.add(
                            NestedItem(
                                parent = key,
                                children = value.map { it }.toList()
                            )
                        )
                    }
                }
            }


            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                    ?.let { Json.decodeFromString(it) } ?: BaseConfig(),
                Json.encodeToJsonElement(listVisiteWithLinesDn)
            )

            notSyncAffectationObj = Json.encodeToString(baseConfigObj)

            proCaisseRemote.inventairePatrimoine.addBatchInvPat(Json.encodeToString(baseConfigObj))
                .onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            setStateSynced(
                                invPatResponse = result.data!!,
                                listVisiteWithLinesDn = listVisiteWithLinesDn
                            )

                            responseAddAffectationState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )
                            isSyncInProgress = false // Reset sync progress flag
                        }

                        is DataResult.Loading -> {
                            responseAddAffectationState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            responseAddAffectationState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message,
                                message = selectedInvPatrimoine.keys.first().dEVNum
                            )
                            isSyncInProgress = false // Reset sync progress flag on error
                        }
                    }
                }/*.flowOn(dispatcherIO)*/.launchIn(this)
        }
    }


    /**
     * *************************************************************************
     */


    var responseAddAffectationBatimentState: RemoteResponseState<List<InvPatBatchResponse>> by mutableStateOf(
        RemoteResponseState()
    )
        private set


    var affectationBatimentNotSync: Map<BonCommande, List<LigneBonCommandeWithImageList>> by mutableStateOf(
        emptyMap()
    )
        private set

    var notSyncAffectationBatimentObj: String by mutableStateOf("")
        private set

    private fun getNotSyncAffectationBatiment() {
        viewModelScope.launch {
            val affectationBatimentFlow = proCaisseLocalDb.invePatrimoine.notSynced(
                typeInv = TypePatrimoine.AFFECTATION.typePat,
                devEtat = IMMOBILISATION
            ).distinctUntilChanged()

            combine(
                networkFlow,
                affectationBatimentFlow,
                autoSyncFlow
            ) { isConnected, affectationBatimentList, autoSync ->

                connected = isConnected
                autoSyncState = autoSync
                affectationBatimentList.ifEmpty { emptyMap() }
            }.sample(samplingInterval).collect {
                if (it.isEmpty()) {
                    affectationBatimentNotSync = emptyMap()
                    return@collect
                }
                affectationBatimentNotSync = it

                // Only auto-sync if connected, auto-sync enabled, not already syncing, and not loading
                if (connected && autoSyncState && !isSyncInProgress && !responseAddAffectationBatimentState.loading) {
                    isSyncInProgress = true
                    syncAffectationBatiment()
                }
            }
            //  }
        }
    }


    fun syncAffectationBatiment(selectedInvPat: BonCommande = BonCommande()) {
        viewModelScope.launch(dispatcherIO) {
            val listVisiteWithLinesDn = ArrayList<NestedItem<BonCommande, List<LigneBonCommande>>>()

            affectationBatimentNotSync.forEach { (key, value) ->
                run {
                    listVisiteWithLinesDn.add(
                        NestedItem(
                            parent = key,
                            children = value.map { it.ligneBonCommande ?: LigneBonCommande() }
                        )
                    )
                }
            }


            if (selectedInvPat != BonCommande()) {
                listVisiteWithLinesDn.removeIf { it.parent?.devCodeM != selectedInvPat.devCodeM }
            }

            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                    ?.let { Json.decodeFromString(it) } ?: BaseConfig(),
                Json.encodeToJsonElement(listVisiteWithLinesDn)
            )

            notSyncAffectationBatimentObj = Json.encodeToString(baseConfigObj)

            proCaisseRemote.inventairePatrimoine.addBatchInvPat(notSyncAffectationBatimentObj)
                .onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            responseAddAffectationBatimentState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )

                            setStateSynced(
                                invPatResponse = result.data,
                                listVisiteWithLinesDn = listVisiteWithLinesDn
                            )
                            isSyncInProgress = false // Reset sync progress flag
                            Log.d(
                                "ccxxddsszzz",
                                "****************************************************"
                            )
                        }

                        is DataResult.Loading -> {
                            responseAddAffectationBatimentState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            responseAddAffectationBatimentState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message,
                                message = selectedInvPat.dEVNum
                            )
                            isSyncInProgress = false // Reset sync progress flag on error
                        }
                    }
                }.launchIn(this)
        }
    }


    var affectationBatimentImageNotSync: List<ImagePieceJoint> by mutableStateOf(emptyList())
        private set

    private fun getImageNotSyncAffectationBatiment() {
        viewModelScope.launch {

            val affectationBatimentImageNotSyncFlow =
                proCaisseLocalDb.inventairePieceJoint.noSynced(
                    typeVC = IMMOBILISATION + ";" + TypePatrimoine.AFFECTATION.typePat
                ).distinctUntilChanged()


            combine(
                networkFlow,
                affectationBatimentImageNotSyncFlow,
                autoSyncFlow
            ) { isConnected, affectationBatimentImageNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                affectationBatimentImageNotSyncList.ifEmpty { emptyList() }
            }.sample(samplingInterval)/*.debounce(10000)*/.collect {

                if (it.isEmpty()) {
                    affectationBatimentImageNotSync = emptyList()
                    return@collect
                }
                affectationBatimentImageNotSync = it

                // Only auto-sync if connected, auto-sync enabled, not already syncing, and not loading
                if (connected && autoSyncState && !isSyncInProgress && !responseImageAddAffectationBatimentState.loading) {
                    isSyncInProgress = true
                    syncImageAffectationBatiment()
                }
            }


        }
    }

    var responseImageAddAffectationBatimentState: RemoteResponseState<List<AddPieceJointInventaire>> by mutableStateOf(
        RemoteResponseState()
    )
        private set
    var notImageAddAffectationBatimentObj: String by mutableStateOf("")
        private set

    fun syncImageAffectationBatiment() {
        viewModelScope.launch(dispatcherIO) {

            val listImageAffectationBatiment = affectationBatimentImageNotSync
                .groupBy { it.devNum }
                .map { (parentDevNum, children) ->
                    NestedItem(
                        parent = Parent(dEVNum = parentDevNum),
                        children = children
                    )
                }


            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                    ?.let { Json.decodeFromString(it) } ?: BaseConfig(),
                Json.encodeToJsonElement(listImageAffectationBatiment)
            )

            notImageAddAffectationBatimentObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.inventairePatrimoine.addBatchPiecJointInventaire(
                notImageAddAffectationBatimentObj
            ).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        for (image in result.data!!) {
                            if (image.code == 10200) {
                                proCaisseLocalDb.inventairePieceJoint.updateImageState(image.codeIMG)
                            }

                        }

                        responseImageAddAffectationBatimentState =
                            RemoteResponseState(data = result.data, loading = false, error = null)
                        isSyncInProgress = false // Reset sync progress flag


                    }

                    is DataResult.Loading -> {
                        responseImageAddAffectationBatimentState =
                            RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        Log.d("oooosdcsdcscd", "error image " + result.message.toString())
                        responseImageAddAffectationBatimentState = RemoteResponseState(
                            data = null,
                            loading = false,
                            error = result.message
                        )
                        isSyncInProgress = false // Reset sync progress flag on error
                    }
                }
            }/*.flowOn(dispatcherIO)*/.launchIn(this)
        }
    }


    /**
     ********************************************************** DEP OUT ***********************************************************
     */
    var responseAddDepOutState: RemoteResponseState<List<InvPatBatchResponse>> by mutableStateOf(
        RemoteResponseState()
    )
        private set


    var depOutNotSync: Map<BonCommande, List<LigneBonCommandeWithImageList>> by mutableStateOf(
        emptyMap()
    )
        private set

    var notSyncDepOutObj: String by mutableStateOf("")
        private set

    private fun getNotSyncDepOut() {
        viewModelScope.launch {

            val depOutNotSyncFlow = proCaisseLocalDb.invePatrimoine.notSynced(
                typeInv = TypePatrimoine.SORTIE.typePat,
                devEtat = PATRIMOINE
            ).distinctUntilChanged()


            combine(
                networkFlow,
                depOutNotSyncFlow,
                autoSyncFlow
            ) { isConnected, depOutNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                depOutNotSyncList.ifEmpty { emptyMap() }
            }.sample(samplingInterval)/*.debounce(10000)*/.collect {

                if (it.isEmpty()) {
                    depOutNotSync = emptyMap()
                    return@collect
                }
                depOutNotSync = it

                // Only auto-sync if connected, auto-sync enabled, not already syncing, and not loading
                if (connected && autoSyncState && !isSyncInProgress && !responseAddDepOutState.loading) {
                    isSyncInProgress = true
                    syncDepOut()
                }
            }
        }
    }


    fun syncDepOut(selectedInvPatrimoine: Map<BonCommande, List<LigneBonCommande>> = emptyMap()) {
        viewModelScope.launch(dispatcherIO) {
            val listVisiteWithLinesDn = ArrayList<NestedItem<BonCommande, List<LigneBonCommande>>>()

            depOutNotSync.forEach { (key, value) ->
                run {
                    listVisiteWithLinesDn.add(
                        NestedItem(
                            parent = key,
                            children = value.map { it.ligneBonCommande ?: LigneBonCommande() }
                        )
                    )
                }
            }


            if (selectedInvPatrimoine != emptyMap<BonCommande, List<LigneBonCommande>>()) {
                listVisiteWithLinesDn.clear()

                selectedInvPatrimoine.forEach { (key, value) ->
                    run {
                        listVisiteWithLinesDn.add(
                            NestedItem(
                                parent = key,
                                children = value.map { it }.toList()
                            )
                        )
                    }
                }
            }

            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                    ?.let { Json.decodeFromString(it) } ?: BaseConfig(),
                Json.encodeToJsonElement(listVisiteWithLinesDn)
            )

            notSyncDepOutObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.inventairePatrimoine.addBatchInvPat(notSyncDepOutObj).onEach { result ->
                when (result) {
                    is DataResult.Success -> {

                        setStateSynced(
                            invPatResponse = result.data!!,
                            listVisiteWithLinesDn = listVisiteWithLinesDn
                        )
                        responseAddDepOutState =
                            RemoteResponseState(data = result.data, loading = false, error = null)
                        isSyncInProgress = false // Reset sync progress flag
                    }

                    is DataResult.Loading -> {
                        responseAddDepOutState =
                            RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        responseAddDepOutState = RemoteResponseState(
                            data = null,
                            loading = false,
                            error = result.message,
                            message = selectedInvPatrimoine.keys.first().dEVNum
                        )
                        isSyncInProgress = false // Reset sync progress flag on error
                    }
                }
            }/*.flowOn(dispatcherIO)*/.launchIn(this)
        }
    }


    /**
     * ******************************************************************************
     */


    var responseAddDepOutBatimentState: RemoteResponseState<List<InvPatBatchResponse>> by mutableStateOf(
        RemoteResponseState()
    )
        private set


    var depOutBatimentNotSync: Map<BonCommande, List<LigneBonCommandeWithImageList>> by mutableStateOf(
        emptyMap()
    )
        private set

    var notSyncDepOutBatimentObj: String by mutableStateOf("")
        private set


    private fun getNotSyncDepOutBatiment() {
        viewModelScope.launch {
            val depOutBatimentNotSyncFlow = proCaisseLocalDb.invePatrimoine.notSynced(
                typeInv = TypePatrimoine.SORTIE.typePat,
                devEtat = IMMOBILISATION
            ).distinctUntilChanged()


            combine(
                networkFlow,
                depOutBatimentNotSyncFlow,
                autoSyncFlow
            ) { isConnected, depOutBatimentNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                depOutBatimentNotSyncList.ifEmpty { emptyMap() }
            }.sample(samplingInterval)/*.debounce(10000)*/.collect {

                if (it.isEmpty()) {
                    depOutBatimentNotSync = emptyMap()
                    return@collect
                }
                depOutBatimentNotSync = it

                // Only auto-sync if connected, auto-sync enabled, not already syncing, and not loading
                if (connected && autoSyncState && !isSyncInProgress && !responseAddDepOutBatimentState.loading) {
                    isSyncInProgress = true
                    syncDepOutBatiment()
                }
            }
        }
    }


    fun syncDepOutBatiment(selectedInvPat: BonCommande = BonCommande()) {
        viewModelScope.launch(dispatcherIO) {
            val listDepOutBatiment = ArrayList<NestedItem<BonCommande, List<LigneBonCommande>>>()

            depOutBatimentNotSync.forEach { (key, value) ->
                run {
                    listDepOutBatiment.add(
                        NestedItem(
                            parent = key,
                            children = value.map { it.ligneBonCommande ?: LigneBonCommande() }
                        )
                    )
                }
            }


            if (selectedInvPat != BonCommande()) {
                listDepOutBatiment.removeIf { it.parent?.devCodeM != selectedInvPat.devCodeM }
            }

            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                    ?.let { Json.decodeFromString(it) } ?: BaseConfig(),
                Json.encodeToJsonElement(listDepOutBatiment)
            )

            notSyncDepOutBatimentObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.inventairePatrimoine.addBatchInvPat(notSyncDepOutBatimentObj)
                .onEach { result ->
                    when (result) {
                        is DataResult.Success -> {

                            setStateSynced(
                                invPatResponse = result.data!!,
                                listVisiteWithLinesDn = listDepOutBatiment
                            )
                            responseAddDepOutBatimentState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )
                            isSyncInProgress = false // Reset sync progress flag
                        }

                        is DataResult.Loading -> {
                            responseAddDepOutBatimentState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            responseAddDepOutBatimentState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message,
                                message = selectedInvPat.devCodeM
                            )
                            isSyncInProgress = false // Reset sync progress flag on error
                        }
                    }
                }/*.flowOn(dispatcherIO)*/.launchIn(this)
        }
    }


    var depOutBatimentImageNotSync: List<ImagePieceJoint> by mutableStateOf(emptyList())
        private set

    private fun getImageNotSyncDepOutBatiment() {
        viewModelScope.launch {
            proCaisseLocalDb.inventairePieceJoint.noSynced(
                typeVC = IMMOBILISATION + ";" + TypePatrimoine.SORTIE.typePat
            ).distinctUntilChanged().sample(samplingInterval)/*.debounce(10000)*/.collectLatest {
                depOutBatimentImageNotSync = it

                // Only auto-sync if connected, auto-sync enabled, not already syncing, and not loading
                if (it.isNotEmpty() && connected && autoSyncState && !isSyncInProgress && !responseImageDepOutBatimentState.loading) {
                    isSyncInProgress = true
                    syncImageDepOutBatiment()
                }
            }
        }
    }


    var responseImageDepOutBatimentState: RemoteResponseState<List<AddPieceJointInventaire>> by mutableStateOf(
        RemoteResponseState()
    )
        private set

    var notSyncImageDepOutBatimentObj: String by mutableStateOf("")
        private set

    fun syncImageDepOutBatiment() {
        viewModelScope.launch(dispatcherIO) {


            val listImageDepOutBatiment = depOutBatimentImageNotSync
                .groupBy { it.devNum }
                .map { (parentDevNum, children) ->
                    NestedItem(
                        parent = Parent(dEVNum = parentDevNum),
                        children = children
                    )
                }


            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                    ?.let { Json.decodeFromString(it) } ?: BaseConfig(),
                Json.encodeToJsonElement(listImageDepOutBatiment)
            )

            notSyncImageDepOutBatimentObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.inventairePatrimoine.addBatchPiecJointInventaire(
                notSyncImageDepOutBatimentObj
            ).onEach { result ->
                when (result) {
                    is DataResult.Success -> {

                        for (image in result.data!!) {
                            if (image.code == 10200) {
                                proCaisseLocalDb.inventairePieceJoint.updateImageState(image.codeIMG)
                            }
                        }


                        responseImageDepOutBatimentState =
                            RemoteResponseState(data = result.data, loading = false, error = null)
                        isSyncInProgress = false // Reset sync progress flag
                    }

                    is DataResult.Loading -> {
                        responseImageDepOutBatimentState =
                            RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        responseImageDepOutBatimentState = RemoteResponseState(
                            data = null,
                            loading = false,
                            error = result.message
                        )
                        isSyncInProgress = false // Reset sync progress flag on error
                    }
                }
            }/*.flowOn(dispatcherIO)*/.launchIn(this)
        }
    }


    /**
     ************************************************************* DEP IN **************************************************************
     */
    var responseAddDepInState: RemoteResponseState<List<InvPatBatchResponse>> by mutableStateOf(
        RemoteResponseState()
    )
        private set


    var depInNotSync: Map<BonCommande, List<LigneBonCommandeWithImageList>> by mutableStateOf(
        emptyMap()
    )
        private set

    var notSyncDepInObj: String by mutableStateOf("")
        private set

    private fun getNotSyncDepIn() {
        viewModelScope.launch {
            val depInNotSyncFlow = proCaisseLocalDb.invePatrimoine.notSynced(
                typeInv = TypePatrimoine.ENTREE.typePat,
                devEtat = PATRIMOINE
            ).distinctUntilChanged()


            combine(
                networkFlow,
                depInNotSyncFlow,
                autoSyncFlow
            ) { isConnected, depInNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                depInNotSyncList.ifEmpty { emptyMap() }
            }.sample(samplingInterval)/*.debounce(10000)*/.collect {

                if (it.isEmpty()) {
                    depInNotSync = emptyMap()
                    return@collect
                }
                depInNotSync = it

                // Only auto-sync if connected, auto-sync enabled, not already syncing, and not loading
                if (connected && autoSyncState && !isSyncInProgress && !responseAddDepInState.loading) {
                    isSyncInProgress = true
                    syncDepIn()
                }
            }

        }
    }


    fun syncDepIn(selectedInvPatrimoine: Map<BonCommande, List<LigneBonCommande>> = emptyMap()) {
        viewModelScope.launch(dispatcherIO) {
            val listVisiteWithLinesDn = ArrayList<NestedItem<BonCommande, List<LigneBonCommande>>>()

            depInNotSync.forEach { (key, value) ->
                run {
                    listVisiteWithLinesDn.add(
                        NestedItem(
                            parent = key,
                            children = value.map { it.ligneBonCommande ?: LigneBonCommande() }
                        )
                    )
                }
            }

            if (selectedInvPatrimoine != emptyMap<BonCommande, List<LigneBonCommande>>()) {
                listVisiteWithLinesDn.clear()

                selectedInvPatrimoine.forEach { (key, value) ->
                    run {
                        listVisiteWithLinesDn.add(
                            NestedItem(
                                parent = key,
                                children = value.map { it }.toList()
                            )
                        )
                    }
                }
            }


            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                    ?.let { Json.decodeFromString(it) } ?: BaseConfig(),
                Json.encodeToJsonElement(listVisiteWithLinesDn)
            )

            notSyncDepInObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.inventairePatrimoine.addBatchInvPat(notSyncDepInObj).onEach { result ->
                when (result) {
                    is DataResult.Success -> {

                        setStateSynced(
                            invPatResponse = result.data!!,
                            listVisiteWithLinesDn = listVisiteWithLinesDn
                        )
                        responseAddDepInState =
                            RemoteResponseState(data = result.data, loading = false, error = null)
                        isSyncInProgress = false // Reset sync progress flag
                    }

                    is DataResult.Loading -> {
                        responseAddDepInState =
                            RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        responseAddDepInState = RemoteResponseState(
                            data = null,
                            loading = false,
                            error = result.message,
                            message = selectedInvPatrimoine.keys.first().dEVNum
                        )
                        isSyncInProgress = false // Reset sync progress flag on error
                    }
                }
            }/*.flowOn(dispatcherIO)*/.launchIn(this)
        }
    }


    /**
     * **************************************************************
     */
    var responseAddDepInBatimentState: RemoteResponseState<List<InvPatBatchResponse>> by mutableStateOf(
        RemoteResponseState()
    )
        private set


    var depInBatimentNotSync: Map<BonCommande, List<LigneBonCommandeWithImageList>> by mutableStateOf(
        emptyMap()
    )
        private set

    var notSyncDepInBatimentObj: String by mutableStateOf("")
        private set


    private fun getNotSyncDepInBatiment() {
        viewModelScope.launch {
            val depInBatimentNotSyncFlow = proCaisseLocalDb.invePatrimoine.notSynced(
                typeInv = TypePatrimoine.ENTREE.typePat,
                devEtat = IMMOBILISATION
            ).distinctUntilChanged()


            combine(
                networkFlow,
                depInBatimentNotSyncFlow,
                autoSyncFlow
            ) { isConnected, depInBatimentNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                depInBatimentNotSyncList.ifEmpty { emptyMap() }
            }.sample(samplingInterval)/*.debounce(10000)*/.collect {

                if (it.isEmpty()) {
                    depInBatimentNotSync = emptyMap()
                    return@collect
                }
                depInBatimentNotSync = it

                // Only auto-sync if connected, auto-sync enabled, not already syncing, and not loading
                if (connected && autoSyncState && !isSyncInProgress && !responseAddDepInBatimentState.loading) {
                    isSyncInProgress = true
                    syncDepInBatiment()
                }
            }


        }
    }


    fun syncDepInBatiment(selectedInvPat: BonCommande = BonCommande()) {
        viewModelScope.launch(dispatcherIO) {
            val listDepInBatiment = ArrayList<NestedItem<BonCommande, List<LigneBonCommande>>>()

            depInBatimentNotSync.forEach { (key, value) ->
                run {
                    listDepInBatiment.add(
                        NestedItem(
                            parent = key,
                            children = value.map { it.ligneBonCommande ?: LigneBonCommande() }
                        )
                    )
                }
            }


            if (selectedInvPat != BonCommande()) {
                listDepInBatiment.removeIf { it.parent?.devCodeM != selectedInvPat.devCodeM }
            }

            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                    ?.let { Json.decodeFromString(it) } ?: BaseConfig(),
                Json.encodeToJsonElement(listDepInBatiment)
            )

            notSyncDepInBatimentObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.inventairePatrimoine.addBatchInvPat(notSyncDepInBatimentObj)
                .onEach { result ->
                    when (result) {
                        is DataResult.Success -> {

                            setStateSynced(
                                invPatResponse = result.data!!,
                                listVisiteWithLinesDn = listDepInBatiment
                            )
                            responseAddDepInBatimentState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )
                            isSyncInProgress = false // Reset sync progress flag
                        }

                        is DataResult.Loading -> {
                            responseAddDepInBatimentState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            responseAddDepInBatimentState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message,
                                message = selectedInvPat.devCodeM
                            )
                            isSyncInProgress = false // Reset sync progress flag on error
                        }
                    }
                }/*.flowOn(dispatcherIO)*/.launchIn(this)
        }
    }


    var depInBatimentImageNotSync: List<ImagePieceJoint> by mutableStateOf(emptyList())
        private set

    private fun getImageNotSyncDepInBatiment() {
        viewModelScope.launch {
            val depInBatimentImageNotSyncFlow = proCaisseLocalDb.inventairePieceJoint.noSynced(
                typeVC = IMMOBILISATION + ";" + TypePatrimoine.ENTREE.typePat
            ).distinctUntilChanged()


            combine(
                networkFlow,
                depInBatimentImageNotSyncFlow,
                autoSyncFlow
            ) { isConnected, depInBatimentImageNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                depInBatimentImageNotSyncList.ifEmpty { emptyList() }
            }.sample(samplingInterval)/*.debounce(10000)*/.collect {

                if (it.isEmpty()) {
                    depInBatimentImageNotSync = emptyList()
                    return@collect
                }
                depInBatimentImageNotSync = it

                // Only auto-sync if connected, auto-sync enabled, not already syncing, and not loading
                if (connected && autoSyncState && !isSyncInProgress && !responseImageDepInBatimentState.loading) {
                    isSyncInProgress = true
                    syncImageDepInBatiment()
                }
            }
        }
    }


    var responseImageDepInBatimentState: RemoteResponseState<List<AddPieceJointInventaire>> by mutableStateOf(
        RemoteResponseState()
    )
        private set
    var notSyncImageDepInBatimentObj: String by mutableStateOf("")
        private set

    fun syncImageDepInBatiment() {
        viewModelScope.launch(dispatcherIO) {

            val listImageDepInBatiment = depInBatimentImageNotSync
                .groupBy { it.devNum }
                .map { (parentDevNum, children) ->
                    NestedItem(
                        parent = Parent(dEVNum = parentDevNum),
                        children = children
                    )
                }


            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                    ?.let { Json.decodeFromString(it) } ?: BaseConfig(),
                Json.encodeToJsonElement(listImageDepInBatiment)
            )

            notSyncImageDepInBatimentObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.inventairePatrimoine.addBatchPiecJointInventaire(
                notSyncImageDepInBatimentObj
            ).onEach { result ->
                when (result) {
                    is DataResult.Success -> {

                        for (image in result.data!!) {
                            if (image.code == 10200) {
                                proCaisseLocalDb.inventairePieceJoint.updateImageState(image.codeIMG)
                            }
                        }


                        responseImageDepInBatimentState =
                            RemoteResponseState(data = result.data, loading = false, error = null)
                        isSyncInProgress = false // Reset sync progress flag
                    }

                    is DataResult.Loading -> {
                        responseImageDepInBatimentState =
                            RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        responseImageDepInBatimentState = RemoteResponseState(
                            data = null,
                            loading = false,
                            error = result.message
                        )
                        isSyncInProgress = false // Reset sync progress flag on error
                    }
                }
            }/*.flowOn(dispatcherIO)*/.launchIn(this)
        }
    }

    /**
     * ******************************************  I N V E N T A I R E *************************************
     */
    var responseAddInventaireState: RemoteResponseState<List<InvPatBatchResponse>> by mutableStateOf(
        RemoteResponseState()
    )
        private set

    var inventaireNotSync: Map<BonCommande, List<LigneBonCommandeWithImageList>> by mutableStateOf(
        emptyMap()
    )
        private set


    var notSyncInventaireObj: String by mutableStateOf("")
        private set

    private fun getNotSyncInventaire() {
        viewModelScope.launch {
            val inventaireNotSyncFlow = proCaisseLocalDb.invePatrimoine.notSynced(
                typeInv = TypePatrimoine.INVENTAIRE.typePat,
                devEtat = PATRIMOINE
            ).distinctUntilChanged()


            combine(
                networkFlow,
                inventaireNotSyncFlow,
                autoSyncFlow
            ) { isConnected, inventaireNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                inventaireNotSyncList.ifEmpty { emptyMap() }
            }.sample(samplingInterval)/*.debounce(10000)*/.collect {

                if (it.isEmpty()) {
                    inventaireNotSync = emptyMap()
                    return@collect
                }
                inventaireNotSync = it

                // Only auto-sync if connected, auto-sync enabled, not already syncing, and not loading
                if (connected && autoSyncState && !isSyncInProgress && !responseAddInventaireState.loading) {
                    isSyncInProgress = true
                    syncInventaire()
                }
            }
        }
    }


    fun syncInventaire(selectedInvPatrimoine: Map<BonCommande, List<LigneBonCommande>> = emptyMap()) {
        viewModelScope.launch(dispatcherIO) {


            val listVisiteWithLinesDn = ArrayList<NestedItem<BonCommande, List<LigneBonCommande>>>()

            inventaireNotSync.forEach { (key, value) ->
                run {
                    listVisiteWithLinesDn.add(
                        NestedItem(
                            parent = key,
                            children = value.map { it.ligneBonCommande ?: LigneBonCommande() }
                        )
                    )
                }
            }


            if (selectedInvPatrimoine != emptyMap<BonCommande, List<LigneBonCommande>>()) {
                listVisiteWithLinesDn.clear()

                selectedInvPatrimoine.forEach { (key, value) ->
                    run {
                        listVisiteWithLinesDn.add(
                            NestedItem(
                                parent = key,
                                children = value.map { it }.toList()
                            )
                        )
                    }
                }
            }

            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                    ?.let { Json.decodeFromString(it) } ?: BaseConfig(),
                Json.encodeToJsonElement(listVisiteWithLinesDn)
            )

            notSyncInventaireObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.inventairePatrimoine.addBatchInvPat(notSyncInventaireObj)
                .onEach { result ->
                    when (result) {
                        is DataResult.Success -> {

                            setStateSynced(
                                invPatResponse = result.data!!,
                                listVisiteWithLinesDn = listVisiteWithLinesDn
                            )
                            responseAddInventaireState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )
                            isSyncInProgress = false // Reset sync progress flag
                        }

                        is DataResult.Loading -> {
                            responseAddInventaireState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            responseAddInventaireState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message,
                                message = selectedInvPatrimoine.keys.first().dEVNum
                            )
                            isSyncInProgress = false // Reset sync progress flag on error
                        }
                    }
                }/*.flowOn(dispatcherIO)*/.launchIn(this)
        }
    }

    /**
     * **********************************************************************
     */

    var responseAddInventaireBatimentState: RemoteResponseState<List<InvPatBatchResponse>> by mutableStateOf(
        RemoteResponseState()
    )
        private set

    var inventaireBatimentNotSync: Map<BonCommande, List<LigneBonCommandeWithImageList>> by mutableStateOf(
        emptyMap()
    )
        private set

    var notSyncInventaireBatimentObj: String by mutableStateOf("")
        private set

    private fun getNotSyncInventaireBatiment() {
        viewModelScope.launch {
            val inventaireBatimentNotSyncFlow = proCaisseLocalDb.invePatrimoine.notSynced(
                typeInv = TypePatrimoine.INVENTAIRE.typePat,
                devEtat = IMMOBILISATION
            ).distinctUntilChanged()


            combine(
                networkFlow,
                inventaireBatimentNotSyncFlow,
                autoSyncFlow
            ) { isConnected, inventaireBatimentNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                inventaireBatimentNotSyncList.ifEmpty { emptyMap() }
            }.sample(samplingInterval)/*.debounce(10000)*/.collect {

                if (it.isEmpty()) {
                    inventaireBatimentNotSync = emptyMap()
                    return@collect
                }
                inventaireBatimentNotSync = it

                // Only auto-sync if connected, auto-sync enabled, not already syncing, and not loading
                if (connected && autoSyncState && !isSyncInProgress && !responseAddInventaireBatimentState.loading) {
                    isSyncInProgress = true
                    syncInventaireBatiment()
                }
            }
        }
    }


    fun syncInventaireBatiment(selectedInvPat: BonCommande = BonCommande()) {
        viewModelScope.launch(dispatcherIO) {

            // if(!listenNetwork.isConnected.first()) return@launch
            val listInvBatimentToSync = ArrayList<NestedItem<BonCommande, List<LigneBonCommande>>>()

            inventaireBatimentNotSync.forEach { (key, value) ->
                run {
                    listInvBatimentToSync.add(
                        NestedItem(
                            parent = key,
                            children = value.map { it.ligneBonCommande ?: LigneBonCommande() }
                        )
                    )
                }
            }
            if (selectedInvPat != BonCommande()) {
                listInvBatimentToSync.removeIf { it.parent?.devCodeM != selectedInvPat.devCodeM }
            }


            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                    ?.let { Json.decodeFromString(it) } ?: BaseConfig(),
                Json.encodeToJsonElement(listInvBatimentToSync)
            )
            notSyncInventaireBatimentObj = Json.encodeToString(baseConfigObj)

            proCaisseRemote.inventairePatrimoine.addBatchInvPat(notSyncInventaireBatimentObj)
                .onEach { result ->
                    when (result) {
                        is DataResult.Success -> {

                            setStateSynced(
                                invPatResponse = result.data!!,
                                listVisiteWithLinesDn = listInvBatimentToSync
                            )
                            responseAddInventaireBatimentState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null
                            )
                            isSyncInProgress = false // Reset sync progress flag
                        }

                        is DataResult.Loading -> {
                            responseAddInventaireBatimentState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            responseAddInventaireBatimentState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message,
                                message = selectedInvPat.devCodeM
                            )
                            isSyncInProgress = false // Reset sync progress flag on error
                        }
                    }
                }/*.flowOn(dispatcherIO)*/.launchIn(this)
        }
    }


    var inventaireBatimentImageNotSync: List<ImagePieceJoint> by mutableStateOf(emptyList())
        private set

    private fun getImageNotSyncInventaireBatiment() {
        viewModelScope.launch {

            val inventaireBatimentImageNotSyncFlow = proCaisseLocalDb.inventairePieceJoint.noSynced(
                typeVC = IMMOBILISATION + ";" + TypePatrimoine.INVENTAIRE.typePat
            ).distinctUntilChanged()


            combine(
                networkFlow,
                inventaireBatimentImageNotSyncFlow,
                autoSyncFlow
            ) { isConnected, inventaireBatimentImageNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                inventaireBatimentImageNotSyncList.ifEmpty { emptyList() }
            }.sample(samplingInterval)/*.debounce(10000)*/.collect {

                if (it.isEmpty()) {
                    return@collect
                }
                inventaireBatimentImageNotSync = it

                // Only auto-sync if connected, auto-sync enabled, not already syncing, and not loading
                if (connected && autoSyncState && !isSyncInProgress && !responseImageInventaireBatimentState.loading) {
                    isSyncInProgress = true
                    syncImageInventaireBatiment()
                }
            }
        }
    }


    var responseImageInventaireBatimentState: RemoteResponseState<List<AddPieceJointInventaire>> by mutableStateOf(
        RemoteResponseState()
    )
        private set
    var notSyncImageInventaireBatimentObj: String by mutableStateOf("")
        private set

    fun syncImageInventaireBatiment() {
        viewModelScope.launch(dispatcherIO) {


            val listImageInventaireBatiment = inventaireBatimentImageNotSync
                .groupBy { it.devNum }
                .map { (parentDevNum, children) ->
                    NestedItem(
                        parent = Parent(dEVNum = parentDevNum),
                        children = children
                    )
                }

            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                    ?.let { Json.decodeFromString(it) } ?: BaseConfig(),
                Json.encodeToJsonElement(listImageInventaireBatiment)
            )

            notSyncImageInventaireBatimentObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.inventairePatrimoine.addBatchPiecJointInventaire(
                notSyncImageInventaireBatimentObj
            ).onEach { result ->
                when (result) {
                    is DataResult.Success -> {

                        for (image in result.data!!) {
                            if (image.code == 10200)
                                proCaisseLocalDb.inventairePieceJoint.updateImageState(image.codeIMG)
                        }


                        responseImageInventaireBatimentState =
                            RemoteResponseState(data = result.data, loading = false, error = null)
                        isSyncInProgress = false // Reset sync progress flag
                    }

                    is DataResult.Loading -> {
                        responseImageInventaireBatimentState =
                            RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        responseImageInventaireBatimentState = RemoteResponseState(
                            data = null,
                            loading = false,
                            error = result.message
                        )
                        isSyncInProgress = false // Reset sync progress flag on error
                    }
                }
            }/*.flowOn(dispatcherIO)*/.launchIn(this)
        }
    }

    /////////////////////////////////////////

    private fun setStateSynced(
        invPatResponse: List<InvPatBatchResponse>?,
        listVisiteWithLinesDn: ArrayList<NestedItem<BonCommande, List<LigneBonCommande>>>
    ) {
        for (i in invPatResponse!!.indices) {
            if (invPatResponse[i].code == 10200 || invPatResponse[i].code == 10701) {
                proCaisseLocalDb.invePatrimoine.setSynced(
                    devNum = invPatResponse[i].dEVNum,
                    devNumM = listVisiteWithLinesDn[i].parent!!.devCodeM
                )

                proCaisseLocalDb.invePatrimoine.setLgSynced(
                    devNum = invPatResponse[i].dEVNum,
                    devNumM = listVisiteWithLinesDn[i].parent!!.devCodeM
                )
            }

        }
    }
}