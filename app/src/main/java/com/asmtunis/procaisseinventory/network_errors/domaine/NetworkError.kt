package com.asmtunis.procaisseinventory.network_errors.domaine

import androidx.room.Entity
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.simapps.ui_kit.utils.getCurrentDateTime
import kotlinx.serialization.Serializable

@Entity(tableName = ProCaisseConstants.NETWORK_ERRORS, primaryKeys = ["url", "extraInfo"])
@Serializable
data class NetworkError(
//@PrimaryKey(autoGenerate = true)
  //  val id: Long,

    val url: String = "",

    val extraInfo: String = "",

    val errorMessage: String? = null,

    val date: String = getCurrentDateTime()
)
