{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "9fbbf6b765d054789e7c387ea9e4cb29", "packages": [{"name": "alexusmai/laravel-file-manager", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/alexusmai/laravel-file-manager.git", "reference": "1ad25434eb321cc208dbd3cecca504bd4f774b57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alexusmai/laravel-file-manager/zipball/1ad25434eb321cc208dbd3cecca504bd4f774b57", "reference": "1ad25434eb321cc208dbd3cecca504bd4f774b57", "shasum": ""}, "require": {"ext-json": "*", "ext-zip": "*", "intervention/image": "^2.4", "intervention/imagecache": "^2.3", "laravel/framework": "^5.5|^6.0|^7.0|^8.0", "league/flysystem": "^1.0", "php": ">=7.1.0"}, "type": "library", "extra": {"laravel": {"providers": ["Alexusmai\\LaravelFileManager\\FileManagerServiceProvider"]}}, "autoload": {"psr-4": {"Alexusmai\\LaravelFileManager\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "File manager for Laravel", "homepage": "https://github.com/alexusami/laravel-file-manager", "keywords": ["file", "laravel", "manager"], "support": {"issues": "https://github.com/alexusmai/laravel-file-manager/issues", "source": "https://github.com/alexusmai/laravel-file-manager/tree/v2.5.4"}, "time": "2021-03-31T17:42:31+00:00"}, {"name": "arcanedev/log-viewer", "version": "3.4.0", "source": {"type": "git", "url": "https://github.com/ARCANEDEV/LogViewer.git", "reference": "4b361568b0b841183d92cf1d0e9ef27350044323"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ARCANEDEV/LogViewer/zipball/4b361568b0b841183d92cf1d0e9ef27350044323", "reference": "4b361568b0b841183d92cf1d0e9ef27350044323", "shasum": ""}, "require": {"arcanedev/support": "~2.0", "php": ">=5.4.0", "psr/log": "~1.0"}, "require-dev": {"orchestra/testbench": "~3.0", "phpunit/phpcov": "~2.0", "phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"files": ["constants.php", "helpers.php"], "psr-4": {"Arcanedev\\LogViewer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "ARCANEDEV", "email": "<EMAIL>", "homepage": "https://github.com/ARCANEDEV"}], "description": "Provides a Log Viewer for Laravel 5", "homepage": "https://github.com/ARCANEDEV/LogViewer", "keywords": ["arcanesoft", "laravel", "log", "log viewer", "logviewer"], "support": {"issues": "https://github.com/ARCANEDEV/LogViewer/issues", "source": "https://github.com/ARCANEDEV/LogViewer/tree/master"}, "time": "2015-09-11T17:40:41+00:00"}, {"name": "arcanedev/support", "version": "2.1.4", "source": {"type": "git", "url": "https://github.com/ARCANEDEV/Support.git", "reference": "e603730820cb6b8e51b02a4cb6661c18d8771b5d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ARCANEDEV/Support/zipball/e603730820cb6b8e51b02a4cb6661c18d8771b5d", "reference": "e603730820cb6b8e51b02a4cb6661c18d8771b5d", "shasum": ""}, "require": {"illuminate/filesystem": "~5.0", "illuminate/support": "~5.0", "patchwork/utf8": "~1.1", "php": ">=5.4.0"}, "require-dev": {"orchestra/testbench": "~3.0", "phpunit/phpcov": "~2.0", "phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"files": ["constants.php", "helpers.php"], "psr-4": {"Arcanedev\\Support\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "ARCANEDEV", "email": "<EMAIL>", "homepage": "https://github.com/ARCANEDEV"}], "description": "ARCANEDEV Support Helpers", "keywords": ["arcanedev", "support"], "support": {"issues": "https://github.com/ARCANEDEV/Support/issues", "source": "https://github.com/ARCANEDEV/Support/tree/2.1.4"}, "time": "2015-09-09T23:15:28+00:00"}, {"name": "carbonphp/carbon-doctrine-types", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "reference": "3c430083d0b41ceed84ecccf9dac613241d7305d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/3c430083d0b41ceed84ecccf9dac613241d7305d", "reference": "3c430083d0b41ceed84ecccf9dac613241d7305d", "shasum": ""}, "require": {"php": "^7.1.8 || ^8.0"}, "conflict": {"doctrine/dbal": ">=3.7.0"}, "require-dev": {"doctrine/dbal": ">=2.0.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "type": "library", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/1.0.0"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2023-10-01T12:35:29+00:00"}, {"name": "chumper/zipper", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/Chumper/Zipper.git", "reference": "d15207e010f8fe1bdd341376bd86d599c4166423"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Chumper/Zipper/zipball/d15207e010f8fe1bdd341376bd86d599c4166423", "reference": "d15207e010f8fe1bdd341376bd86d599c4166423", "shasum": ""}, "require": {"ext-zip": "*", "illuminate/filesystem": "^5.0", "illuminate/support": "^5.0", "php": ">=5.6.0"}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^5.7"}, "type": "library", "extra": {"laravel": {"providers": ["Chumper\\Zipper\\ZipperServiceProvider"], "aliases": {"Zipper": "Chumper\\Zipper\\Zipper"}}}, "autoload": {"psr-4": {"Chumper\\Zipper\\": "src/<PERSON><PERSON>/<PERSON>per"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache2"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://nilsplaschke.de", "role": "Developer"}], "description": "This is a little neat helper for the ZipArchive methods with handy functions", "homepage": "http://github.com/Chumper/zipper", "keywords": ["archive", "laravel", "zip"], "support": {"issues": "https://github.com/Chumper/Zipper/issues", "source": "https://github.com/Chumper/Zipper/tree/v1.0.3"}, "abandoned": true, "time": "2020-02-25T11:57:40+00:00"}, {"name": "clue/stream-filter", "version": "v1.7.0", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/049509fef80032cb3f051595029ab75b49a3c2f7", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"Clue\\StreamFilter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "support": {"issues": "https://github.com/clue/stream-filter/issues", "source": "https://github.com/clue/stream-filter/tree/v1.7.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2023-12-20T15:40:13+00:00"}, {"name": "darkaonline/l5-swagger", "version": "5.8.5", "source": {"type": "git", "url": "https://github.com/DarkaOnLine/L5-Swagger.git", "reference": "fddbeed402553c7548e3389d2c501c4fdad57dbe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DarkaOnLine/L5-Swagger/zipball/fddbeed402553c7548e3389d2c501c4fdad57dbe", "reference": "fddbeed402553c7548e3389d2c501c4fdad57dbe", "shasum": ""}, "require": {"laravel/framework": "5.6.*|5.7.*|5.8.*", "php": ">=7.1.3", "swagger-api/swagger-ui": "^3.0", "symfony/yaml": "^4.1", "zircote/swagger-php": "~2.0|3.*"}, "require-dev": {"mockery/mockery": "1.*", "orchestra/testbench": "3.6.*|3.8.*", "php-coveralls/php-coveralls": "^2.0", "phpunit/phpunit": "7.*"}, "suggest": {"zircote/swagger-php:~2.0": "!!! Require Swagger-PHP ~2.0 for @SWG annotations support !!!"}, "type": "library", "extra": {"laravel": {"providers": ["L5Swagger\\L5SwaggerServiceProvider"]}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"L5Swagger\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Swagger integration to Laravel 5", "keywords": ["api", "laravel", "swagger"], "support": {"issues": "https://github.com/DarkaOnLine/L5-Swagger/issues", "source": "https://github.com/DarkaOnLine/L5-Swagger/tree/5.8.x"}, "funding": [{"url": "https://github.com/DarkaOnLine", "type": "github"}], "time": "2020-08-21T05:39:35+00:00"}, {"name": "dnoegel/php-xdg-base-dir", "version": "v0.1.1", "source": {"type": "git", "url": "https://github.com/dnoegel/php-xdg-base-dir.git", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dnoegel/php-xdg-base-dir/zipball/8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~7.0|~6.0|~5.0|~4.8.35"}, "type": "library", "autoload": {"psr-4": {"XdgBaseDir\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "implementation of xdg base directory specification for php", "support": {"issues": "https://github.com/dnoegel/php-xdg-base-dir/issues", "source": "https://github.com/dnoegel/php-xdg-base-dir/tree/v0.1.1"}, "time": "2019-12-04T15:06:13+00:00"}, {"name": "doctrine/annotations", "version": "1.14.3", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af", "reference": "fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af", "shasum": ""}, "require": {"doctrine/lexer": "^1 || ^2", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6", "vimeo/psalm": "^4.10"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.3"}, "time": "2023-02-01T09:20:38+00:00"}, {"name": "doctrine/cache", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/1ca8f21980e770095a31456042471a57bc4c68fb", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:07:39+00:00"}, {"name": "doctrine/dbal", "version": "2.13.9", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "c480849ca3ad6706a39c970cdfe6888fa8a058b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/c480849ca3ad6706a39c970cdfe6888fa8a058b8", "reference": "c480849ca3ad6706a39c970cdfe6888fa8a058b8", "shasum": ""}, "require": {"doctrine/cache": "^1.0|^2.0", "doctrine/deprecations": "^0.5.3|^1", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.1 || ^8"}, "require-dev": {"doctrine/coding-standard": "9.0.0", "jetbrains/phpstorm-stubs": "2021.1", "phpstan/phpstan": "1.4.6", "phpunit/phpunit": "^7.5.20|^8.5|9.5.16", "psalm/plugin-phpunit": "0.16.1", "squizlabs/php_codesniffer": "3.6.2", "symfony/cache": "^4.4", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0", "vimeo/psalm": "4.22.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/2.13.9"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2022-05-02T20:28:55+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "1.4.10 || 1.10.15", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "0.18.4", "psr/log": "^1 || ^2 || ^3", "vimeo/psalm": "4.30.0 || 5.12.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.3"}, "time": "2024-01-30T19:34:25+00:00"}, {"name": "doctrine/event-manager", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/95aa4cb529f1e96576f3fda9f5705ada4056a520", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2022-10-12T20:51:15+00:00"}, {"name": "doctrine/inflector", "version": "1.4.4", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector", "Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/1.4.4"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2021-04-16T17:34:40+00:00"}, {"name": "doctrine/lexer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/c268e882d4dbdd85e36e4ad69e02dc284f89d229", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-02-28T11:07:21+00:00"}, {"name": "dompdf/dompdf", "version": "v1.2.2", "source": {"type": "git", "url": "https://github.com/dompdf/dompdf.git", "reference": "5031045d9640b38cfc14aac9667470df09c9e090"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/dompdf/zipball/5031045d9640b38cfc14aac9667470df09c9e090", "reference": "5031045d9640b38cfc14aac9667470df09c9e090", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "phenx/php-font-lib": "^0.5.4", "phenx/php-svg-lib": "^0.3.3 || ^0.4.0", "php": "^7.1 || ^8.0"}, "require-dev": {"ext-json": "*", "ext-zip": "*", "mockery/mockery": "^1.3", "phpunit/phpunit": "^7.5 || ^8 || ^9", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"ext-gd": "Needed to process images", "ext-gmagick": "Improves image processing performance", "ext-imagick": "Improves image processing performance", "ext-zlib": "Needed for pdf stream compression"}, "type": "library", "autoload": {"psr-4": {"Dompdf\\": "src/"}, "classmap": ["lib/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "DOMPDF is a CSS 2.1 compliant HTML to PDF converter", "homepage": "https://github.com/dompdf/dompdf", "support": {"issues": "https://github.com/dompdf/dompdf/issues", "source": "https://github.com/dompdf/dompdf/tree/v1.2.2"}, "time": "2022-04-27T13:50:54+00:00"}, {"name": "dragonmantank/cron-expression", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "65b2d8ee1f10915efb3b55597da3404f096acba2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/65b2d8ee1f10915efb3b55597da3404f096acba2", "reference": "65b2d8ee1f10915efb3b55597da3404f096acba2", "shasum": ""}, "require": {"php": "^7.0|^8.0"}, "require-dev": {"phpunit/phpunit": "^6.4|^7.0|^8.0|^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v2.3.1"}, "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "time": "2020-10-13T00:52:37+00:00"}, {"name": "earlpeterg/php-password-decipher", "version": "0.1.0", "source": {"type": "git", "url": "https://bitbucket.org/EarlPeterG/php-password-decipher.git", "reference": "a46ced353d3c5299474f21555935584cc0ed66c5"}, "dist": {"type": "zip", "url": "https://bitbucket.org/EarlPeterG/php-password-decipher/get/a46ced353d3c5299474f21555935584cc0ed66c5.zip", "reference": "a46ced353d3c5299474f21555935584cc0ed66c5", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"files": ["PasswordDecipher.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "EarlPeterG", "email": "<EMAIL>"}], "description": "Password Decipher class that utilizes SHA1 to create secure passwords with salt.", "homepage": "https://bitbucket.org/EarlPeterG/php-password-decipher", "support": {"source": "https://bitbucket.org/EarlPeterG/php-password-decipher/src/a46ced353d3c5299474f21555935584cc0ed66c5/?at=master"}, "time": "2018-08-27T10:25:34+00:00"}, {"name": "egulias/email-validator", "version": "2.1.25", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/0dbf5d78455d4d6a41d186da50adc1122ec066f4", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4", "shasum": ""}, "require": {"doctrine/lexer": "^1.0.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.10"}, "require-dev": {"dominicsayers/isemail": "^3.0.7", "phpunit/phpunit": "^4.8.36|^7.5.15", "satooshi/php-coveralls": "^1.0.1"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/2.1.25"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2020-12-29T14:50:06+00:00"}, {"name": "erusev/parsedown", "version": "1.7.4", "source": {"type": "git", "url": "https://github.com/erusev/parsedown.git", "reference": "cb17b6477dfff935958ba01325f2e8a2bfa6dab3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/erusev/parsedown/zipball/cb17b6477dfff935958ba01325f2e8a2bfa6dab3", "reference": "cb17b6477dfff935958ba01325f2e8a2bfa6dab3", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35"}, "type": "library", "autoload": {"psr-0": {"Parsedown": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://erusev.com"}], "description": "<PERSON><PERSON>r for <PERSON>down.", "homepage": "http://parsedown.org", "keywords": ["markdown", "parser"], "support": {"issues": "https://github.com/erusev/parsedown/issues", "source": "https://github.com/erusev/parsedown/tree/1.7.x"}, "time": "2019-12-30T22:54:17+00:00"}, {"name": "geo-sot/laravel-env-editor", "version": "v0.9.12", "source": {"type": "git", "url": "https://github.com/GeoSot/Laravel-EnvEditor.git", "reference": "fc6f83787d86119bcdab016b2fdb8ebc57ebb1a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GeoSot/<PERSON>vel-EnvEditor/zipball/fc6f83787d86119bcdab016b2fdb8ebc57ebb1a5", "reference": "fc6f83787d86119bcdab016b2fdb8ebc57ebb1a5", "shasum": ""}, "require": {"laravel/framework": ">=5.5", "php": ">=7.1"}, "require-dev": {"orchestra/testbench": "^3"}, "type": "library", "extra": {"laravel": {"providers": ["GeoSot\\EnvEditor\\ServiceProvider"], "aliases": {"EnvEditor": "GeoSot\\EnvEditor\\Facades\\EnvEditor"}}}, "autoload": {"psr-4": {"GeoSot\\EnvEditor\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Geo Sot", "email": "<EMAIL>"}], "description": "A laravel Package that supports .Env File, editing and backup ", "keywords": ["EnvEditor", "geo-sot", "laravel", "laravel-env-editor"], "support": {"issues": "https://github.com/GeoSot/Laravel-EnvEditor/issues", "source": "https://github.com/GeoSot/Laravel-EnvEditor/tree/v0.9.12"}, "time": "2021-06-02T11:35:51+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.8.1", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/41042bc7ab002487b876a0683fc8dce04ddce104", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.1", "guzzlehttp/psr7": "^1.9.1 || ^2.5.1", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "php-http/client-integration-tests": "dev-master#2c025848417c1135031fdf9c728ee53d0a7ceaee as 3.0.999", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.36 || ^9.6.15", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.8.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2023-12-03T20:35:24+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "bbff78d96034045e58e13dedd6ad91b5d1253223"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/bbff78d96034045e58e13dedd6ad91b5d1253223", "reference": "bbff78d96034045e58e13dedd6ad91b5d1253223", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.36 || ^9.6.15"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2023-12-03T20:19:20+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.6.2", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "45b30f99ac27b5ca93cb4831afe16285f57b8221"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/45b30f99ac27b5ca93cb4831afe16285f57b8221", "reference": "45b30f99ac27b5ca93cb4831afe16285f57b8221", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5.36 || ^9.6.15"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.6.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2023-12-03T20:05:35+00:00"}, {"name": "http-interop/http-factory-guzzle", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/http-interop/http-factory-guzzle.git", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/http-interop/http-factory-guzzle/zipball/8f06e92b95405216b237521cc64c804dd44c4a81", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81", "shasum": ""}, "require": {"guzzlehttp/psr7": "^1.7||^2.0", "php": ">=7.3", "psr/http-factory": "^1.0"}, "provide": {"psr/http-factory-implementation": "^1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^9.5"}, "suggest": {"guzzlehttp/psr7": "Includes an HTTP factory starting in version 2.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Factory\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "An HTTP Factory using Guzzle PSR7", "keywords": ["factory", "http", "psr-17", "psr-7"], "support": {"issues": "https://github.com/http-interop/http-factory-guzzle/issues", "source": "https://github.com/http-interop/http-factory-guzzle/tree/1.2.0"}, "time": "2021-07-21T13:50:14+00:00"}, {"name": "intervention/image", "version": "2.7.2", "source": {"type": "git", "url": "https://github.com/Intervention/image.git", "reference": "04be355f8d6734c826045d02a1079ad658322dad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/image/zipball/04be355f8d6734c826045d02a1079ad658322dad", "reference": "04be355f8d6734c826045d02a1079ad658322dad", "shasum": ""}, "require": {"ext-fileinfo": "*", "guzzlehttp/psr7": "~1.1 || ^2.0", "php": ">=5.4.0"}, "require-dev": {"mockery/mockery": "~0.9.2", "phpunit/phpunit": "^4.8 || ^5.7 || ^7.5.15"}, "suggest": {"ext-gd": "to use GD library based image processing.", "ext-imagick": "to use Imagick based image processing.", "intervention/imagecache": "Caching extension for the Intervention Image library"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}, "laravel": {"providers": ["Intervention\\Image\\ImageServiceProvider"], "aliases": {"Image": "Intervention\\Image\\Facades\\Image"}}}, "autoload": {"psr-4": {"Intervention\\Image\\": "src/Intervention/Image"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://intervention.io/"}], "description": "Image handling and manipulation library with support for Laravel integration", "homepage": "http://image.intervention.io/", "keywords": ["gd", "image", "imagick", "laravel", "thumbnail", "watermark"], "support": {"issues": "https://github.com/Intervention/image/issues", "source": "https://github.com/Intervention/image/tree/2.7.2"}, "funding": [{"url": "https://paypal.me/interventionio", "type": "custom"}, {"url": "https://github.com/Intervention", "type": "github"}], "time": "2022-05-21T17:30:32+00:00"}, {"name": "intervention/imagecache", "version": "2.6.0", "source": {"type": "git", "url": "https://github.com/Intervention/imagecache.git", "reference": "86136575a62d3634b51f196a998fce4a583b49bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/imagecache/zipball/86136575a62d3634b51f196a998fce4a583b49bb", "reference": "86136575a62d3634b51f196a998fce4a583b49bb", "shasum": ""}, "require": {"illuminate/cache": "^5.5|~6|~7|~8|~9|~10", "illuminate/filesystem": "^5.5|~6|~7|~8|~9|~10", "intervention/image": "~2.2", "nesbot/carbon": "^2.39", "opis/closure": "^3.5", "php": "~7.2|~8"}, "require-dev": {"phpunit/phpunit": "^8.0"}, "type": "library", "autoload": {"psr-4": {"Intervention\\Image\\": "src/Intervention/Image"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://intervention.io/"}], "description": "Caching extension for the Intervention Image Class", "homepage": "https://image.intervention.io", "keywords": ["cache", "gd", "image", "imagick", "laravel"], "support": {"issues": "https://github.com/Intervention/imagecache/issues", "source": "https://github.com/Intervention/imagecache/tree/2.6.0"}, "funding": [{"url": "https://paypal.me/interventionio", "type": "custom"}, {"url": "https://github.com/Intervention", "type": "github"}], "abandoned": true, "time": "2023-02-25T19:40:47+00:00"}, {"name": "jakub-onderka/php-console-color", "version": "v0.2", "source": {"type": "git", "url": "https://github.com/JakubOnderka/PHP-Console-Color.git", "reference": "d5deaecff52a0d61ccb613bb3804088da0307191"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JakubOnderka/PHP-Console-Color/zipball/d5deaecff52a0d61ccb613bb3804088da0307191", "reference": "d5deaecff52a0d61ccb613bb3804088da0307191", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"jakub-onderka/php-code-style": "1.0", "jakub-onderka/php-parallel-lint": "1.0", "jakub-onderka/php-var-dump-check": "0.*", "phpunit/phpunit": "~4.3", "squizlabs/php_codesniffer": "1.*"}, "type": "library", "autoload": {"psr-4": {"JakubOnderka\\PhpConsoleColor\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/JakubOnderka/PHP-Console-Color/issues", "source": "https://github.com/JakubOnderka/PHP-Console-Color/tree/master"}, "abandoned": "php-parallel-lint/php-console-color", "time": "2018-09-29T17:23:10+00:00"}, {"name": "jakub-onderka/php-console-highlighter", "version": "v0.4", "source": {"type": "git", "url": "https://github.com/JakubOnderka/PHP-Console-Highlighter.git", "reference": "9f7a229a69d52506914b4bc61bfdb199d90c5547"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JakubOnderka/PHP-Console-Highlighter/zipball/9f7a229a69d52506914b4bc61bfdb199d90c5547", "reference": "9f7a229a69d52506914b4bc61bfdb199d90c5547", "shasum": ""}, "require": {"ext-tokenizer": "*", "jakub-onderka/php-console-color": "~0.2", "php": ">=5.4.0"}, "require-dev": {"jakub-onderka/php-code-style": "~1.0", "jakub-onderka/php-parallel-lint": "~1.0", "jakub-onderka/php-var-dump-check": "~0.1", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~1.5"}, "type": "library", "autoload": {"psr-4": {"JakubOnderka\\PhpConsoleHighlighter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.acci.cz/"}], "description": "Highlight PHP code in terminal", "support": {"issues": "https://github.com/JakubOnderka/PHP-Console-Highlighter/issues", "source": "https://github.com/JakubOnderka/PHP-Console-Highlighter/tree/master"}, "abandoned": "php-parallel-lint/php-console-highlighter", "time": "2018-09-29T18:48:56+00:00"}, {"name": "jdavidbakr/laravel-cache-garbage-collector", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/jdavidbakr/laravel-cache-garbage-collector.git", "reference": "6e344e405bebcfb918315a7e1a074a3d0d846cb1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jdavidbakr/laravel-cache-garbage-collector/zipball/6e344e405bebcfb918315a7e1a074a3d0d846cb1", "reference": "6e344e405bebcfb918315a7e1a074a3d0d846cb1", "shasum": ""}, "require": {"illuminate/support": "~5.1", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "4.*", "scrutinizer/ocular": "~1.1", "squizlabs/php_codesniffer": "~2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"jdavidbakr\\LaravelCacheGarbageCollector\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.jdavidbaker.com", "role": "Developer"}], "description": "A script that will clean up expired cache files if the system is using the files cache system", "homepage": "https://github.com/jdavidbakr/LaravelCacheGarbageCollector", "keywords": ["LaravelCacheGarbageCollector", "jdavidbakr"], "support": {"issues": "https://github.com/jdavidbakr/laravel-cache-garbage-collector/issues", "source": "https://github.com/jdavidbakr/laravel-cache-garbage-collector/tree/master"}, "time": "2016-06-29T14:18:15+00:00"}, {"name": "jean85/pretty-package-versions", "version": "2.0.6", "source": {"type": "git", "url": "https://github.com/Jean85/pretty-package-versions.git", "reference": "f9fdd29ad8e6d024f52678b570e5593759b550b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Jean85/pretty-package-versions/zipball/f9fdd29ad8e6d024f52678b570e5593759b550b4", "reference": "f9fdd29ad8e6d024f52678b570e5593759b550b4", "shasum": ""}, "require": {"composer-runtime-api": "^2.0.0", "php": "^7.1|^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "jean85/composer-provided-replaced-stub-package": "^1.0", "phpstan/phpstan": "^1.4", "phpunit/phpunit": "^7.5|^8.5|^9.4", "vimeo/psalm": "^4.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Jean85\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to get pretty versions strings of installed dependencies", "keywords": ["composer", "package", "release", "versions"], "support": {"issues": "https://github.com/Jean85/pretty-package-versions/issues", "source": "https://github.com/Jean85/pretty-package-versions/tree/2.0.6"}, "time": "2024-03-08T09:58:59+00:00"}, {"name": "laravel/framework", "version": "v5.8.38", "source": {"type": "git", "url": "https://github.com/laravel/framework.git", "reference": "78eb4dabcc03e189620c16f436358d41d31ae11f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/framework/zipball/78eb4dabcc03e189620c16f436358d41d31ae11f", "reference": "78eb4dabcc03e189620c16f436358d41d31ae11f", "shasum": ""}, "require": {"doctrine/inflector": "^1.1", "dragonmantank/cron-expression": "^2.0", "egulias/email-validator": "^2.0", "erusev/parsedown": "^1.7", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "league/flysystem": "^1.0.8", "monolog/monolog": "^1.12", "nesbot/carbon": "^1.26.3 || ^2.0", "opis/closure": "^3.1", "php": "^7.1.3", "psr/container": "^1.0", "psr/simple-cache": "^1.0", "ramsey/uuid": "^3.7", "swiftmailer/swiftmailer": "^6.0", "symfony/console": "^4.2", "symfony/debug": "^4.2", "symfony/finder": "^4.2", "symfony/http-foundation": "^4.2", "symfony/http-kernel": "^4.2", "symfony/process": "^4.2", "symfony/routing": "^4.2", "symfony/var-dumper": "^4.2", "tijsverkoyen/css-to-inline-styles": "^2.2.1", "vlucas/phpdotenv": "^3.3"}, "conflict": {"tightenco/collect": "<5.5.33"}, "replace": {"illuminate/auth": "self.version", "illuminate/broadcasting": "self.version", "illuminate/bus": "self.version", "illuminate/cache": "self.version", "illuminate/config": "self.version", "illuminate/console": "self.version", "illuminate/container": "self.version", "illuminate/contracts": "self.version", "illuminate/cookie": "self.version", "illuminate/database": "self.version", "illuminate/encryption": "self.version", "illuminate/events": "self.version", "illuminate/filesystem": "self.version", "illuminate/hashing": "self.version", "illuminate/http": "self.version", "illuminate/log": "self.version", "illuminate/mail": "self.version", "illuminate/notifications": "self.version", "illuminate/pagination": "self.version", "illuminate/pipeline": "self.version", "illuminate/queue": "self.version", "illuminate/redis": "self.version", "illuminate/routing": "self.version", "illuminate/session": "self.version", "illuminate/support": "self.version", "illuminate/translation": "self.version", "illuminate/validation": "self.version", "illuminate/view": "self.version"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/dbal": "^2.6", "filp/whoops": "^2.1.4", "guzzlehttp/guzzle": "^6.3", "league/flysystem-cached-adapter": "^1.0", "mockery/mockery": "^1.0", "moontoast/math": "^1.1", "orchestra/testbench-core": "3.8.*", "pda/pheanstalk": "^4.0", "phpunit/phpunit": "^7.5|^8.0", "predis/predis": "^1.1.1", "symfony/css-selector": "^4.2", "symfony/dom-crawler": "^4.2", "true/punycode": "^2.1"}, "suggest": {"aws/aws-sdk-php": "Required to use the SQS queue driver and SES mail driver (^3.0).", "doctrine/dbal": "Required to rename columns and drop SQLite columns (^2.6).", "ext-gd": "Required to use Illuminate\\Http\\Testing\\FileFactory::image().", "ext-pcntl": "Required to use all features of the queue worker.", "ext-posix": "Required to use all features of the queue worker.", "filp/whoops": "Required for friendly error pages in development (^2.1.4).", "fzaninotto/faker": "Required to use the eloquent factory builder (^1.4).", "guzzlehttp/guzzle": "Required to use the Mailgun and Mandrill mail drivers and the ping methods on schedules (^6.0).", "laravel/tinker": "Required to use the tinker console command (^1.0).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^1.0).", "league/flysystem-cached-adapter": "Required to use the Flysystem cache (^1.0).", "league/flysystem-rackspace": "Required to use the Flysystem Rackspace driver (^1.0).", "league/flysystem-sftp": "Required to use the Flysystem SFTP driver (^1.0).", "moontoast/math": "Required to use ordered UUIDs (^1.1).", "nexmo/client": "Required to use the Nexmo transport (^1.0).", "pda/pheanstalk": "Required to use the beanstalk queue driver (^4.0).", "predis/predis": "Required to use the redis cache and queue drivers (^1.0).", "pusher/pusher-php-server": "Required to use the <PERSON><PERSON><PERSON> broadcast driver (^3.0).", "symfony/css-selector": "Required to use some of the crawler integration testing tools (^4.2).", "symfony/dom-crawler": "Required to use most of the crawler integration testing tools (^4.2).", "symfony/psr-http-message-bridge": "Required to use PSR-7 bridging features (^1.1).", "wildbit/swiftmailer-postmark": "Required to use Postmark mail driver (^3.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.8-dev"}}, "autoload": {"files": ["src/Illuminate/Foundation/helpers.php", "src/Illuminate/Support/helpers.php"], "psr-4": {"Illuminate\\": "src/Illuminate/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Laravel Framework.", "homepage": "https://laravel.com", "keywords": ["framework", "laravel"], "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2020-04-14T14:14:36+00:00"}, {"name": "laravel/tinker", "version": "v1.0.10", "source": {"type": "git", "url": "https://github.com/laravel/tinker.git", "reference": "ad571aacbac1539c30d480908f9d0c9614eaf1a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/tinker/zipball/ad571aacbac1539c30d480908f9d0c9614eaf1a7", "reference": "ad571aacbac1539c30d480908f9d0c9614eaf1a7", "shasum": ""}, "require": {"illuminate/console": "~5.1|^6.0", "illuminate/contracts": "~5.1|^6.0", "illuminate/support": "~5.1|^6.0", "php": ">=5.5.9", "psy/psysh": "0.7.*|0.8.*|0.9.*", "symfony/var-dumper": "~3.0|~4.0"}, "require-dev": {"phpunit/phpunit": "~4.0|~5.0"}, "suggest": {"illuminate/database": "The Illuminate Database package (~5.1)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}, "laravel": {"providers": ["Laravel\\Tinker\\TinkerServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Tinker\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful REPL for the Laravel framework.", "keywords": ["REPL", "Tinker", "laravel", "psysh"], "support": {"issues": "https://github.com/laravel/tinker/issues", "source": "https://github.com/laravel/tinker/tree/v1.0.10"}, "time": "2019-08-07T15:10:45+00:00"}, {"name": "league/flysystem", "version": "1.1.10", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3239285c825c152bcc315fe0e87d6b55f5972ed1", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/mime-type-detection": "^1.3", "php": "^7.2.5 || ^8.0"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/prophecy": "^1.11.1", "phpunit/phpunit": "^8.5.8"}, "suggest": {"ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.10"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "other"}], "time": "2022-10-04T09:16:37+00:00"}, {"name": "league/mime-type-detection", "version": "1.15.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301", "reference": "ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3 || ^10.0"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.15.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2024-01-28T23:22:08+00:00"}, {"name": "monolog/monolog", "version": "1.27.1", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/904713c5929655dc9b97288b69cfeedad610c9a1", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "~4.5", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.27.1"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2022-06-09T08:53:42+00:00"}, {"name": "moontoast/math", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/ramsey/moontoast-math.git", "reference": "5f47d34c87767dbcc08b30377a9827df71de91fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/moontoast-math/zipball/5f47d34c87767dbcc08b30377a9827df71de91fa", "reference": "5f47d34c87767dbcc08b30377a9827df71de91fa", "shasum": ""}, "require": {"php": ">=5.3.3", "phpseclib/bcmath_compat": ">=1.0.3"}, "require-dev": {"jakub-onderka/php-parallel-lint": "^0.9.0", "phpunit/phpunit": "^4.8 || ^5.5 || ^6.5 || ^7.0", "satooshi/php-coveralls": "^0.6.1", "squizlabs/php_codesniffer": "^2.3"}, "type": "library", "autoload": {"psr-4": {"Moontoast\\Math\\": "src/Moontoast/Math", "Moontoast\\Math\\Exception\\": "src/Moontoast/Math/Exception"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A mathematics library, providing functionality for large numbers", "homepage": "https://github.com/ramsey/moontoast-math", "keywords": ["bcmath", "math"], "support": {"issues": "https://github.com/ramsey/moontoast-math/issues", "source": "https://github.com/ramsey/moontoast-math"}, "abandoned": "brick/math", "time": "2020-01-05T04:49:34+00:00"}, {"name": "nesbot/carbon", "version": "2.72.5", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "afd46589c216118ecd48ff2b95d77596af1e57ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/afd46589c216118ecd48ff2b95d77596af1e57ed", "reference": "afd46589c216118ecd48ff2b95d77596af1e57ed", "shasum": ""}, "require": {"carbonphp/carbon-doctrine-types": "*", "ext-json": "*", "php": "^7.1.8 || ^8.0", "psr/clock": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4 || ^4.0", "doctrine/orm": "^2.7 || ^3.0", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "*", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev", "dev-2.x": "2.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2024-06-03T19:18:41+00:00"}, {"name": "nikic/php-parser", "version": "v4.19.1", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "4e1b88d21c69391150ace211e9eaf05810858d0b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/4e1b88d21c69391150ace211e9eaf05810858d0b", "reference": "4e1b88d21c69391150ace211e9eaf05810858d0b", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.19.1"}, "time": "2024-03-17T08:10:35+00:00"}, {"name": "nyholm/psr7", "version": "1.8.1", "source": {"type": "git", "url": "https://github.com/Nyholm/psr7.git", "reference": "aa5fc277a4f5508013d571341ade0c3886d4d00e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/psr7/zipball/aa5fc277a4f5508013d571341ade0c3886d4d00e", "reference": "aa5fc277a4f5508013d571341ade0c3886d4d00e", "shasum": ""}, "require": {"php": ">=7.2", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0", "psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "php-http/message-factory": "^1.0", "php-http/psr7-integration-tests": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.4", "symfony/error-handler": "^4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "autoload": {"psr-4": {"Nyholm\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "marti<PERSON>@vanderven.se"}], "description": "A fast PHP7 implementation of PSR-7", "homepage": "https://tnyholm.se", "keywords": ["psr-17", "psr-7"], "support": {"issues": "https://github.com/Nyholm/psr7/issues", "source": "https://github.com/Nyholm/psr7/tree/1.8.1"}, "funding": [{"url": "https://github.com/Zegnat", "type": "github"}, {"url": "https://github.com/nyholm", "type": "github"}], "time": "2023-11-13T09:31:12+00:00"}, {"name": "opis/closure", "version": "3.6.3", "source": {"type": "git", "url": "https://github.com/opis/closure.git", "reference": "3d81e4309d2a927abbe66df935f4bb60082805ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/closure/zipball/3d81e4309d2a927abbe66df935f4bb60082805ad", "reference": "3d81e4309d2a927abbe66df935f4bb60082805ad", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0"}, "require-dev": {"jeremeamia/superclosure": "^2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.6.x-dev"}}, "autoload": {"files": ["functions.php"], "psr-4": {"Opis\\Closure\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "A library that can be used to serialize closures (anonymous functions) and arbitrary objects.", "homepage": "https://opis.io/closure", "keywords": ["anonymous functions", "closure", "function", "serializable", "serialization", "serialize"], "support": {"issues": "https://github.com/opis/closure/issues", "source": "https://github.com/opis/closure/tree/3.6.3"}, "time": "2022-01-27T09:35:39+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v2.7.0", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "52a0d99e69f56b9ec27ace92ba56897fe6993105"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/52a0d99e69f56b9ec27ace92ba56897fe6993105", "reference": "52a0d99e69f56b9ec27ace92ba56897fe6993105", "shasum": ""}, "require": {"php": "^7|^8"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "vimeo/psalm": "^1|^2|^3|^4"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "time": "2024-05-08T12:18:48+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "patchwork/utf8", "version": "v1.3.3", "source": {"type": "git", "url": "https://github.com/tchwork/utf8.git", "reference": "e1fa4d4a57896d074c9a8d01742b688d5db4e9d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tchwork/utf8/zipball/e1fa4d4a57896d074c9a8d01742b688d5db4e9d5", "reference": "e1fa4d4a57896d074c9a8d01742b688d5db4e9d5", "shasum": ""}, "require": {"lib-pcre": ">=7.3", "php": ">=5.3.0"}, "require-dev": {"symfony/phpunit-bridge": "^3.4|^4.4"}, "suggest": {"ext-iconv": "Use iconv for best performance", "ext-intl": "Use Intl for best performance", "ext-mbstring": "Use Mbstring for best performance", "ext-wfio": "Use WFIO for UTF-8 filesystem access on Windows"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"psr-4": {"Patchwork\\": "src/Patchwork/"}, "classmap": ["src/Normalizer.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["(Apache-2.0 or GPL-2.0)"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Portable and performant UTF-8, Unicode and Grapheme Clusters for PHP", "homepage": "https://github.com/tchwork/utf8", "keywords": ["grapheme", "i18n", "unicode", "utf-8", "utf8"], "support": {"issues": "https://github.com/tchwork/utf8/issues", "source": "https://github.com/tchwork/utf8/tree/v1.3.3"}, "funding": [{"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/patchwork/utf8", "type": "tidelift"}], "abandoned": "symfony/polyfill-mbstring or symfony/string", "time": "2021-01-07T16:38:58+00:00"}, {"name": "phenx/php-font-lib", "version": "0.5.6", "source": {"type": "git", "url": "https://github.com/dompdf/php-font-lib.git", "reference": "a1681e9793040740a405ac5b189275059e2a9863"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/php-font-lib/zipball/a1681e9793040740a405ac5b189275059e2a9863", "reference": "a1681e9793040740a405ac5b189275059e2a9863", "shasum": ""}, "require": {"ext-mbstring": "*"}, "require-dev": {"symfony/phpunit-bridge": "^3 || ^4 || ^5 || ^6"}, "type": "library", "autoload": {"psr-4": {"FontLib\\": "src/FontLib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse, export and make subsets of different types of font files.", "homepage": "https://github.com/PhenX/php-font-lib", "support": {"issues": "https://github.com/dompdf/php-font-lib/issues", "source": "https://github.com/dompdf/php-font-lib/tree/0.5.6"}, "time": "2024-01-29T14:45:26+00:00"}, {"name": "phenx/php-svg-lib", "version": "0.4.1", "source": {"type": "git", "url": "https://github.com/dompdf/php-svg-lib.git", "reference": "4498b5df7b08e8469f0f8279651ea5de9626ed02"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/php-svg-lib/zipball/4498b5df7b08e8469f0f8279651ea5de9626ed02", "reference": "4498b5df7b08e8469f0f8279651ea5de9626ed02", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.1 || ^7.2 || ^7.3 || ^7.4 || ^8.0", "sabberworm/php-css-parser": "^8.4"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.5 || ^9.5"}, "type": "library", "autoload": {"psr-4": {"Svg\\": "src/Svg"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse and export to PDF SVG files.", "homepage": "https://github.com/PhenX/php-svg-lib", "support": {"issues": "https://github.com/dompdf/php-svg-lib/issues", "source": "https://github.com/dompdf/php-svg-lib/tree/0.4.1"}, "time": "2022-03-07T12:52:04+00:00"}, {"name": "php-http/client-common", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/php-http/client-common.git", "reference": "1e19c059b0e4d5f717bf5d524d616165aeab0612"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/client-common/zipball/1e19c059b0e4d5f717bf5d524d616165aeab0612", "reference": "1e19c059b0e4d5f717bf5d524d616165aeab0612", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/httplug": "^2.0", "php-http/message": "^1.6", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0 || ^2.0", "symfony/options-resolver": "~4.0.15 || ~4.1.9 || ^4.2.1 || ^5.0 || ^6.0 || ^7.0", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"doctrine/instantiator": "^1.1", "guzzlehttp/psr7": "^1.4", "nyholm/psr7": "^1.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "phpspec/prophecy": "^1.10.2", "phpunit/phpunit": "^7.5.20 || ^8.5.33 || ^9.6.7"}, "suggest": {"ext-json": "To detect JSON responses with the ContentTypePlugin", "ext-libxml": "To detect XML responses with the ContentTypePlugin", "php-http/cache-plugin": "PSR-6 Cache plugin", "php-http/logger-plugin": "PSR-3 Logger plugin", "php-http/stopwatch-plugin": "Symfony Stopwatch plugin"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\Common\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common HTTP Client implementations and tools for HTTPlug", "homepage": "http://httplug.io", "keywords": ["client", "common", "http", "httplug"], "support": {"issues": "https://github.com/php-http/client-common/issues", "source": "https://github.com/php-http/client-common/tree/2.7.1"}, "time": "2023-11-30T10:31:25+00:00"}, {"name": "php-http/discovery", "version": "1.19.4", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "0700efda8d7526335132360167315fdab3aeb599"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/0700efda8d7526335132360167315fdab3aeb599", "reference": "0700efda8d7526335132360167315fdab3aeb599", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "sebastian/comparator": "^3.0.5 || ^4.0.8", "symfony/phpunit-bridge": "^6.4.4 || ^7.0.1"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.19.4"}, "time": "2024-03-29T13:00:05+00:00"}, {"name": "php-http/httplug", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "625ad742c360c8ac580fcc647a1541d29e257f67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/625ad742c360c8ac580fcc647a1541d29e257f67", "reference": "625ad742c360c8ac580fcc647a1541d29e257f67", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/promise": "^1.1", "psr/http-client": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.1 || ^5.0 || ^6.0", "phpspec/phpspec": "^5.1 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "support": {"issues": "https://github.com/php-http/httplug/issues", "source": "https://github.com/php-http/httplug/tree/2.4.0"}, "time": "2023-04-14T15:10:03+00:00"}, {"name": "php-http/message", "version": "1.16.1", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "5997f3289332c699fa2545c427826272498a2088"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/5997f3289332c699fa2545c427826272498a2088", "reference": "5997f3289332c699fa2545c427826272498a2088", "shasum": ""}, "require": {"clue/stream-filter": "^1.5", "php": "^7.2 || ^8.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.6", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0 || ^2.0", "laminas/laminas-diactoros": "^2.0 || ^3.0", "php-http/message-factory": "^1.0.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "slim/slim": "^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "type": "library", "autoload": {"files": ["src/filters.php"], "psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "support": {"issues": "https://github.com/php-http/message/issues", "source": "https://github.com/php-http/message/tree/1.16.1"}, "time": "2024-03-07T13:22:09+00:00"}, {"name": "php-http/message-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "reference": "4d8778e1c7d405cbb471574821c1ff5b68cc8f57", "shasum": ""}, "require": {"php": ">=5.4", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "support": {"issues": "https://github.com/php-http/message-factory/issues", "source": "https://github.com/php-http/message-factory/tree/1.1.0"}, "abandoned": "psr/http-factory", "time": "2023-04-14T14:16:17+00:00"}, {"name": "php-http/promise", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2 || ^6.3", "phpspec/phpspec": "^5.1.2 || ^6.2 || ^7.4"}, "type": "library", "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "support": {"issues": "https://github.com/php-http/promise/issues", "source": "https://github.com/php-http/promise/tree/1.3.1"}, "time": "2024-03-15T13:55:21+00:00"}, {"name": "phpoption/phpoption", "version": "1.9.2", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "80735db690fe4fc5c76dfa7f9b770634285fa820"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/80735db690fe4fc5c76dfa7f9b770634285fa820", "reference": "80735db690fe4fc5c76dfa7f9b770634285fa820", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true}, "branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2023-11-12T21:59:55+00:00"}, {"name": "phpseclib/bcmath_compat", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/phpseclib/bcmath_compat.git", "reference": "29bbf07a7039ff65ce7daa44502ba34baf1512ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/bcmath_compat/zipball/29bbf07a7039ff65ce7daa44502ba34baf1512ec", "reference": "29bbf07a7039ff65ce7daa44502ba34baf1512ec", "shasum": ""}, "require": {"phpseclib/phpseclib": "^3.0"}, "provide": {"ext-bcmath": "8.1.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.7|^6.0|^9.4", "squizlabs/php_codesniffer": "^3.0"}, "suggest": {"ext-gmp": "Will enable faster math operations"}, "type": "library", "autoload": {"files": ["lib/bcmath.php"], "psr-4": {"bcmath_compat\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://phpseclib.sourceforge.net"}], "description": "PHP 5.x-8.x polyfill for bcmath extension", "keywords": ["BigInteger", "bcmath", "bigdecimal", "math", "polyfill"], "support": {"email": "<EMAIL>", "issues": "https://github.com/phpseclib/bcmath_compat/issues", "source": "https://github.com/phpseclib/bcmath_compat"}, "time": "2024-02-21T10:30:36+00:00"}, {"name": "phpseclib/mcrypt_compat", "version": "2.0.6", "source": {"type": "git", "url": "https://github.com/phpseclib/mcrypt_compat.git", "reference": "e5924504997b4f90772034cefd89dc2f4ec189dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/mcrypt_compat/zipball/e5924504997b4f90772034cefd89dc2f4ec189dc", "reference": "e5924504997b4f90772034cefd89dc2f4ec189dc", "shasum": ""}, "require": {"php": ">=5.6.1", "phpseclib/phpseclib": ">=3.0.36 <4.0.0"}, "provide": {"ext-mcrypt": "5.6.40"}, "require-dev": {"phpunit/phpunit": "^5.7|^6.0|^9.4"}, "suggest": {"ext-openssl": "Will enable faster cryptographic operations"}, "type": "library", "autoload": {"files": ["lib/mcrypt.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://phpseclib.sourceforge.net"}], "description": "PHP 5.x-8.x polyfill for mcrypt extension", "keywords": ["cryptograpy", "encryption", "mcrypt", "polyfill"], "support": {"email": "<EMAIL>", "issues": "https://github.com/phpseclib/mcrypt_compat/issues", "source": "https://github.com/phpseclib/mcrypt_compat"}, "funding": [{"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/mcrypt_compat", "type": "tidelift"}], "time": "2024-02-26T14:52:18+00:00"}, {"name": "phpseclib/phpseclib", "version": "3.0.39", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "211ebc399c6e73c225a018435fe5ae209d1d1485"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/211ebc399c6e73c225a018435fe5ae209d1d1485", "reference": "211ebc399c6e73c225a018435fe5ae209d1d1485", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2|^3", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": ">=5.6.1"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-dom": "Install the DOM extension to load XML formatted public keys.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib3\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.39"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2024-06-24T06:27:33+00:00"}, {"name": "pragmarx/version", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/antonioribeiro/version.git", "reference": "143b1389da0097fbb543f3f5733b93ab78474ebb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/antonioribeiro/version/zipball/143b1389da0097fbb543f3f5733b93ab78474ebb", "reference": "143b1389da0097fbb543f3f5733b93ab78474ebb", "shasum": ""}, "require": {"laravel/framework": ">=5.5.33", "php": ">=7.0", "pragmarx/yaml": "^1.0", "symfony/process": "^3.3|^4.0|^5.0|^6.0"}, "require-dev": {"orchestra/testbench": "3.4.*|3.5.*|3.6.*|3.7.*|4.*|5.*|6.*", "phpunit/phpunit": "~5|~6|~7|~8|~9"}, "type": "library", "extra": {"component": "package", "laravel": {"providers": ["PragmaRX\\Version\\Package\\ServiceProvider"], "aliases": {"Version": "PragmaRX\\Version\\Package\\Facade"}}}, "autoload": {"psr-4": {"PragmaRX\\Version\\Tests\\": "tests/", "PragmaRX\\Version\\Package\\": "src/package"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator & Designer"}], "description": "Take control over your Laravel app version", "keywords": ["laravel", "version", "versioning"], "support": {"issues": "https://github.com/antonioribeiro/version/issues", "source": "https://github.com/antonioribeiro/version/tree/v1.3.1"}, "time": "2022-11-16T09:34:26+00:00"}, {"name": "pragmarx/yaml", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/antonioribeiro/yaml.git", "reference": "56a12f5310807a9defbd9d63aaebb53957fc34b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/antonioribeiro/yaml/zipball/56a12f5310807a9defbd9d63aaebb53957fc34b0", "reference": "56a12f5310807a9defbd9d63aaebb53957fc34b0", "shasum": ""}, "require": {"illuminate/support": ">=5.5.33", "php": ">=7.0", "symfony/yaml": "^3.4|^4.0|^5.0|^6.0"}, "require-dev": {"orchestra/testbench": "3.5|^3.6|^4.0|^5.0", "phpunit/phpunit": "^4.0|^6.4|^7.0|^8.0|^9.0"}, "suggest": {"ext-yaml": "Required to use the PECL YAML."}, "type": "library", "extra": {"component": "package", "laravel": {"providers": ["PragmaRX\\Yaml\\Package\\ServiceProvider"], "aliases": {"Yaml": "PragmaRX\\Yaml\\Package\\Facade"}}}, "autoload": {"psr-4": {"PragmaRX\\Yaml\\Tests\\": "tests/", "PragmaRX\\Yaml\\Package\\": "src/package"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator & Designer"}], "description": "Load your Laravel config files using yaml", "keywords": ["config", "laravel", "yaml"], "support": {"issues": "https://github.com/antonioribeiro/yaml/issues", "source": "https://github.com/antonioribeiro/yaml/tree/v1.3.0"}, "time": "2022-06-13T21:27:41+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "psy/psysh", "version": "v0.9.12", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "90da7f37568aee36b116a030c5f99c915267edd4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/90da7f37568aee36b116a030c5f99c915267edd4", "reference": "90da7f37568aee36b116a030c5f99c915267edd4", "shasum": ""}, "require": {"dnoegel/php-xdg-base-dir": "0.1.*", "ext-json": "*", "ext-tokenizer": "*", "jakub-onderka/php-console-highlighter": "0.3.*|0.4.*", "nikic/php-parser": "~1.3|~2.0|~3.0|~4.0", "php": ">=5.4.0", "symfony/console": "~2.3.10|^2.4.2|~3.0|~4.0|~5.0", "symfony/var-dumper": "~2.7|~3.0|~4.0|~5.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2", "hoa/console": "~2.15|~3.16", "phpunit/phpunit": "~4.8.35|~5.0|~6.0|~7.0"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history.", "hoa/console": "A pure PHP readline implementation. You'll want this if your PHP install doesn't already support readline or libedit."}, "bin": ["bin/psysh"], "type": "library", "extra": {"branch-alias": {"dev-develop": "0.9.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.9.12"}, "time": "2019-12-06T14:19:43+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/uuid", "version": "3.9.7", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "dc75aa439eb4c1b77f5379fd958b3dc0e6014178"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/dc75aa439eb4c1b77f5379fd958b3dc0e6014178", "reference": "dc75aa439eb4c1b77f5379fd958b3dc0e6014178", "shasum": ""}, "require": {"ext-json": "*", "paragonie/random_compat": "^1 | ^2 | ^9.99.99", "php": "^5.4 | ^7.0 | ^8.0", "symfony/polyfill-ctype": "^1.8"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"codeception/aspect-mock": "^1 | ^2", "doctrine/annotations": "^1.2", "goaop/framework": "1.0.0-alpha.2 | ^1 | >=2.1.0 <=2.3.2", "mockery/mockery": "^0.9.11 | ^1", "moontoast/math": "^1.1", "nikic/php-parser": "<=4.5.0", "paragonie/random-lib": "^2", "php-mock/php-mock-phpunit": "^0.3 | ^1.1 | ^2.6", "php-parallel-lint/php-parallel-lint": "^1.3", "phpunit/phpunit": ">=4.8.36 <9.0.0 | >=9.3.0", "squizlabs/php_codesniffer": "^3.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-ctype": "Provides support for PHP Ctype functions", "ext-libsodium": "Provides the PECL libsodium extension for use with the SodiumRandomGenerator", "ext-openssl": "Provides the OpenSSL extension for use with the OpenSslGenerator", "ext-uuid": "Provides the PECL UUID extension for use with the PeclUuidTimeGenerator and PeclUuidRandomGenerator", "moontoast/math": "Provides support for converting UUID to 128-bit integer (in string form).", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-console": "A console application for generating UUIDs with ramsey/uuid", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Formerly rhumsaa/uuid. A PHP 5.4+ library for generating RFC 4122 version 1, 3, 4, and 5 universally unique identifiers (UUID).", "homepage": "https://github.com/ramsey/uuid", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "rss": "https://github.com/ramsey/uuid/releases.atom", "source": "https://github.com/ramsey/uuid", "wiki": "https://github.com/ramsey/uuid/wiki"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/uuid", "type": "tidelift"}], "time": "2022-12-19T21:55:10+00:00"}, {"name": "rap2hpoutre/laravel-log-viewer", "version": "v1.7.0", "source": {"type": "git", "url": "https://github.com/rap2hpoutre/laravel-log-viewer.git", "reference": "27392d29234b6ff38a456454558f4bcc40cc837a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rap2hpoutre/laravel-log-viewer/zipball/27392d29234b6ff38a456454558f4bcc40cc837a", "reference": "27392d29234b6ff38a456454558f4bcc40cc837a", "shasum": ""}, "require": {"illuminate/support": "4.2.*|5.*|^6.0|^7.0|^8.0", "php": ">=5.4.0"}, "require-dev": {"orchestra/testbench": "3.7.*", "phpunit/phpunit": "^7"}, "type": "laravel-package", "extra": {"laravel": {"providers": ["Rap2hpoutre\\LaravelLogViewer\\LaravelLogViewerServiceProvider"]}}, "autoload": {"psr-0": {"Rap2hpoutre\\LaravelLogViewer\\": "src/"}, "classmap": ["src/controllers"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "rap2hpoutre", "email": "rap<PERSON><PERSON><PERSON>@gmail.com"}], "description": "A Laravel log reader", "keywords": ["laravel", "log", "log-reader", "log-viewer", "logging", "lumen"], "support": {"issues": "https://github.com/rap2hpoutre/laravel-log-viewer/issues", "source": "https://github.com/rap2hpoutre/laravel-log-viewer/tree/master"}, "time": "2020-09-08T12:21:27+00:00"}, {"name": "sabberworm/php-css-parser", "version": "v8.6.0", "source": {"type": "git", "url": "https://github.com/MyIntervals/PHP-CSS-Parser.git", "reference": "d2fb94a9641be84d79c7548c6d39bbebba6e9a70"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MyIntervals/PHP-CSS-Parser/zipball/d2fb94a9641be84d79c7548c6d39bbebba6e9a70", "reference": "d2fb94a9641be84d79c7548c6d39bbebba6e9a70", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=5.6.20"}, "require-dev": {"phpunit/phpunit": "^5.7.27"}, "suggest": {"ext-mbstring": "for parsing UTF-8 CSS"}, "type": "library", "extra": {"branch-alias": {"dev-main": "9.0.x-dev"}}, "autoload": {"psr-4": {"Sabberworm\\CSS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Parser for CSS Files written in PHP", "homepage": "https://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"], "support": {"issues": "https://github.com/MyIntervals/PHP-CSS-Parser/issues", "source": "https://github.com/MyIntervals/PHP-CSS-Parser/tree/v8.6.0"}, "time": "2024-07-01T07:33:21+00:00"}, {"name": "sentry/sdk", "version": "3.6.0", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php-sdk.git", "reference": "24c235ff2027401cbea099bf88689e1a1f197c7a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php-sdk/zipball/24c235ff2027401cbea099bf88689e1a1f197c7a", "reference": "24c235ff2027401cbea099bf88689e1a1f197c7a", "shasum": ""}, "require": {"http-interop/http-factory-guzzle": "^1.0", "sentry/sentry": "^3.22", "symfony/http-client": "^4.3|^5.0|^6.0|^7.0"}, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "This is a metapackage shipping sentry/sentry with a recommended HTTP client.", "homepage": "http://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "log", "logging", "sentry"], "support": {"issues": "https://github.com/getsentry/sentry-php-sdk/issues", "source": "https://github.com/getsentry/sentry-php-sdk/tree/3.6.0"}, "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2023-12-04T10:49:33+00:00"}, {"name": "sentry/sentry", "version": "3.22.1", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php.git", "reference": "8859631ba5ab15bc1af420b0eeed19ecc6c9d81d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php/zipball/8859631ba5ab15bc1af420b0eeed19ecc6c9d81d", "reference": "8859631ba5ab15bc1af420b0eeed19ecc6c9d81d", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "guzzlehttp/promises": "^1.5.3|^2.0", "jean85/pretty-package-versions": "^1.5|^2.0.4", "php": "^7.2|^8.0", "php-http/async-client-implementation": "^1.0", "php-http/client-common": "^1.5|^2.0", "php-http/discovery": "^1.15", "php-http/httplug": "^1.1|^2.0", "php-http/message": "^1.5", "php-http/message-factory": "^1.1", "psr/http-factory": "^1.0", "psr/http-factory-implementation": "^1.0", "psr/log": "^1.0|^2.0|^3.0", "symfony/options-resolver": "^3.4.43|^4.4.30|^5.0.11|^6.0|^7.0", "symfony/polyfill-php80": "^1.17"}, "conflict": {"php-http/client-common": "1.8.0", "raven/raven": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.19|3.4.*", "guzzlehttp/psr7": "^1.8.4|^2.1.1", "http-interop/http-factory-guzzle": "^1.0", "monolog/monolog": "^1.6|^2.0|^3.0", "nikic/php-parser": "^4.10.3", "php-http/mock-client": "^1.3", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.3", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^8.5.14|^9.4", "symfony/phpunit-bridge": "^5.2|^6.0", "vimeo/psalm": "^4.17"}, "suggest": {"monolog/monolog": "Allow sending log messages to Sentry by using the included Monolog handler."}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Sentry\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "A PHP SDK for Sentry (http://sentry.io)", "homepage": "http://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "log", "logging", "sentry"], "support": {"issues": "https://github.com/getsentry/sentry-php/issues", "source": "https://github.com/getsentry/sentry-php/tree/3.22.1"}, "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2023-11-13T11:47:28+00:00"}, {"name": "sentry/sentry-laravel", "version": "2.14.2", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-laravel.git", "reference": "4538ed31d77868dd3b6d72ad6e5e68b572beeb9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-laravel/zipball/4538ed31d77868dd3b6d72ad6e5e68b572beeb9f", "reference": "4538ed31d77868dd3b6d72ad6e5e68b572beeb9f", "shasum": ""}, "require": {"illuminate/support": "5.0 - 5.8 | ^6.0 | ^7.0 | ^8.0 | ^9.0", "nyholm/psr7": "^1.0", "php": "^7.2 | ^8.0", "sentry/sdk": "^3.1", "sentry/sentry": "^3.3", "symfony/psr-http-message-bridge": "^1.0 | ^2.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.11", "laravel/framework": "5.0 - 5.8 | ^6.0 | ^7.0 | ^8.0 | ^9.0", "mockery/mockery": "^1.3", "orchestra/testbench": "3.1 - 3.8 | ^4.7 | ^5.1 | ^6.0 | ^7.0", "phpunit/phpunit": "^5.7 | ^6.5 | ^7.5 | ^8.4 | ^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev", "dev-0.x": "0.x-dev"}, "laravel": {"providers": ["Sentry\\Laravel\\ServiceProvider", "Sentry\\Laravel\\Tracing\\ServiceProvider"], "aliases": {"Sentry": "Sentry\\Laravel\\Facade"}}}, "autoload": {"psr-0": {"Sentry\\Laravel\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "<PERSON>vel SDK for Sentry (https://sentry.io)", "homepage": "https://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "laravel", "log", "logging", "sentry"], "support": {"issues": "https://github.com/getsentry/sentry-laravel/issues", "source": "https://github.com/getsentry/sentry-laravel/tree/2.14.2"}, "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2022-10-13T09:21:29+00:00"}, {"name": "swagger-api/swagger-ui", "version": "v3.52.5", "source": {"type": "git", "url": "https://github.com/swagger-api/swagger-ui.git", "reference": "f1ad60dc92e7edb0898583e16c3e66fe3e9eada2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swagger-api/swagger-ui/zipball/f1ad60dc92e7edb0898583e16c3e66fe3e9eada2", "reference": "f1ad60dc92e7edb0898583e16c3e66fe3e9eada2", "shasum": ""}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": " Swagger UI is a collection of HTML, Javascript, and CSS assets that dynamically generate beautiful documentation from a Swagger-compliant API.", "homepage": "http://swagger.io", "keywords": ["api", "documentation", "openapi", "specification", "swagger", "ui"], "support": {"issues": "https://github.com/swagger-api/swagger-ui/issues", "source": "https://github.com/swagger-api/swagger-ui/tree/v3.52.5"}, "time": "2021-10-14T14:25:14+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.3.0", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/8a5d5072dca8f48460fce2f4131fcc495eec654c", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c", "shasum": ""}, "require": {"egulias/email-validator": "^2.0|^3.1", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.4"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "support": {"issues": "https://github.com/swiftmailer/swiftmailer/issues", "source": "https://github.com/swiftmailer/swiftmailer/tree/v6.3.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/swiftmailer/swiftmailer", "type": "tidelift"}], "abandoned": "symfony/mailer", "time": "2021-10-18T15:26:12+00:00"}, {"name": "symfony/console", "version": "v4.4.49", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "33fa45ffc81fdcc1ca368d4946da859c8cdb58d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/33fa45ffc81fdcc1ca368d4946da859c8cdb58d9", "reference": "33fa45ffc81fdcc1ca368d4946da859c8cdb58d9", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<4.3|>=5", "symfony/lock": "<4.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/event-dispatcher": "^4.3", "symfony/lock": "^4.4|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/var-dumper": "^4.3|^5.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/console/tree/v4.4.49"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-05T17:10:16+00:00"}, {"name": "symfony/css-selector", "version": "v5.4.40", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "ea43887e9afd2029509662d4f95e8b5ef6fc9bbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/ea43887e9afd2029509662d4f95e8b5ef6fc9bbb", "reference": "ea43887e9afd2029509662d4f95e8b5ef6fc9bbb", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.40"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:33:22+00:00"}, {"name": "symfony/debug", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "1a692492190773c5310bc7877cb590c04c2f05be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/1a692492190773c5310bc7877cb590c04c2f05be", "reference": "1a692492190773c5310bc7877cb590c04c2f05be", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/http-kernel": "<3.4"}, "require-dev": {"symfony/http-kernel": "^3.4|^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/debug/tree/v4.4.44"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "abandoned": "symfony/error-handler", "time": "2022-07-28T16:29:46+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "80d075412b557d41002320b96a096ca65aa2c98d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/80d075412b557d41002320b96a096ca65aa2c98d", "reference": "80d075412b557d41002320b96a096ca65aa2c98d", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-24T14:02:46+00:00"}, {"name": "symfony/error-handler", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "be731658121ef2d8be88f3a1ec938148a9237291"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/be731658121ef2d8be88f3a1ec938148a9237291", "reference": "be731658121ef2d8be88f3a1ec938148a9237291", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1|^2|^3", "symfony/debug": "^4.4.5", "symfony/var-dumper": "^4.4|^5.0"}, "require-dev": {"symfony/http-kernel": "^4.4|^5.0", "symfony/serializer": "^4.4|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v4.4.44"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-28T16:29:46+00:00"}, {"name": "symfony/event-dispatcher", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "1e866e9e5c1b22168e0ce5f0b467f19bba61266a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/1e866e9e5c1b22168e0ce5f0b467f19bba61266a", "reference": "1e866e9e5c1b22168e0ce5f0b467f19bba61266a", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/event-dispatcher-contracts": "^1.1", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<3.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "1.1"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/error-handler": "~3.4|~4.4", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.44"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v1.10.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "761c8b8387cfe5f8026594a75fdf0a4e83ba6974"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/761c8b8387cfe5f8026594a75fdf0a4e83ba6974", "reference": "761c8b8387cfe5f8026594a75fdf0a4e83ba6974", "shasum": ""}, "require": {"php": ">=7.1.3"}, "suggest": {"psr/event-dispatcher": "", "symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v1.10.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/finder", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "66bd787edb5e42ff59d3523f623895af05043e4f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/66bd787edb5e42ff59d3523f623895af05043e4f", "reference": "66bd787edb5e42ff59d3523f623895af05043e4f", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v4.4.44"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-29T07:35:46+00:00"}, {"name": "symfony/http-client", "version": "v5.4.41", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "87ca825717928d178de8a3458f163100925fb675"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/87ca825717928d178de8a3458f163100925fb675", "reference": "87ca825717928d178de8a3458f163100925fb675", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1|^3", "symfony/http-client-contracts": "^2.5.3", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.0|^2|^3"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "2.4"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "php-http/message-factory": "^1.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4.13|^5.1.5|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "keywords": ["http"], "support": {"source": "https://github.com/symfony/http-client/tree/v5.4.41"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-28T07:25:22+00:00"}, {"name": "symfony/http-client-contracts", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "e5cc97c2b4a4db0ba26bebc154f1426e3fd1d2f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/e5cc97c2b4a4db0ba26bebc154f1426e3fd1d2f1", "reference": "e5cc97c2b4a4db0ba26bebc154f1426e3fd1d2f1", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/http-client-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.5.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-03-26T19:42:53+00:00"}, {"name": "symfony/http-foundation", "version": "v4.4.49", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "191413c7b832c015bb38eae963f2e57498c3c173"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/191413c7b832c015bb38eae963f2e57498c3c173", "reference": "191413c7b832c015bb38eae963f2e57498c3c173", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/mime": "^4.3|^5.0", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"predis/predis": "~1.0", "symfony/expression-language": "^3.4|^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v4.4.49"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-04T16:17:57+00:00"}, {"name": "symfony/http-kernel", "version": "v4.4.51", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "ad8ab192cb619ff7285c95d28c69b36d718416c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/ad8ab192cb619ff7285c95d28c69b36d718416c7", "reference": "ad8ab192cb619ff7285c95d28c69b36d718416c7", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1|^2", "symfony/error-handler": "^4.4", "symfony/event-dispatcher": "^4.4", "symfony/http-client-contracts": "^1.1|^2", "symfony/http-foundation": "^4.4.30|^5.3.7", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/browser-kit": "<4.3", "symfony/config": "<3.4", "symfony/console": ">=5", "symfony/dependency-injection": "<4.3", "symfony/translation": "<4.2", "twig/twig": "<1.43|<2.13,>=2"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^4.3|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0", "symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/dom-crawler": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2|^5.0", "symfony/translation-contracts": "^1.1|^2", "twig/twig": "^1.43|^2.13|^3.0.4"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v4.4.51"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-11-10T13:31:29+00:00"}, {"name": "symfony/mime", "version": "v5.4.41", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "c71c7a1aeed60b22d05e738197e31daf2120bd42"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/c71c7a1aeed60b22d05e738197e31daf2120bd42", "reference": "c71c7a1aeed60b22d05e738197e31daf2120bd42", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<4.4", "symfony/serializer": "<5.4.35|>=6,<6.3.12|>=6.4,<6.4.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/process": "^5.4|^6.4", "symfony/property-access": "^4.4|^5.1|^6.0", "symfony/property-info": "^4.4|^5.1|^6.0", "symfony/serializer": "^5.4.35|~6.3.12|^6.4.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v5.4.41"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-28T09:36:24+00:00"}, {"name": "symfony/options-resolver", "version": "v5.4.40", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "bd1afbde6613a8d6b956115e0e14b196191fd0c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/bd1afbde6613a8d6b956115e0e14b196191fd0c4", "reference": "bd1afbde6613a8d6b956115e0e14b196191fd0c4", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php73": "~1.0", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v5.4.40"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:33:22+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "0424dff1c58f028c451efff2045f5d92410bd540"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/0424dff1c58f028c451efff2045f5d92410bd540", "reference": "0424dff1c58f028c451efff2045f5d92410bd540", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "c027e6a3c6aee334663ec21f5852e89738abc805"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/c027e6a3c6aee334663ec21f5852e89738abc805", "reference": "c027e6a3c6aee334663ec21f5852e89738abc805", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "a6e83bdeb3c84391d1dfe16f42e40727ce524a5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/a6e83bdeb3c84391d1dfe16f42e40727ce524a5c", "reference": "a6e83bdeb3c84391d1dfe16f42e40727ce524a5c", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "a95281b0be0d9ab48050ebd988b967875cdb9fdb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/a95281b0be0d9ab48050ebd988b967875cdb9fdb", "reference": "a95281b0be0d9ab48050ebd988b967875cdb9fdb", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "fd22ab50000ef01661e2a31d850ebaa297f8e03c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/fd22ab50000ef01661e2a31d850ebaa297f8e03c", "reference": "fd22ab50000ef01661e2a31d850ebaa297f8e03c", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-19T12:30:46+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "10112722600777e02d2745716b70c5db4ca70442"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/10112722600777e02d2745716b70c5db4ca70442", "reference": "10112722600777e02d2745716b70c5db4ca70442", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-19T12:30:46+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "ec444d3f3f6505bb28d11afa41e75faadebc10a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/ec444d3f3f6505bb28d11afa41e75faadebc10a1", "reference": "ec444d3f3f6505bb28d11afa41e75faadebc10a1", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "77fa7995ac1b21ab60769b7323d600a991a90433"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/77fa7995ac1b21ab60769b7323d600a991a90433", "reference": "77fa7995ac1b21ab60769b7323d600a991a90433", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/process", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "5cee9cdc4f7805e2699d9fd66991a0e6df8252a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/5cee9cdc4f7805e2699d9fd66991a0e6df8252a2", "reference": "5cee9cdc4f7805e2699d9fd66991a0e6df8252a2", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v4.4.44"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T13:16:42+00:00"}, {"name": "symfony/psr-http-message-bridge", "version": "v2.1.4", "source": {"type": "git", "url": "https://github.com/symfony/psr-http-message-bridge.git", "reference": "a125b93ef378c492e274f217874906fb9babdebb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/a125b93ef378c492e274f217874906fb9babdebb", "reference": "a125b93ef378c492e274f217874906fb9babdebb", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0", "symfony/http-foundation": "^4.4 || ^5.0 || ^6.0"}, "require-dev": {"nyholm/psr7": "^1.1", "psr/log": "^1.1 || ^2 || ^3", "symfony/browser-kit": "^4.4 || ^5.0 || ^6.0", "symfony/config": "^4.4 || ^5.0 || ^6.0", "symfony/event-dispatcher": "^4.4 || ^5.0 || ^6.0", "symfony/framework-bundle": "^4.4 || ^5.0 || ^6.0", "symfony/http-kernel": "^4.4 || ^5.0 || ^6.0", "symfony/phpunit-bridge": "^5.4@dev || ^6.0"}, "suggest": {"nyholm/psr7": "For a super lightweight PSR-7/17 implementation"}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-main": "2.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\PsrHttpMessage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "PSR HTTP message bridge", "homepage": "http://symfony.com", "keywords": ["http", "http-message", "psr-17", "psr-7"], "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/v2.1.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-28T22:46:34+00:00"}, {"name": "symfony/routing", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "f7751fd8b60a07f3f349947a309b5bdfce22d6ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/f7751fd8b60a07f3f349947a309b5bdfce22d6ae", "reference": "f7751fd8b60a07f3f349947a309b5bdfce22d6ae", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/config": "<4.2", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "psr/log": "^1|^2|^3", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v4.4.44"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/service-contracts", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "a2329596ddc8fd568900e3fc76cba42489ecc7f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/a2329596ddc8fd568900e3fc76cba42489ecc7f3", "reference": "a2329596ddc8fd568900e3fc76cba42489ecc7f3", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-04-21T15:04:16+00:00"}, {"name": "symfony/translation", "version": "v4.4.47", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "45036b1d53accc48fe9bab71ccd86d57eba0dd94"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/45036b1d53accc48fe9bab71ccd86d57eba0dd94", "reference": "45036b1d53accc48fe9bab71ccd86d57eba0dd94", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^1.1.6|^2"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/yaml": "<3.4"}, "provide": {"symfony/translation-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/finder": "~2.8|~3.0|~4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v4.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-10-03T15:15:11+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "b0073a77ac0b7ea55131020e87b1e3af540f4664"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/b0073a77ac0b7ea55131020e87b1e3af540f4664", "reference": "b0073a77ac0b7ea55131020e87b1e3af540f4664", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.5.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T13:51:25+00:00"}, {"name": "symfony/var-dumper", "version": "v4.4.47", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "1069c7a3fca74578022fab6f81643248d02f8e63"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/1069c7a3fca74578022fab6f81643248d02f8e63", "reference": "1069c7a3fca74578022fab6f81643248d02f8e63", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5", "symfony/polyfill-php80": "^1.16"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<3.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^3.4|^4.0|^5.0", "symfony/process": "^4.4|^5.0", "twig/twig": "^1.43|^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v4.4.47"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-10-03T15:15:11+00:00"}, {"name": "symfony/yaml", "version": "v4.4.45", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "aeccc4dc52a9e634f1d1eebeb21eacfdcff1053d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/aeccc4dc52a9e634f1d1eebeb21eacfdcff1053d", "reference": "aeccc4dc52a9e634f1d1eebeb21eacfdcff1053d", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/console": "<3.4"}, "require-dev": {"symfony/console": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-08-02T15:47:23+00:00"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "v2.2.7", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "83ee6f38df0a63106a9e4536e3060458b74ccedb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/83ee6f38df0a63106a9e4536e3060458b74ccedb", "reference": "83ee6f38df0a63106a9e4536e3060458b74ccedb", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.5 || ^7.0 || ^8.0", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^7.5 || ^8.5.21 || ^9.5.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/v2.2.7"}, "time": "2023-12-08T13:03:43+00:00"}, {"name": "vlucas/phpdotenv", "version": "v3.6.10", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "5b547cdb25825f10251370f57ba5d9d924e6f68e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/5b547cdb25825f10251370f57ba5d9d924e6f68e", "reference": "5b547cdb25825f10251370f57ba5d9d924e6f68e", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0", "phpoption/phpoption": "^1.5.2", "symfony/polyfill-ctype": "^1.17"}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.21"}, "suggest": {"ext-filter": "Required to use the boolean validator.", "ext-pcre": "Required to use most of the library."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v3.6.10"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2021-12-12T23:02:06+00:00"}, {"name": "youthage/laravel-3des", "version": "4.0", "source": {"type": "git", "url": "https://github.com/heyhip/Laravel-3DES.git", "reference": "ff0d556106d353b335d1ae871091a189814fe2c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/heyhip/Laravel-3DES/zipball/ff0d556106d353b335d1ae871091a189814fe2c1", "reference": "ff0d556106d353b335d1ae871091a189814fe2c1", "shasum": ""}, "require": {"php": ">=5.3.9"}, "type": "library", "extra": {"laravel": {"providers": ["laraveldes3\\Des3Provider"], "aliases": {"DES3": "laraveldes3\\Des3Facade"}}}, "autoload": {"psr-4": {"laraveldes3\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "YouthAge", "email": "<EMAIL>"}], "description": "Laravel Des3 encode", "support": {"issues": "https://github.com/heyhip/Laravel-3DES/issues", "source": "https://github.com/heyhip/Laravel-3DES/tree/4.0"}, "time": "2019-03-14T15:52:27+00:00"}, {"name": "zircote/swagger-php", "version": "3.3.7", "source": {"type": "git", "url": "https://github.com/zircote/swagger-php.git", "reference": "e8c3bb316e385e93a0c7e8b2aa0681079244c381"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zircote/swagger-php/zipball/e8c3bb316e385e93a0c7e8b2aa0681079244c381", "reference": "e8c3bb316e385e93a0c7e8b2aa0681079244c381", "shasum": ""}, "require": {"doctrine/annotations": "^1.7", "ext-json": "*", "php": ">=7.2", "psr/log": "^1.1 || ^2.0 || ^3.0", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}, "require-dev": {"composer/package-versions-deprecated": "*********", "friendsofphp/php-cs-fixer": "^2.17 || ^3.0", "phpunit/phpunit": ">=8.5.14"}, "bin": ["bin/openapi"], "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"OpenApi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://bfanger.nl"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://radebatz.net"}], "description": "swagger-php - Generate interactive documentation for your RESTful API using phpdoc annotations", "homepage": "https://github.com/zircote/swagger-php/", "keywords": ["api", "json", "rest", "service discovery"], "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.3.7"}, "time": "2023-01-03T21:17:10+00:00"}], "packages-dev": [{"name": "brainmaestro/composer-git-hooks", "version": "v2.8.5", "source": {"type": "git", "url": "https://github.com/BrainMaestro/composer-git-hooks.git", "reference": "ffed8803690ac12214082120eee3441b00aa390e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/BrainMaestro/composer-git-hooks/zipball/ffed8803690ac12214082120eee3441b00aa390e", "reference": "ffed8803690ac12214082120eee3441b00aa390e", "shasum": ""}, "require": {"php": "^5.6 || >=7.0", "symfony/console": "^3.2 || ^4.0 || ^5.0"}, "require-dev": {"ext-json": "*", "friendsofphp/php-cs-fixer": "^2.9", "phpunit/phpunit": "^5.7 || ^7.0"}, "bin": ["cghooks"], "type": "library", "extra": {"hooks": {"pre-commit": "composer check-style", "pre-push": ["composer test", "appver=$(grep -o -E '\\d.\\d.\\d' cghooks)", "tag=$(git describe --tags --abbrev=0)", "if [ \"$tag\" != \"v$appver\" ]; then", "echo \"The most recent tag $tag does not match the application version $appver\\n\"", "tag=${tag#v}", "sed -i -E \"s/$appver/$tag/\" cghooks", "exit 1", "fi"]}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"BrainMaestro\\GitHooks\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Easily manage git hooks in your composer config", "keywords": ["HOOK", "composer", "git"], "support": {"issues": "https://github.com/BrainMaestro/composer-git-hooks/issues", "source": "https://github.com/BrainMaestro/composer-git-hooks/tree/v2.8.5"}, "time": "2021-02-08T15:59:11+00:00"}, {"name": "doctrine/instantiator", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/0a0fa9780f5d4e507415a065172d26a98d02047b", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.30 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.5.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:15:36+00:00"}, {"name": "fzaninotto/faker", "version": "v1.9.2", "source": {"type": "git", "url": "https://github.com/fzaninotto/Faker.git", "reference": "848d8125239d7dbf8ab25cb7f054f1a630e68c2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fzaninotto/Faker/zipball/848d8125239d7dbf8ab25cb7f054f1a630e68c2e", "reference": "848d8125239d7dbf8ab25cb7f054f1a630e68c2e", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7", "squizlabs/php_codesniffer": "^2.9.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/fzaninotto/Faker/issues", "source": "https://github.com/fzaninotto/Faker/tree/v1.9.2"}, "abandoned": true, "time": "2020-12-11T09:56:16+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v1.2.2", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "b37020aa976fa52d3de9aa904aa2522dc518f79c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/b37020aa976fa52d3de9aa904aa2522dc518f79c", "reference": "b37020aa976fa52d3de9aa904aa2522dc518f79c", "shasum": ""}, "require": {"php": ">=5.3.2"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "1.3.3", "satooshi/php-coveralls": "dev-master"}, "type": "library", "autoload": {"files": ["hamcrest/Hamcrest.php"], "classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/master"}, "time": "2015-05-11T14:41:42+00:00"}, {"name": "krlove/code-generator", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/krlove/code-generator.git", "reference": "1ac521f5ef79a376282e3315ba6a13a83c63fb9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/krlove/code-generator/zipball/1ac521f5ef79a376282e3315ba6a13a83c63fb9a", "reference": "1ac521f5ef79a376282e3315ba6a13a83c63fb9a", "shasum": ""}, "type": "library", "autoload": {"psr-4": {"Krlove\\CodeGenerator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Code Generator", "support": {"issues": "https://github.com/krlove/code-generator/issues", "source": "https://github.com/krlove/code-generator/tree/1.0.1"}, "time": "2022-02-20T12:24:22+00:00"}, {"name": "krlove/eloquent-model-generator", "version": "1.3.8", "source": {"type": "git", "url": "https://github.com/krlove/eloquent-model-generator.git", "reference": "788a73bafb3c5f60a20890ef9278878dc4221355"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/krlove/eloquent-model-generator/zipball/788a73bafb3c5f60a20890ef9278878dc4221355", "reference": "788a73bafb3c5f60a20890ef9278878dc4221355", "shasum": ""}, "require": {"doctrine/dbal": "^2.5 || ^3.0", "illuminate/config": "^5.0 || ^6.0|| ^7.0 || ^8.0 || ^9.0", "illuminate/console": "^5.0 || ^6.0|| ^7.0 || ^8.0 || ^9.0", "illuminate/database": "^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0", "illuminate/support": "^5.0 || ^6.0|| ^7.0 || ^8.0 || ^9.0", "krlove/code-generator": "^1.0"}, "type": "library", "extra": {"laravel": {"providers": ["Krlove\\EloquentModelGenerator\\Provider\\GeneratorServiceProvider"]}}, "autoload": {"psr-4": {"Krlove\\EloquentModelGenerator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Eloquent Model Generator", "support": {"issues": "https://github.com/krlove/eloquent-model-generator/issues", "source": "https://github.com/krlove/eloquent-model-generator/tree/1.3.8"}, "time": "2022-02-10T12:52:03+00:00"}, {"name": "mockery/mockery", "version": "0.9.11", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "be9bf28d8e57d67883cba9fcadfcff8caab667f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/be9bf28d8e57d67883cba9fcadfcff8caab667f8", "reference": "be9bf28d8e57d67883cba9fcadfcff8caab667f8", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "~1.1", "lib-pcre": ">=7.0", "php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.9.x-dev"}}, "autoload": {"psr-0": {"Mockery": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.astrumfutura.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://davedevelopment.co.uk"}], "description": "Mockery is a simple yet flexible PHP mock object framework for use in unit testing with PHPUnit, PHPSpec or any other testing framework. Its core goal is to offer a test double framework with a succinct API capable of clearly defining all possible object operations and interactions using a human readable Domain Specific Language (DSL). Designed as a drop in alternative to PHPUnit's phpunit-mock-objects library, Mockery is easy to integrate with PHPUnit and can operate alongside phpunit-mock-objects without the World ending.", "homepage": "http://github.com/padraic/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/0.9"}, "time": "2019-02-12T16:07:13+00:00"}, {"name": "myclabs/deep-copy", "version": "1.12.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c", "reference": "3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.12.0"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2024-06-12T14:39:25+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.4.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "9d07b3f7fdcf5efec5d1609cba3c19c5ea2bdc9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/9d07b3f7fdcf5efec5d1609cba3c19c5ea2bdc9c", "reference": "9d07b3f7fdcf5efec5d1609cba3c19c5ea2bdc9c", "shasum": ""}, "require": {"doctrine/deprecations": "^1.1", "ext-filter": "*", "php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.7", "phpstan/phpdoc-parser": "^1.7", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.5", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-webmozart-assert": "^1.2", "phpunit/phpunit": "^9.5", "vimeo/psalm": "^5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.4.1"}, "time": "2024-05-21T05:55:05+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.8.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "153ae662783729388a584b4361f2545e4d841e3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/153ae662783729388a584b4361f2545e4d841e3c", "reference": "153ae662783729388a584b4361f2545e4d841e3c", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.13"}, "require-dev": {"ext-tokenizer": "*", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "rector/rector": "^0.13.9", "vimeo/psalm": "^4.25"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.8.2"}, "time": "2024-02-23T11:10:43+00:00"}, {"name": "phpspec/prophecy", "version": "v1.10.3", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "451c3cd1418cf640de218914901e51b064abb093"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/451c3cd1418cf640de218914901e51b064abb093", "reference": "451c3cd1418cf640de218914901e51b064abb093", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0|^5.0", "sebastian/comparator": "^1.2.3|^2.0|^3.0|^4.0", "sebastian/recursion-context": "^1.0|^2.0|^3.0|^4.0"}, "require-dev": {"phpspec/phpspec": "^2.5 || ^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5 || ^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10.x-dev"}}, "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.10.3"}, "time": "2020-03-05T15:02:03+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "1.29.1", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "fcaefacf2d5c417e928405b71b400d4ce10daaf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/fcaefacf2d5c417e928405b71b400d4ce10daaf4", "reference": "fcaefacf2d5c417e928405b71b400d4ce10daaf4", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^4.15", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.29.1"}, "time": "2024-05-31T08:52:43+00:00"}, {"name": "phpunit/php-code-coverage", "version": "4.0.8", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "ef7b2f56815df854e66ceaee8ebe9393ae36a40d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ef7b2f56815df854e66ceaee8ebe9393ae36a40d", "reference": "ef7b2f56815df854e66ceaee8ebe9393ae36a40d", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^5.6 || ^7.0", "phpunit/php-file-iterator": "^1.3", "phpunit/php-text-template": "^1.2", "phpunit/php-token-stream": "^1.4.2 || ^2.0", "sebastian/code-unit-reverse-lookup": "^1.0", "sebastian/environment": "^1.3.2 || ^2.0", "sebastian/version": "^1.0 || ^2.0"}, "require-dev": {"ext-xdebug": "^2.1.4", "phpunit/phpunit": "^5.7"}, "suggest": {"ext-xdebug": "^2.5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/4.0"}, "time": "2017-04-02T07:44:40+00:00"}, {"name": "phpunit/php-file-iterator", "version": "1.4.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/730b01bc3e867237eaac355e06a36b85dd93a8b4", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/1.4.5"}, "time": "2017-11-27T13:52:08+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/1.2.1"}, "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "1.0.9", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-timer/tree/master"}, "time": "2017-02-26T11:10:40+00:00"}, {"name": "phpunit/php-token-stream", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "791198a2c6254db10131eecfe8c06670700904db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/791198a2c6254db10131eecfe8c06670700904db", "reference": "791198a2c6254db10131eecfe8c06670700904db", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-token-stream/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-token-stream/tree/master"}, "abandoned": true, "time": "2017-11-27T05:48:46+00:00"}, {"name": "phpunit/phpunit", "version": "5.7.27", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit/zipball/b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c", "reference": "b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "myclabs/deep-copy": "~1.3", "php": "^5.6 || ^7.0", "phpspec/prophecy": "^1.6.2", "phpunit/php-code-coverage": "^4.0.4", "phpunit/php-file-iterator": "~1.4", "phpunit/php-text-template": "~1.2", "phpunit/php-timer": "^1.0.6", "phpunit/phpunit-mock-objects": "^3.2", "sebastian/comparator": "^1.2.4", "sebastian/diff": "^1.4.3", "sebastian/environment": "^1.3.4 || ^2.0", "sebastian/exporter": "~2.0", "sebastian/global-state": "^1.1", "sebastian/object-enumerator": "~2.0", "sebastian/resource-operations": "~1.0", "sebastian/version": "^1.0.6|^2.0.1", "symfony/yaml": "~2.1|~3.0|~4.0"}, "conflict": {"phpdocumentor/reflection-docblock": "3.0.2"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-xdebug": "*", "phpunit/php-invoker": "~1.1"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.7.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/5.7.27"}, "time": "2018-02-01T05:50:59+00:00"}, {"name": "phpunit/phpunit-mock-objects", "version": "3.4.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects.git", "reference": "a23b761686d50a560cc56233b9ecf49597cc9118"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects/zipball/a23b761686d50a560cc56233b9ecf49597cc9118", "reference": "a23b761686d50a560cc56233b9ecf49597cc9118", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.6 || ^7.0", "phpunit/php-text-template": "^1.2", "sebastian/exporter": "^1.2 || ^2.0"}, "conflict": {"phpunit/phpunit": "<5.4.0"}, "require-dev": {"phpunit/phpunit": "^5.4"}, "suggest": {"ext-soap": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "support": {"irc": "irc://irc.freenode.net/phpunit", "issues": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/tree/3.4"}, "abandoned": true, "time": "2017-06-30T09:13:00+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "92a1a52e86d34cde6caa54f1b5ffa9fda18e5d54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/92a1a52e86d34cde6caa54f1b5ffa9fda18e5d54", "reference": "92a1a52e86d34cde6caa54f1b5ffa9fda18e5d54", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/1.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-01T13:45:45+00:00"}, {"name": "sebastian/comparator", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/diff": "~1.2", "sebastian/exporter": "~1.2 || ~2.0"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "http://www.github.com/sebastian<PERSON>mann/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/1.2"}, "time": "2017-01-29T09:50:25+00:00"}, {"name": "sebastian/diff", "version": "1.4.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "7f066a26a962dbe58ddea9f72a4e82874a3975a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/7f066a26a962dbe58ddea9f72a4e82874a3975a4", "reference": "7f066a26a962dbe58ddea9f72a4e82874a3975a4", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/1.4"}, "time": "2017-05-22T07:24:03+00:00"}, {"name": "sebastian/environment", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "5795ffe5dc5b02460c3e34222fee8cbe245d8fac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/5795ffe5dc5b02460c3e34222fee8cbe245d8fac", "reference": "5795ffe5dc5b02460c3e34222fee8cbe245d8fac", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/master"}, "time": "2016-11-26T07:53:53+00:00"}, {"name": "sebastian/exporter", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4", "reference": "ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/recursion-context": "~2.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/master"}, "time": "2016-11-19T08:54:04+00:00"}, {"name": "sebastian/global-state", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bc37d50fea7d017d3d340f230811c9f1d7280af4", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/1.1.1"}, "time": "2015-10-12T03:26:01+00:00"}, {"name": "sebastian/object-enumerator", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "1311872ac850040a79c3c058bea3e22d0f09cbb7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/1311872ac850040a79c3c058bea3e22d0f09cbb7", "reference": "1311872ac850040a79c3c058bea3e22d0f09cbb7", "shasum": ""}, "require": {"php": ">=5.6", "sebastian/recursion-context": "~2.0"}, "require-dev": {"phpunit/phpunit": "~5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/master"}, "time": "2017-02-18T15:18:39+00:00"}, {"name": "sebastian/recursion-context", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "2c3ba150cbec723aa057506e73a8d33bdb286c9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/2c3ba150cbec723aa057506e73a8d33bdb286c9a", "reference": "2c3ba150cbec723aa057506e73a8d33bdb286c9a", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/master"}, "time": "2016-11-19T07:33:16+00:00"}, {"name": "sebastian/resource-operations", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/resource-operations/zipball/ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "shasum": ""}, "require": {"php": ">=5.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"issues": "https://github.com/sebastian<PERSON>mann/resource-operations/issues", "source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/master"}, "time": "2015-07-28T20:34:47+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/master"}, "time": "2016-10-03T07:35:21+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=7.1", "ext-json": "*", "ext-pdo": "*"}, "platform-dev": [], "plugin-api-version": "2.0.0"}