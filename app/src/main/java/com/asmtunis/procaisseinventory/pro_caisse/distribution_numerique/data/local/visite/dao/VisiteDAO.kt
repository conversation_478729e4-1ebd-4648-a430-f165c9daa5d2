package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.visite.dao

import androidx.lifecycle.LiveData
import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.LIGNE_VISITE_TABLE
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.VISITE_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.LigneVisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.VisitesDn
import kotlinx.coroutines.flow.Flow

@Dao
interface VisiteDAO {
    @get:Query("SELECT * FROM $VISITE_TABLE where Status!='DELETED' order by strftime('%Y-%m-%d %H-%M-%S',VIS_DDM) desc")
    val all: Flow<List<VisitesDn>>

    @get:Query("SELECT count(*) FROM $VISITE_TABLE")
    val allCount: Flow<Int>


    @Query("UPDATE $VISITE_TABLE SET VIS_Num = :code , Status = 'SELECTED' , IsSync = 1 where VIS_Code_M = :CodeMobile")
    fun updateVisite(code: String, CodeMobile: String)



    @Query(
        "SELECT * FROM $VISITE_TABLE" +
            " JOIN $LIGNE_VISITE_TABLE ON $VISITE_TABLE.VIS_Num = $LIGNE_VISITE_TABLE.LG_VISNum" +
            " WHERE $VISITE_TABLE.VIS_Num= :visNum and $VISITE_TABLE.VIS_Exerc= :exercice"

    )
    fun loadAllVisiteAndLignesVisite(visNum: String, exercice: String): Flow<Map<VisitesDn, List<LigneVisitesDn>>>

    @Query("SELECT * FROM $VISITE_TABLE where VIS_CodeClient=:codeclient")
    fun getByCodeClient(codeclient: String?): List<VisitesDn>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(dnVisites: List<VisitesDn>)

    @Query("SELECT * FROM $VISITE_TABLE WHERE VIS_Code_M = :code")
    fun getByCodeM(code: String?): VisitesDn

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(dnVisite: VisitesDn)

    //@get:Query("SELECT * FROM $VISITE_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ")
    //val noSyncedToAddOrUpdate: Flow<List<VisitesDn>>

    @Query(
        "SELECT * FROM $VISITE_TABLE " +
                " JOIN $LIGNE_VISITE_TABLE ON $VISITE_TABLE.VIS_Num = $LIGNE_VISITE_TABLE.LG_VISNum" +

                " WHERE $VISITE_TABLE.IsSync=0 and  ($VISITE_TABLE.Status='INSERTED'  or $VISITE_TABLE.Status='UPDATED') "

    )
    fun noSyncedToAddOrUpdate(): Flow<Map<VisitesDn, List<LigneVisitesDn>>>


    @Query("SELECT * FROM $VISITE_TABLE " +
            " JOIN $LIGNE_VISITE_TABLE ON $VISITE_TABLE.VIS_Num = $LIGNE_VISITE_TABLE.LG_VISNum" +
            " WHERE $VISITE_TABLE.IsSync=0 and  $VISITE_TABLE.Status='DELETED'  "

    )
    fun noSyncedToDelete(): Flow<Map<VisitesDn, List<LigneVisitesDn>>>













    @get:Query("SELECT count(*) FROM $VISITE_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED' or Status='DELETED')")
    val countNonSync: Flow<Int>

    @get:Query("SELECT count(*) FROM $VISITE_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val noSyncCountMubtale: LiveData<Int?>?

    @get:Query("SELECT count(*) FROM $VISITE_TABLE where isSync=0 and Status='DELETED'")
    val noSynctoDeleteCountMubtale: LiveData<Int?>?

    @Query("delete from $VISITE_TABLE")
    fun deleteAll()

    @Delete
    fun delete(visitesDn: VisitesDn)

    //@Query("UPDATE $VISITE_TABLE SET Status = 'DELETED' , IsSync = 0 where VIS_Exerc= :exercice and (VIS_Num = :code or VIS_Code_M = :code)")
    @Query("UPDATE $VISITE_TABLE SET Status = 'DELETED' , IsSync = 0 where VIS_Exerc= :exercice and (VIS_Num = :code or VIS_Code_M = :code)")
    fun setIsDeletedVisite(code: String, exercice : String)
    @Query("UPDATE $VISITE_TABLE SET Status = :status , IsSync = :isSync where VIS_Exerc= :exercice and (VIS_Num = :code or VIS_Code_M = :code)")
    fun restDeletedVisite(code: String, exercice : String,status : String, isSync:Boolean)



    @Query("DELETE FROM $VISITE_TABLE where VIS_Code_M=:VIS_Code or VIS_Num=:VIS_Code and VIS_Exerc=:exercice")
    fun deleteVisiteByVisNum(VIS_Code: String, exercice : String)

    @Query("SELECT * FROM $VISITE_TABLE where VIS_CodeClient=:clientCode and Status!='DELETED' order by strftime('%Y-%m-%d %H-%M-%S',VIS_DDM) desc")
    fun getByClientCode(clientCode: String?): Flow<List<VisitesDn>>

    @Query("UPDATE $VISITE_TABLE SET VIS_CodeClient = :code_client where VIS_CodeClient = :oldCodeClient")
    fun updateCodeClient(code_client: String?, oldCodeClient: String?)

    @Query(
        "SELECT * FROM $VISITE_TABLE" +
            " JOIN $LIGNE_VISITE_TABLE ON $VISITE_TABLE.VIS_Num = $LIGNE_VISITE_TABLE.LG_VISNum" +
            " WHERE $VISITE_TABLE.VIS_Num= :visNum and $VISITE_TABLE.VIS_Exerc= :exercice" // +
        // " and Status!='DELETED' "
    )
    fun loadVisiteAndLignesVisite(visNum: String, exercice: String): Flow<Map<VisitesDn, List<LigneVisitesDn>>>

    @Query(
        "SELECT * FROM $VISITE_TABLE " +

            "JOIN $LIGNE_VISITE_TABLE ON $VISITE_TABLE.VIS_Num = $LIGNE_VISITE_TABLE.LG_VISNum" +
           " WHERE  CASE WHEN :codeClient != '' AND :codeClient IS NOT NULL THEN $VISITE_TABLE.VIS_CodeClient=:codeClient ELSE  $VISITE_TABLE.VIS_CodeClient !=:codeClient END " +
              //  "and $VISITE_TABLE.Status!='DELETED' "+
                " ORDER BY " +
                "CASE WHEN :sort_by = 'VIS_NomGerant'  AND :isAsc = 1 THEN $VISITE_TABLE.VIS_NomGerant END ASC, " +
                "CASE WHEN :sort_by = 'VIS_NomGerant'  AND :isAsc = 2 THEN $VISITE_TABLE.VIS_NomGerant END DESC, " +
                "CASE WHEN :sort_by = 'VIS_DDM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',$VISITE_TABLE.VIS_DDM) END ASC, " +
                "CASE WHEN :sort_by = 'VIS_DDM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',$VISITE_TABLE.VIS_DDM) END DESC, " +
                "CASE WHEN :sort_by = 'VIS_Num'  AND :isAsc = 1 THEN $VISITE_TABLE.VIS_Num END ASC, " +
                "CASE WHEN :sort_by = 'VIS_Num'  AND :isAsc = 2 THEN $VISITE_TABLE.VIS_Num END DESC "
            /*" ORDER BY " +
            "CASE WHEN :sort_by = 'VIS_NomGerant'  AND :isAsc = 1 THEN $LIGNE_VISITE_TABLE.LG_VISFamille END ASC, " +
            "CASE WHEN :sort_by = 'VIS_NomGerant'  AND :isAsc = 2 THEN $LIGNE_VISITE_TABLE.LG_VISFamille END DESC, " +
            "CASE WHEN :sort_by = 'VIS_DDM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',$LIGNE_VISITE_TABLE.LG_VISDDM) END ASC, " +
            "CASE WHEN :sort_by = 'VIS_DDM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',$LIGNE_VISITE_TABLE.LG_VISDDM) END DESC, " +
            "CASE WHEN :sort_by = 'VIS_Num'  AND :isAsc = 1 THEN $LIGNE_VISITE_TABLE.LG_VISNum END ASC, " +
            "CASE WHEN :sort_by = 'VIS_Num'  AND :isAsc = 2 THEN $LIGNE_VISITE_TABLE.LG_VISNum END DESC  "*/


    )
    fun getAllFiltred(isAsc: Int?, sort_by: String?, codeClient: String): Flow<Map<VisitesDn, List<LigneVisitesDn>>>

    @Query(
        "SELECT * FROM $VISITE_TABLE " +
            " JOIN $LIGNE_VISITE_TABLE ON $VISITE_TABLE.VIS_Num = $LIGNE_VISITE_TABLE.LG_VISNum" +

            " WHERE $VISITE_TABLE.VIS_NomGerant LIKE '%' || :filterString || '%' " +
            "and CASE WHEN :codeClient != '' AND :codeClient IS NOT NULL THEN $VISITE_TABLE.VIS_CodeClient=:codeClient ELSE $VISITE_TABLE.VIS_CodeClient !=:codeClient END " +
                "and $VISITE_TABLE.Status!='DELETED' "+
            // " and Status!='DELETED' "+
            " ORDER BY " +
            "CASE WHEN :sort_by = 'VIS_NomGerant'  AND :isAsc = 1 THEN $VISITE_TABLE.VIS_NomGerant END ASC, " +
            "CASE WHEN :sort_by = 'VIS_NomGerant'  AND :isAsc = 2 THEN $VISITE_TABLE.VIS_NomGerant END DESC, " +
            "CASE WHEN :sort_by = 'VIS_DDM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',$VISITE_TABLE.VIS_DDM) END ASC, " +
            "CASE WHEN :sort_by = 'VIS_DDM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',$VISITE_TABLE.VIS_DDM) END DESC, " +
            "CASE WHEN :sort_by = 'VIS_Num'  AND :isAsc = 1 THEN $VISITE_TABLE.VIS_Num END ASC, " +
            "CASE WHEN :sort_by = 'VIS_Num'  AND :isAsc = 2 THEN $VISITE_TABLE.VIS_Num END DESC "
    )
    fun filterByNomGerant(filterString: String, sort_by: String?, isAsc: Int?, codeClient: String): Flow<Map<VisitesDn, List<LigneVisitesDn>>>

    @Query(
        "SELECT * FROM $VISITE_TABLE " +
            " JOIN $LIGNE_VISITE_TABLE ON $VISITE_TABLE.VIS_Num = $LIGNE_VISITE_TABLE.LG_VISNum" +

            " WHERE $VISITE_TABLE.VIS_Num LIKE '%' || :filterString || '%' " +
            "and  CASE WHEN :codeClient != '' AND :codeClient IS NOT NULL THEN $VISITE_TABLE.VIS_CodeClient=:codeClient ELSE $VISITE_TABLE.VIS_CodeClient !=:codeClient END " +
              //  "and $VISITE_TABLE.Status!='DELETED' "+
            " ORDER BY " +
            "CASE WHEN :sort_by = 'VIS_NomGerant'  AND :isAsc = 1 THEN $VISITE_TABLE.VIS_NomGerant END ASC, " +
            "CASE WHEN :sort_by = 'VIS_NomGerant'  AND :isAsc = 2 THEN $VISITE_TABLE.VIS_NomGerant END DESC, " +
            "CASE WHEN :sort_by = 'VIS_DDM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',$VISITE_TABLE.VIS_DDM) END ASC, " +
            "CASE WHEN :sort_by = 'VIS_DDM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',$VISITE_TABLE.VIS_DDM) END DESC, " +
            "CASE WHEN :sort_by = 'VIS_Num'  AND :isAsc = 1 THEN $VISITE_TABLE.VIS_Num END ASC, " +
            "CASE WHEN :sort_by = 'VIS_Num'  AND :isAsc = 2 THEN $VISITE_TABLE.VIS_Num END DESC "
    )
    fun filterByNumVisite(filterString: String, sort_by: String?, isAsc: Int?, codeClient: String): Flow<Map<VisitesDn, List<LigneVisitesDn>>>

    @Query(
        "SELECT * FROM $VISITE_TABLE " +
            " JOIN $LIGNE_VISITE_TABLE ON $VISITE_TABLE.VIS_Num = $LIGNE_VISITE_TABLE.LG_VISNum" +

            " WHERE $VISITE_TABLE.VIS_CodeClient LIKE '%' || :filterString || '%'  or $VISITE_TABLE.VIS_NomClient LIKE '%' || :filterString || '%'"  +
            "and  CASE WHEN :codeClient != '' AND :codeClient IS NOT NULL THEN $VISITE_TABLE.VIS_CodeClient=:codeClient ELSE $VISITE_TABLE.VIS_CodeClient !=:codeClient END " +

              //  "and $VISITE_TABLE.Status!='DELETED' "+
            " ORDER BY " +
            "CASE WHEN :sort_by = 'VIS_NomGerant'  AND :isAsc = 1 THEN $VISITE_TABLE.VIS_NomGerant END ASC, " +
            "CASE WHEN :sort_by = 'VIS_NomGerant'  AND :isAsc = 2 THEN $VISITE_TABLE.VIS_NomGerant END DESC, " +
            "CASE WHEN :sort_by = 'VIS_DDM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',$VISITE_TABLE.VIS_DDM) END ASC, " +
            "CASE WHEN :sort_by = 'VIS_DDM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',$VISITE_TABLE.VIS_DDM) END DESC, " +
            "CASE WHEN :sort_by = 'VIS_Num'  AND :isAsc = 1 THEN $VISITE_TABLE.VIS_Num END ASC, " +
            "CASE WHEN :sort_by = 'VIS_Num'  AND :isAsc = 2 THEN $VISITE_TABLE.VIS_Num END DESC "
    )
    fun filterByProspect(filterString: String, sort_by: String?, isAsc: Int?, codeClient: String): Flow<Map<VisitesDn, List<LigneVisitesDn>>>

    @Query(
        "SELECT * FROM $VISITE_TABLE " +

            "JOIN $LIGNE_VISITE_TABLE ON $VISITE_TABLE.VIS_Num = $LIGNE_VISITE_TABLE.LG_VISNum" +
            " WHERE $VISITE_TABLE.VIS_CodeClient= :codeClient "+
                "order by strftime('%Y-%m-%d %H-%M-%S',VIS_DDM) desc"

    )
    fun getVisiteListByClient(codeClient: String): Flow<Map<VisitesDn, List<LigneVisitesDn>>>
}
