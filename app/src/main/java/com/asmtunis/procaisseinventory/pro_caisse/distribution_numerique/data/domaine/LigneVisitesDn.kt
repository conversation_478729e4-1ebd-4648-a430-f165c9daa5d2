package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.model.BaseModel
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


//@Entity(tableName = ProCaisseConstants.LIGNE_VISITE_TABLE,primaryKeys = ["LG_VISNum", "LG_VISExerc", "LG_VISFamille", "LG_VISTier"])
@Entity(tableName = ProCaisseConstants.LIGNE_VISITE_TABLE)
@Serializable
data class LigneVisitesDn @OptIn(ExperimentalSerializationApi::class) constructor(

    @PrimaryKey(autoGenerate = true)
    @Transient
    val id: Long = 0,
    @ColumnInfo(name = "LG_VISNum")
    @SerialName("LG_VISNum")
    var lgVISNum: String = "",

    @ColumnInfo(name = "LG_VISFamille")
    @SerialName("LG_VISFamille")
    var lgVISFamille: String = "",

    @ColumnInfo(name = "LG_VISExerc")
    @SerialName("LG_VISExerc")
    var lgVISExerc: String = "",

    @ColumnInfo(name = "LG_VISTier")
    @SerialName("LG_VISTier")
    var lgVISTier: String = "",

    @ColumnInfo(name = "LG_VISInfo1")
    @SerialName("LG_VISInfo1")
    @EncodeDefault(EncodeDefault.Mode.ALWAYS)
    var lgVISInfo1: String? = null,

    @ColumnInfo(name = "LG_VISInfo2")
    @SerialName("LG_VISInfo2")
    @EncodeDefault(EncodeDefault.Mode.ALWAYS)
    var lgVISInfo2: String? = null,

    @ColumnInfo(name = "LG_VISInfo3")
    @SerialName("LG_VISInfo3")
    @EncodeDefault(EncodeDefault.Mode.ALWAYS)
    var lgVISInfo3: String? = null,

    @ColumnInfo(name = "LG_VISDDM")
    @SerialName("LG_VISDDM")
    var lgVISDDM: String? = ""
) : BaseModel()
