<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;

class TVAWSMobile extends Controller
{


    public function getTVAByCode(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('TVA')->where('TVA_Code', $data["object"])->first());

    }


    /*fonction get data from database*/

    public function getTVA(Request $request)
    {


        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $TVA = $connection->table('TVA')->get();
        //afficher tous les lignes
        foreach ($TVA as $T) {

            $data[] = ['TVA_Code' => $T->TVA_Code,

                'TVA_User' => $T->TVA_User,
                'TVA_Station' => $T->TVA_Station
            ];

        }

        return response()->json($connection->table('TVA')->get());
    }


    public function getTVAByX(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('TVA')->where($request->field, $request->value)->get());

    }


    public function addTVA(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        if (!empty ($data)) {
            $T = $connection->table('TVA')->insert($data);

            return response()->json($T);

        } else {
            return (false);
        }


    }


    public function updateTVA(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        $connection->table('TVA')->where('TVA_Code', $data["TVA_Code"])->update($data);

        return ("Data modified");

    }


    public function deleteTVA(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $connection->table('TVA')->where('TVA_Code', $data["object"])->delete();
        return ("Data Deleted");
    }


}





