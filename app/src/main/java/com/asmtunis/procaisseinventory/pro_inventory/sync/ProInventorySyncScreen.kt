package com.asmtunis.procaisseinventory.pro_inventory.sync

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.KeyboardReturn
import androidx.compose.material.icons.filled.CloudSync
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.shared_ui_components.SharedSyncView
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.utils.Sync.getProInventoryTotalNoSyncCount
import com.asmtunis.procaisseinventory.core.utils.Sync.getSharedTotalNoSyncCount
import com.asmtunis.procaisseinventory.core.utils.Sync.isSyncInProInventory
import com.asmtunis.procaisseinventory.core.utils.Sync.syncAllProInventory
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.buttons.SyncButtons
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels

@Composable
fun ProInventorySyncScreen(
    navigate: (route: String) -> Unit,
    navigatePopUpTo: (route: String, popUpTo: String, isInclusive: Boolean) -> Unit,
    popBackStack: () -> Unit,
    navigateUp: () -> Unit,
    dataViewModel: DataViewModel,
    syncInventoryViewModel: SyncInventoryViewModel,
    networkViewModel: NetworkViewModel,
    syncSharedViewModels: SyncSharedViewModels,
) {
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val noSyncCount =
        getProInventoryTotalNoSyncCount(
            syncInventoryViewModel = syncInventoryViewModel,
        ) + getSharedTotalNoSyncCount(syncSharedViewModels = syncSharedViewModels)

    val isConnected = networkViewModel.isConnected

    val syncArticlesViewModel = syncSharedViewModels.syncArticlesViewModel
    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = { popBackStack() },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = stringResource(id = R.string.sync_title),
            )
        },
        floatingActionButton = {
            val density = LocalDensity.current

            AnimatedVisibility(
                // modifier = modifier,
                visible =
                    isConnected &&
                        !isSyncInProInventory(
                            syncSharedViewModels = syncSharedViewModels,
                            syncInventoryViewModel = syncInventoryViewModel,
                        ),
                enter =
                    slideInVertically {
                        with(density) { 40.dp.roundToPx() }
                    } + fadeIn(),
                exit =
                    fadeOut(
                        animationSpec =
                            keyframes {
                                this.durationMillis = 120
                            },
                    ),
            ) {
                FloatingActionButton(
                    onClick = {
                        if (noSyncCount > 0) {
                            syncAllProInventory(
                                syncSharedViewModels = syncSharedViewModels,
                                syncInventoryViewModel = syncInventoryViewModel,
                            )
                        } else {
                          navigateUp()
                        }
                    },
                ) {
                    if (!isConnected) LottieAnim(lotti = R.raw.no_connection, size = 25.dp)
                    // Text(text = if (noSyncCount > 0) "Sync ALL" else "Exit")
                    Icon(
                        imageVector = if (noSyncCount > 0) Icons.Default.CloudSync else Icons.AutoMirrored.Filled.KeyboardReturn,
                        contentDescription = stringResource(id = R.string.cd_favorite_button),
                    )
                    if (isSyncInProInventory(
                            syncSharedViewModels = syncSharedViewModels,
                            syncInventoryViewModel = syncInventoryViewModel,
                        )
                    ) {
                        LottieAnim(lotti = R.raw.loading, size = 60.dp)
                    }
                }
            }
        },
    ) { padding ->
        Column(
            modifier =
                Modifier
                    .verticalScroll(rememberScrollState())
                    .padding(padding)
                    .fillMaxSize(),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
             SharedSyncView (
                isConnected = isConnected,
                syncSharedViewModels = syncSharedViewModels,
            )
            /**
             * *********************
             *    A C H A T
             * *********************
             */

                SyncButtons(
                    text =  "Sync Achat ",
                    nbrNotSync = syncInventoryViewModel.bonEntreeNotSync.size,
                    remoteResponseState = syncInventoryViewModel.responseAddAchatState,
                    emailBody = syncInventoryViewModel.notSyncBonEntreeObj,
                    isConnected = isConnected,
                    onClickSync = { syncInventoryViewModel.syncAchat() }

                )


            /**
             * *********************
             *  *  *   B O N  * * *  T R A N S F E R T * * *
             * *********************
             */

                SyncButtons(
                    text =  "Sync Bon Transfert ",
                    nbrNotSync = syncInventoryViewModel.bonTransfertNotSync.size,
                    remoteResponseState = syncInventoryViewModel.responseAddBonTransfertState,
                    emailBody = syncInventoryViewModel.notSyncBonTransfertObj,
                    isConnected = isConnected,
                    onClickSync = { syncInventoryViewModel.syncBnTransfert() }

                )


            /**
             * *********************
             *  *  *  I N V E N T A I R E * * *
             * *********************
             */

                SyncButtons(
                    text =  "Sync Inventaire ",
                    nbrNotSync = syncInventoryViewModel.inventaireNotSync.size,
                    remoteResponseState = syncInventoryViewModel.responseAddInventaireState,
                    emailBody = syncInventoryViewModel.notSyncInventaireObj,
                    isConnected = isConnected,
                    onClickSync = { syncInventoryViewModel.syncInventaire() }

                )


            /**
             * *********************
             *  *  *  T I C K E T *** R A Y O N  * * *
             * *********************
             */

                SyncButtons(
                    text =  "Sync Ticket Rayon ",
                    nbrNotSync = syncInventoryViewModel.ticketRayonNotSync.size,
                    remoteResponseState = syncInventoryViewModel.responseAddTicketRayonState,
                    emailBody = syncInventoryViewModel.notSyncTicketRayonObj,
                    isConnected = isConnected,
                    onClickSync = { syncInventoryViewModel.syncTicketRayon() }

                )


            /**
             * *********************
             *  *  *  T I C K E T *** R A Y O N  * * *
             * *********************
             */
        }
    }
}




