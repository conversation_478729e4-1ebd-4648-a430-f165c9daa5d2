package com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.local.repository


import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.domaine.TypePrixUnitaireHT
import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.local.dao.TypePrixUnitaireHTDAO
import kotlinx.coroutines.flow.Flow


class TypePrixUnitaireHTLocalRepositoryImpl(
    private val typePrixUnitaireHTDAO: TypePrixUnitaireHTDAO
) : TypePrixUnitaireHTLocalRepository {
    override fun upsertAll(value: List<TypePrixUnitaireHT>) =
        typePrixUnitaireHTDAO.insertAll(value)

    override fun deleteAll() =
        typePrixUnitaireHTDAO.deleteAll()

    override fun getAll(): Flow<List<TypePrixUnitaireHT>> =
        typePrixUnitaireHTDAO.all

}