<?php

namespace App\Models;

class Couleur
{

    public $COU_Code;
    public $COU_Designation;
    public $COU_User;
    public $COU_Station;


    function __construct($COU_Code, $COU_Designation, $COU_User, $COU_Station)
    {
        $this->COU_Code = $COU_Code;
        $this->COU_Designation = $COU_Designation;
        $this->COU_User = $COU_User;
        $this->COU_Station = $COU_Station;
    }


    function setCOU_Code($COU_Code)
    {
        $this->COU_Code = $COU_Code;
    }

    function setCOU_Designation($COU_Designation)
    {
        $this->COU_Designation = $COU_Designation;
    }

    function setCOU_User($COU_User)
    {
        $this->COU_User = $COU_User;
    }

    function setCOU_Station($COU_Station)
    {
        $this->COU_Station = $COU_Station;
    }
}
