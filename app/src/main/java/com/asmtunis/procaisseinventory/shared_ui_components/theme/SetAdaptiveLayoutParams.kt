package com.asmtunis.procaisseinventory.shared_ui_components.theme

import android.app.Activity
import androidx.compose.material3.adaptive.currentWindowAdaptiveInfo
import androidx.compose.material3.windowsizeclass.ExperimentalMaterial3WindowSizeClassApi
import androidx.compose.material3.windowsizeclass.WindowHeightSizeClass
import androidx.compose.material3.windowsizeclass.WindowWidthSizeClass
import androidx.compose.material3.windowsizeclass.calculateWindowSizeClass
import androidx.compose.runtime.Composable
import androidx.window.layout.FoldingFeature
import com.asmtunis.procaisseinventory.core.utils.DevicePosture
import com.asmtunis.procaisseinventory.core.utils.ReplyContentType
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationContentPosition
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.core.utils.isBookPosture
import com.asmtunis.procaisseinventory.core.utils.isSeparating
import com.asmtunis.procaisseinventory.nav_components.data.model.UiWindowState
import com.google.accompanist.adaptive.calculateDisplayFeatures

@OptIn(ExperimentalMaterial3WindowSizeClassApi::class)
@Composable
fun SetAdaptiveLayoutParams(activity: Activity, onUiWindowStateChange: (UiWindowState) -> Unit) {
    val windowSize = calculateWindowSizeClass(activity)
    // you can use also
    // val windowAdaptiveInfo = currentWindowAdaptiveInfo()
    // val windowPosture = windowAdaptiveInfo.windowPosture
    val displayFeatures = calculateDisplayFeatures(activity)

    val windowAdaptiveInfo = currentWindowAdaptiveInfo()
    val windowPosture = windowAdaptiveInfo.windowPosture
    val windowSizeClass = windowAdaptiveInfo.windowSizeClass





    var uiWindowState = UiWindowState()
    /**
     * This will help us select type of navigation and content type depending on window size and
     * fold state of the device.
     */
    val navigationType: ReplyNavigationType
    val contentType: ReplyContentType

    /**
     * We are using display's folding features to map the device postures a fold is in.
     * In the state of folding device If it's half fold in BookPosture we want to avoid content
     * at the crease/hinge
     */
    val foldingFeature = displayFeatures.filterIsInstance<FoldingFeature>().firstOrNull()

    val foldingDevicePosture = when {
        isBookPosture(foldingFeature) ->
            DevicePosture.BookPosture(foldingFeature.bounds)

        isSeparating(foldingFeature) ->
            DevicePosture.Separating(foldingFeature.bounds, foldingFeature.orientation)

        else -> DevicePosture.NormalPosture
    }

    uiWindowState =
        when (windowSize.widthSizeClass) {
            WindowWidthSizeClass.Compact -> {
                uiWindowState.copy(
                    //  navigationType = ReplyNavigationType.BOTTOM_NAVIGATION,
                    navigationType = ReplyNavigationType.NAVIGATION_DRAWER,
                    contentType = ReplyContentType.SINGLE_PANE
                )
            }
            WindowWidthSizeClass.Medium -> {

                uiWindowState.copy(
                    navigationType = ReplyNavigationType.NAVIGATION_RAIL,
                    contentType = if (foldingDevicePosture != DevicePosture.NormalPosture) {
                        ReplyContentType.DUAL_PANE
                    } else {
                        ReplyContentType.SINGLE_PANE
                    }
                )
            }
            WindowWidthSizeClass.Expanded -> {



                uiWindowState.copy(
                    navigationType = if (foldingDevicePosture is DevicePosture.BookPosture) {
                        ReplyNavigationType.NAVIGATION_RAIL
                        // ReplyNavigationType.PERMANENT_NAVIGATION_DRAWER
                    } else {
                        ReplyNavigationType.PERMANENT_NAVIGATION_DRAWER
                    },
                    contentType = ReplyContentType.DUAL_PANE
                )
            }
            else -> {
                uiWindowState.copy(
                    //  navigationType = ReplyNavigationType.BOTTOM_NAVIGATION,
                    navigationType =  ReplyNavigationType.PERMANENT_NAVIGATION_DRAWER,
                    contentType = ReplyContentType.SINGLE_PANE
                )
            }
        }

    /**
     * Content inside Navigation Rail/Drawer can also be positioned at top, bottom or center for
     * ergonomics and reachability depending upon the height of the device.
     */
    val navigationContentPosition = when (windowSize.heightSizeClass) {
        WindowHeightSizeClass.Compact -> {
            ReplyNavigationContentPosition.TOP
        }
        WindowHeightSizeClass.Medium,
        WindowHeightSizeClass.Expanded -> {
            ReplyNavigationContentPosition.CENTER
        }
        else -> {
            ReplyNavigationContentPosition.TOP
        }
    }






    uiWindowState = uiWindowState.copy(
        windowSize = windowSize,
        displayFeatures = displayFeatures,
        foldingDevicePosture = foldingDevicePosture,
        navigationContentPosition = navigationContentPosition
    )

    onUiWindowStateChange(uiWindowState)
}
