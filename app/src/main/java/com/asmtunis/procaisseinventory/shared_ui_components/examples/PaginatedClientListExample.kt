package com.asmtunis.procaisseinventory.shared_ui_components.examples

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.paging.compose.collectAsLazyPagingItems
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.client.view_model.ClientViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PaginatedLazyColumn
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.RowClientList

/**
 * Example screen demonstrating how to use the new PaginatedLazyColumn
 * with the standardized pagination system.
 *
 * This example shows:
 * - How to collect paginated data from a ViewModel
 * - How to use PaginatedLazyColumn with proper error and loading states
 * - How to maintain existing UI components (RowClientList)
 * - How to handle pull-to-refresh functionality
 * - How to integrate with existing business logic
 */
@Composable
fun PaginatedClientListExample(
    modifier: Modifier = Modifier,
    clientViewModel: ClientViewModel = hiltViewModel(),
    onClientClick: (Client) -> Unit = {},
    onRefresh: (() -> Unit)? = null
) {
    // Collect paginated data using the new pagination system
    val paginatedClients = clientViewModel.paginatedClients.collectAsLazyPagingItems()
    val selectedClient by clientViewModel::selectedClient
    val listState = rememberLazyListState()

    Column(
        modifier = modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // Header
        Text(
            text = "Clients List",
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(16.dp)
        )

        // Paginated list with all the built-in features
        PaginatedLazyColumn(
            lazyPagingItems = paginatedClients,
            lazyListState = listState,
            pullToRefreshEnabled = true,
            onRefresh = onRefresh,
            key = { client -> client.id },

            // Custom empty state
            emptyStateContent = {
                Column(
                    modifier = Modifier.fillMaxSize(),
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "No clients found",
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.padding(16.dp)
                    )
                }
            },

            // Custom error state
            errorStateContent = { error, retry ->
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "Error loading clients",
                        style = MaterialTheme.typography.headlineSmall,
                        color = MaterialTheme.colorScheme.error
                    )

                    Text(
                        text = error.localizedMessage ?: "Unknown error",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(top = 8.dp)
                    )

                    androidx.compose.material3.Button(
                        onClick = retry,
                        modifier = Modifier.padding(top = 16.dp)
                    ) {
                        Text(stringResource(R.string.retry))
                    }
                }
            }
        ) { client ->
            // Use existing UI component - no changes needed!
            RowClientList(
                selectedClient = selectedClient,
                client = client,
                onClick = { onClientClick(client) }
            )
        }
    }
}

/**
 * Example of how to integrate pagination with search and filtering.
 * This shows how the existing filtering logic can be adapted to work
 * with the new pagination system.
 */
@Composable
fun PaginatedClientListWithFilters(
    modifier: Modifier = Modifier,
    clientViewModel: ClientViewModel = hiltViewModel(),
    searchQuery: String = "",
    sortBy: String = "CLI_NomPren",
    isAscending: Boolean = false,
    filterBySold: Int = 2,
    filterByType: String = "",
    filterByClientEtat: String = "",
    onClientClick: (Client) -> Unit = {},
    onRefresh: (() -> Unit)? = null
) {
    // Update pagination parameters when filters change
    clientViewModel.updatePaginationParams(
        searchText = searchQuery,
        sortBy = sortBy,
        isAsc = if (isAscending) 1 else 2,
        filterBySold = filterBySold,
        filterByType = filterByType,
        filterByClientEtat = filterByClientEtat
    )

    // The rest is the same as the basic example
    PaginatedClientListExample(
        modifier = modifier,
        clientViewModel = clientViewModel,
        onClientClick = onClientClick,
        onRefresh = onRefresh
    )
}

/**
 * Migration guide for existing screens:
 *
 * 1. Replace PullToRefreshLazyColumn with PaginatedLazyColumn
 * 2. Change data source from Flow<List<T>> to Flow<PagingData<T>>
 * 3. Use collectAsLazyPagingItems() instead of collectAsState()
 * 4. Update ViewModel to use paginated repository methods
 * 5. Keep existing UI components and business logic unchanged
 *
 * Benefits:
 * - Automatic loading states and error handling
 * - Better performance with large datasets
 * - Consistent pagination behavior across the app
 * - Built-in pull-to-refresh support
 * - Smooth scrolling with lazy loading
 * - Memory efficient data loading
 */
