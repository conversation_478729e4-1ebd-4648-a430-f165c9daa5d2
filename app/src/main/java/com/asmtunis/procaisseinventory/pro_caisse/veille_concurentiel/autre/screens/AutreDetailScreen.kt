package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.screens

import android.Manifest
import android.app.Activity
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.twotone.Send
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.twotone.Edit
import androidx.compose.material3.Button
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import androidx.hilt.navigation.compose.hiltViewModel
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.MainImageTiketRoute
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain.AutreVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ConcurrentVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.TypeCommunicationVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.text_validation.VcFormEvent
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.text_validation.VcTextValidationViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.AskPermission
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.CameraViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.image_pager.HorizontalImagePager
import com.asmtunis.procaisseinventory.shared_ui_components.image_pager.VerticalImagePager
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu
import com.simapps.ui_kit.edit_text.EditTextField


@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun AutreDetailScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    settingViewModel: SettingViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    dataViewModel: DataViewModel,
    mainViewModel: MainViewModel,
    cameraViewModel: CameraViewModel,
    networkViewModel: NetworkViewModel,
    vcTextValidationViewModel: VcTextValidationViewModel = hiltViewModel(),
    proCaisseViewModels: ProCaisseViewModels
) {
    val vcViewModel = proCaisseViewModels.veilleConcurentielViewModel
    val autreViewModel = proCaisseViewModels.autreViewModel

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

   val validationAddVcEvents = vcTextValidationViewModel.validationAddVcEvents

    val state = vcTextValidationViewModel.stateAddNewVc
    val context = LocalContext.current
    val concurentList = mainViewModel.listConcurentVC
    val imageUriList = cameraViewModel.imageUriList
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val codeM = mainViewModel.codeM
    val imageCodeM = mainViewModel.imageCodeM
    rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartIntentSenderForResult()
    ) { activityResult ->
        if (activityResult.resultCode == Activity.RESULT_OK) {
            Log.d("appDebug", "Accepted")
        } else {
            Log.d("appDebug", "Denied")
        }
    }

    val scrollState = rememberScrollState()

    val autre = autreViewModel.selectedAutre.autreVC?: AutreVC()


    val canModify = autreViewModel.modify
    val listImgeUri = cameraViewModel.listImgeUri
   val utilisateur = mainViewModel.utilisateur

   val typeCommunicationVCList = vcViewModel.typeCommunicationVCList



    LaunchedEffect(key1 = validationAddVcEvents) {


        autreViewModel.handleAddVisiteEvents(
            validationAddVcEvents = validationAddVcEvents,
            popBackStack = { popBackStack() },
            utilisateur = utilisateur,
            codeM = codeM,
            saveImageList = {
                vcViewModel.saveImageList(
                    context = context,
                    userID = utilisateur.codeUt,
                    imageCodeM = imageCodeM,
                    vcCodeM = codeM,
                    imageUriList = imageUriList,
                )
            }
        )
    }


        LaunchedEffect(key1 = Unit) {

        if(canModify) return@LaunchedEffect

        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.produitChanged(autre.autre))


       vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.autreNoteChanged(autre.autreNote))

        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.noteChanged(autre.noteOp))


        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.fournisseurChanged(concurentList.firstOrNull { it.codeconcurrent == autre.codeConcur }?: ConcurrentVC()))
        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.typeCommunicationChanged(typeCommunicationVCList.firstOrNull { it.codeTypeCom == autre.codeTypeCom }?: TypeCommunicationVC()))

    }




    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = { popBackStack() },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title =

                if (autre.status == ItemStatus.DELETED.status) {
                    stringResource(id = R.string.deleted_autre, autre.codeAutre)
                } else {
                    if (autre.codeAutre != "") {
                        if (!canModify) { autre.codeAutre
                        } else stringResource(id = R.string.modification, autre.codeAutre)
                    } else stringResource(id = R.string.new_others)
                },
            )
        },
        //    containerColor = colorResource(id = R.color.black),
        floatingActionButton = {
            FloatingActionButton(
                onClick = {
                if (autre.status != ItemStatus.DELETED.status) {
                    if (!canModify) {
                        autreViewModel.onModifyChange(true)
                    } else {
                        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.SubmitAddAutreVc)
                    }
                } else {

                    showToast(
                        context = context,
                        toaster = toaster,
                        message = context.resources.getString(R.string.deja_supprime),
                        type =  ToastType.Info,
                    )
                }

            }) {
                Icon(
                    imageVector = if (canModify) Icons.AutoMirrored.TwoTone.Send
                    else Icons.TwoTone.Edit,
                    contentDescription = stringResource(id = R.string.cd_addVisite_button)
                )
            }
        }
    ) { padding ->

        Column(
            modifier = Modifier
                .padding(padding)
                .verticalScroll(scrollState)
                .fillMaxWidth()
                .wrapContentHeight()
                .wrapContentSize(Alignment.Center),
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            EditTextField(
                text = state.produit,
                errorValue = state.produitError?.asString(),
                label = stringResource(R.string.others),
                onValueChange = { vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.produitChanged(it)) },
                readOnly = !canModify,
                enabled = true,
                leadingIcon = Icons.Default.Home,
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Next
            )



            EditTextField(
                text = state.autreNote,
                errorValue = state.autreNoteError?.asString(),
                label = stringResource(R.string.autre_note),
                onValueChange = {
                    vcTextValidationViewModel.onAddNewVcEvent(
                        VcFormEvent.autreNoteChanged(
                            it
                        )
                    )
                },
                readOnly = !canModify,
                enabled = true,
                leadingIcon = Icons.Default.Home,
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Next
            )


            EditTextField(
                text = state.note,
                errorValue = state.noteError?.asString(),
                label = stringResource(R.string.note_field),
                onValueChange = {
                    vcTextValidationViewModel.onAddNewVcEvent(
                        VcFormEvent.noteChanged(
                            it
                        )
                    )
                },
                readOnly = !canModify,
                enabled = true,
                leadingIcon = Icons.Default.Home,
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Next
            )

            Spacer(modifier = Modifier.height(9.dp))

            GenericDropdownMenu(
                modifier = Modifier.fillMaxWidth(0.95f),
                designation =  state.typeCommunication.typeCommunication,
                errorValue = state.typeCommunicationError?.asString(),
                label = stringResource(R.string.type_communication),
                readOnly =  canModify,
                selectedItem = state.typeCommunication,
                getItemDesignation = { it.typeCommunication },
                itemList = typeCommunicationVCList,
                itemExpanded = vcViewModel.typeCommunicationExpanded,
                getItemTrailing = { it.codeTypeCom },
                onClick = {
                    vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.typeCommunicationChanged(it))
                    vcViewModel.onTypeCommunicationExpandedChange(false)
                },
                onItemExpandedChange = { vcViewModel.onTypeCommunicationExpandedChange(it) },
                lottieAnimEmpty = { LottieAnim(lotti = R.raw.emptystate) },
                lottieAnimError = { LottieAnim(lotti = R.raw.connection_error, size = it) }
            )


            Spacer(modifier = Modifier.height(9.dp))

            GenericDropdownMenu(
                modifier = Modifier.fillMaxWidth(0.95f),
                designation =  state.concurrent.concurrent,
                errorValue = state.concurrentError?.asString(),
                label = stringResource(R.string.concurrent),
                readOnly =  canModify,
                getItemDesignation = { it.concurrent },
                itemExpanded = vcViewModel.fournisseurExpanded,
                itemList = concurentList,
                getItemTrailing = { it.codeconcurrent },
                selectedItem = state.concurrent,
                onClick = {
                    vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.fournisseurChanged(it))
                    vcViewModel.onFournisseurExpandedChange(false)
                },
                onItemExpandedChange = { vcViewModel.onFournisseurExpandedChange(it) },
                lottieAnimEmpty = { LottieAnim(lotti = R.raw.emptystate) },
                lottieAnimError = { LottieAnim(lotti = R.raw.connection_error, size = it) }
            )

            Spacer(modifier = Modifier.height(12.dp))
            HorizontalImagePager(
                onClicks = { cameraViewModel.onOpenVerticalalImagePagerDialogChange(true) },
                canModify = canModify,
                cameraViewModel = cameraViewModel,

                imageList = listImgeUri,
                onDeleteClick = {
                    vcViewModel.onImageDeleted(it)

                    if(it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                }
            )

            if (cameraViewModel.openVerticalalImagePagerDialog && listImgeUri.isNotEmpty()) {
                VerticalImagePager(
                    canModify = canModify,
                    onDismissRequest = {
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(false)
                    },
                    imageList =  listImgeUri,
                    onDeleteClick = {
                     vcViewModel.onImageDeleted(it)
                        if(it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                    }
                )
            }
            /*HorizontalPagerIndicator(
                pagerState = pagerState,
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(16.dp),
            )*/










            if (canModify)
                AskPermission(
                permission = listOf(
                    Manifest.permission.CAMERA
                ),
                permissionNotAvailableContent = { permissionState ->
                    Column(
                        modifier = Modifier
                            //  .background(colorResource(id = R.color.black))
                            .fillMaxSize(),
                        // .padding(padding),
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        LottieAnim(lotti = R.raw.emptystate)
                        Spacer(modifier = Modifier.height(16.dp))

                        val textToShow = if (permissionState.shouldShowRationale) {
                            stringResource(R.string.access_camera_request_permession)
                        } else {
                            stringResource(R.string.camera_not_available)
                        }
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(textToShow)
                        Spacer(modifier = Modifier.height(8.dp))
                        Button(onClick = {
                            permissionState.launchMultiplePermissionRequest()
                        }) {
                            Text(stringResource(R.string.request_camera_auth))
                        }
                    }
                },
                content = {
                    Spacer(modifier = Modifier.height(12.dp))
                    Button(
                        modifier = Modifier.wrapContentWidth(),
                        onClick = {
                            cameraViewModel.onNumChange(value = imageCodeM)
                            navigate(MainImageTiketRoute)

                        }
                    ) {

                        Text(text = if (cameraViewModel.imageUriList.isNotEmpty()) context.getString(R.string.prendreautrephotos)
                        else context.getString(R.string.prendrephotos))
                    }

                    Spacer(modifier = Modifier.height(16.dp))
                }
            )
        }
    }
}











