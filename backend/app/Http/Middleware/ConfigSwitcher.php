<?php

namespace App\Http\Middleware;


use App\Models\Config;
use App\Traits\ApiResponser;
use Closure;
use Exception;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config as DBConfig;
use Illuminate\Support\Facades\DB;

class ConfigSwitcher
{
    use ApiResponser;

    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if ($request->hasHeader('Language')) {
            App::setLocale($request->header('Language'));
        }

        if ($request->hasHeader('Application-name')) {
            //     DBConfig::set("mobile.default", $request->header('Version-code'));
        }

        if ($request->hasHeader('Version-code')) {
            //     DBConfig::set("mobile.default", $request->header('Version-code'));
        }

        if ($request->hasHeader('Version-name')) {
            // DBConfig::set("mobile.default", $request->header('Version-code'));
        }

        if (App::environment('production')) {
            if ($request->hasHeader('Device-id')) {
                DBConfig::set("mobile.deviceId", $request->header('Device-id'));
            } else {

            }
        }


        if ($request->hasHeader('Key')) {
            //DBConfig::set("mobile.default", $request->header('key'));
            DBConfig::set("mobile.key", $request->header('Key'));
        }

        if (!env('DB_FROM_ENV') && $request->hasHeader('Baseconfig')) {
            $this->changeBaseConfig($request->header('Baseconfig'));
        } else {
            DBConfig::set("database.default", 'sqlsrv');
        }

        $this->checkConnectivity();

        return $next($request);
    }

    public function changeBaseConfig(string $config)
    {
        try {
            $config =
                (new Config())->forceFill(json_decode(base64_decode($config), true));
            //first get default connection array and use in new variable for new connection which will create dynamically.(default connection is defined in database.php config file)
            $dbConfig = config('database.connections.sqlsrv');
            DBConfig::set("mobile.baseConfig", $config);
            //now use database name which is in user record;
            $dbConfig['host'] = $config->dbIpAddress;
            $dbConfig['database'] = $config->dbName;
            $dbConfig['username'] = $config->username;
            $dbConfig['password'] = $config->password;
            //now set a new database connection name is db in my case
            DBConfig::set("database.connections.db", $dbConfig);
            //now set default connection which is created for the user
            DBConfig::set("database.default", 'db');
            //now there are two connection one for master (sqlsrv) and other for user(db) and default connection is (db)
            //we can access these two connection in every models by using $connection as mentioned in Laravel documentation.
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage(), null, -1, $e->getCode());
        }
    }

    public function checkConnectivity()
    {
        try {
            DB::connection()->getPdo();
        } catch (Exception $e) {
            return $this->errorResponse("Could not connect to the database.  Please check your configuration. error:" . $e, null, -1, $e->getCode());
        }
    }

}
