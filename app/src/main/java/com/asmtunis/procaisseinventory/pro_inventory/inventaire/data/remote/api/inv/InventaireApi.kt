package com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.remote.api.inv

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_inventory.data.NestedItem
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.Inventaire
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire
import kotlinx.coroutines.flow.Flow


interface InventaireApi {

        companion object {
          // var URL_ADD_INVENTAIRE = "${Globals.BASE_URL}/Inventaire/addBatchInventaires"
        }

        suspend fun getInventaires(baseConfig: String): Flow<DataResult<List<Inventaire>>>
     //   suspend fun addBatchInventaires(baseConfig: String): List<Inventaire>?
        suspend fun addBatchInventairesWithLines(baseConfig: String): Flow<DataResult<List<NestedItem<Inventaire, List<LigneInventaire>>>>>

}