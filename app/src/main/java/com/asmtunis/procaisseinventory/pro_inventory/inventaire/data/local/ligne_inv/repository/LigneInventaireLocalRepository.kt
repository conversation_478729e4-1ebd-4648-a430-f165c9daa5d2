package com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.ligne_inv.repository

import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire
import kotlinx.coroutines.flow.Flow

interface LigneInventaireLocalRepository {
    fun setToInserted(codeM: String)
    fun upsertAll(value: List<LigneInventaire>)
    fun upsert(value: LigneInventaire)
    fun updateStatus(code: String, codeLocal : String, etat: String)
    fun getByCode(code: String): Flow<List<LigneInventaire>>
    fun deleteAll()
    fun deleteList(ligneInventaireList: List<LigneInventaire>)
    fun deleteByCodeAndCodeArticle(num: String, codeArticle : String)
    fun getAll(): Flow<List<LigneInventaire>>
}