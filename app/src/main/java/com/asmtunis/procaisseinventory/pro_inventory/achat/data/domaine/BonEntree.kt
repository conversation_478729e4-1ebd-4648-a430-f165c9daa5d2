package com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


@Entity(tableName = ProInventoryConstants.BON_ENTREE_TABLE/*, primaryKeys = ["BON_ENT_Num", "BON_ENT_Exer"]*/)
@Serializable
data class BonEntree(
    @PrimaryKey(autoGenerate = true)
    @Transient
    val id: Long = 0,

    @SerialName("BON_ENT_Num")
    @ColumnInfo(name = "BON_ENT_Num")
    var bONENTNum: String = "",

    @SerialName("BON_ENT_Num_M")
    @ColumnInfo(name = "BON_ENT_Num_M")
    val bONENTNumM: String = "",

    @SerialName("BON_ENT_Exer")
    @ColumnInfo(name = "BON_ENT_Exer")
    var bONENTExer: String = "",
    @SerialName("BON_ENT_Date")
    @ColumnInfo(name = "BON_ENT_Date")
    var bONENTDate: String = "",
    @SerialName("BON_ENT_StationEntree")
    @ColumnInfo(name = "BON_ENT_StationEntree")
    var bONENTStationEntree: String = "",
    @SerialName("BON_ENT_CodeFrs")
    @ColumnInfo(name = "BON_ENT_CodeFrs")
    var bONENTCodeFrs: String = "",
    @SerialName("BON_ENT_User")
    @ColumnInfo(name = "BON_ENT_User")
    var bONENTUser: String = "",


    @ColumnInfo(name = "BON_ENT_Type")
    @SerialName("BON_ENT_Type")
    var bONENTType :String = "",

    @ColumnInfo(name = "BON_ENT_SYNC")
    @SerialName("BON_ENT_SYNC")
    var bONENTSYNC: String? = null,

    @ColumnInfo(name = "BON_ENT_NumPiece")
    @SerialName("BON_ENT_NumPiece")
    var bONENTNumPiece: String? = null,

    @ColumnInfo(name = "BON_ENT_TypePiece")
    @SerialName("BON_ENT_TypePiece")
    var bONENTTypePiece: String? = null,

    @ColumnInfo(name = "timestamp")
    var timestamp: Long = 0,



  
    @SerialName("BON_ENT_DDm")
    val bONENTDDm: String? = "",
  
    @SerialName("BON_ENT_Devise")
    val bONENTDevise: String? = "",

    @SerialName("BON_ENT_EN_TTC")
    val bONENTENTTC: String = "",

    @SerialName("BON_ENT_ETAT")
    val bONENTETAT: String = "",
    
    @SerialName("BON_ENT_export")
    val bONENTExport: String = "",

    @SerialName("BON_ENT_Fact")
    val bONENTFact: String? = "",

    @SerialName("BON_ENT_FraisTansport")
    val bONENTFraisTansport: String = "",

    @SerialName("BON_ENT_MntDC")
    val bONENTMntDC: String = "",
    @SerialName("BON_ENT_MntFodec")
    val bONENTMntFodec: String = "",

    @SerialName("BON_ENT_MntHT")
    val bONENTMntHT: String = "",

    @SerialName("BON_ENT_MntNetHt")
    val bONENTMntNetHt: String = "",

    @SerialName("BON_ENT_MntTTC")
    val bONENTMntTTC: String = "",

    @SerialName("BON_ENT_MntTva")
    val bONENTMntTva: String = "",

    @SerialName("BON_ENT_Mont")
    val bONENTMont: String? = "",
 

    
    @SerialName("BON_ENT_Reg")
    val bONENTReg: String? = "",
    
    @SerialName("BON_ENT_Regler")
    val bONENTRegler: String = "",
    
    @SerialName("BON_ENT_Remise")
    val bONENTRemise: String = "",
    
    @SerialName("BON_ENT_Station")
    val bONENTStation: String? = "",
 
    @SerialName("BON_ENT_TauxEchange")
    val bONENTTauxEchange: String = "",

    @SerialName("BON_ENT_TauxRemise")
    val bONENTTauxRemise: String = "",

    @SerialName("DDmM")
    val dDmM: String? = "",
    
    @SerialName("ddm")
    val ddm: String? = "",
    
    @SerialName("DocEtat")
    val docEtat: String? = "",
    
    @SerialName("export")
    val export: String = "",
    
    @SerialName("exportM")
    val exportM: String? = "",
    
    @SerialName("Num_BC")
    val numBC: String? = "",
    
    @SerialName("Num_BEMobile")
    val numBEMobile: String? = ""

): BaseModel() {
    @Ignore
    val bONENTDateFormatted = this.bONENTDate.substringBefore(".")
}


   

    