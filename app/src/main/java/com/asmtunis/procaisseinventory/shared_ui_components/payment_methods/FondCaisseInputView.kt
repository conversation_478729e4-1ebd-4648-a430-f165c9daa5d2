package com.asmtunis.procaisseinventory.shared_ui_components.payment_methods

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.simapps.ui_kit.edit_text.EditTextField

@Composable
fun FondCaisseInputView(
    onDismissRequest: () -> Unit,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit,
    onValueChange: (String) -> Unit,
    text : String
) {
    Dialog(
        onDismissRequest = {
            // Dismiss the dialog when the user clicks outside the dialog or on the back
            // button. If you want to disable that functionality, simply use an empty
            // onDismissRequest.
            onDismissRequest()

        },
        properties = DialogProperties(
            usePlatformDefaultWidth = true
        ),
        content = {
            Card(
                elevation = CardDefaults.cardElevation(),
                shape = RoundedCornerShape(15.dp),

            ) {
                Column(
                    verticalArrangement = Arrangement.Top,
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.padding(top = 16.dp)
                        //.verticalScroll(scrollState)
                       // .background(color = MaterialTheme.colorScheme.background)
                    // .wrapContentSize()

                ) {




                    EditTextField(
                        modifier = Modifier.fillMaxWidth(0.8f),
                        showLeadingIcon = false,
                        requestFocus = true,
                        text = text,
                        errorValue = null,
                        label = stringResource(R.string.fonds_de_caisse),
                        onValueChange = {
                            onValueChange(it)
                        },
                        readOnly = false,
                        leadingIcon = Icons.Default.Person,
                        keyboardType = KeyboardType.Decimal,
                        imeAction = if(text.isNotEmpty() && stringToDouble(text)>0) ImeAction.Done else ImeAction.None,
                        onKeyboardActions = {
                            if(text.isNotEmpty() && stringToDouble(text)>0)
                            onConfirm()
                        }
                    )
                Spacer(modifier = Modifier.height(16.dp))

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.spacedBy(30.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        OutlinedButton(
                            onClick = {
                                onDismiss()
                            },
                            /* colors = ButtonDefaults.buttonColors(
                                 backgroundColor = orange,
                                 contentColor = white
                             ),*/
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f),
                            shape = CircleShape
                        ) {
                            Text(
                                text = "Cancel",
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.Bold,
                                textAlign = TextAlign.Center,
                            )
                        }
                        Button(
                            enabled = text.isNotEmpty() && stringToDouble(text)>0,
                            onClick = {

                                onConfirm()
                            },
                            /*  colors = ButtonDefaults.buttonColors(
                                  backgroundColor = orange,
                                  contentColor = white
                              ),*/
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f),
                            shape = CircleShape
                        ) {
                            Text(
                                text = "Confirm",
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.Bold,
                                textAlign = TextAlign.Center,
                            )
                        }
                    }
                }
            }

        }
    )
}