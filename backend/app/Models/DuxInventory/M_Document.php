<?php

namespace App\Models\DuxInventory;


use App\Enums\ClasseDocument;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class M_Document extends Model
{
    protected $connection = 'onthefly';
    protected $table = 'M_Document';
    public $incrementing = false;
    public $timestamps = false;
    protected $guarded = [];

    public static function defaultFields()
    {
        return [
            'id', 'dateDocument', 'code', 'codePiece', 'idEtatDocument', 'Id_Technicien', 'concratisePar',
            'idRepresentant', 'id_Apporteur', 'idDevise', 'tauxChange', 'idStation', 'isTierexonoTva',
            'idTier', 'nomPrenomTier', 'adresseTier', 'telTier', 'mntBrutht', 'mntNetht', 'mntRemise',
            'mntTtc', 'mntTva', 'mntht', 'tauxRemise'
        ];
    }

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($model) {
            $model->{$model->getKeyName()} = BaseModel::getID();
            $model->fillAdditionalAttributes();
        });
    }

    public function lignes()
    {
        return $this->hasMany(L_LigneDocument::class, 'idDocument');
    }

    public function etatDocument()
    {
        return $this->belongsTo(E_EtatDocument::class, 'idEtatDocument');
    }

    protected function fillAdditionalAttributes()
    {
        // Fetch related models using validated data
        $DataTier = P_tier::findOrFail($this->idTier);
        $dataUser = P_Utilisateur::findOrFail($this->j_codeUser);
        $Exercice = P_Exercice::where('active', '1')->firstOrFail();
        $DataClassDoc = P_ClasseDocument::findOrFail($this->idClasseDocument);
        $DataRgime = P_Regime::findOrFail($DataTier->idRegime);
        $dataStation = P_Station::findOrFail($this->idStation);
        $DataRegimeStation = P_Regime::findOrFail($dataStation->idRegime);

        // Set document class-related attributes
        $this->idClasseDocument = $DataClassDoc->id;
        $this->codeClasseDocument = $DataClassDoc->code;
        $this->libelleClasseDocument = $DataClassDoc->libelle;
        $this->isReservation = $DataClassDoc->isReservation;
        $this->titre = $DataClassDoc->titre;
        $this->titreImprimable = $DataClassDoc->titreImprimable;
        $this->affecteStock = $DataClassDoc->affecteStock;
        $this->affecteStockFils = $DataClassDoc->affecteStockFils;
        $this->affecteSolde = $DataClassDoc->affecteSolde;
        $this->affectecmp = $DataClassDoc->affectecmp;
        $this->isInput = $DataClassDoc->isInput;
        $this->isOutput = $DataClassDoc->isOutput;
        $this->isAchat = $DataClassDoc->isAchat;
        $this->isVente = $DataClassDoc->isVente;
        $this->haveCharges = $DataClassDoc->haveCharges;
        $this->idTypeCalcul = $DataClassDoc->idTypeCalcul;
        $this->codeTypeCalcul = $DataClassDoc->codeTypeCalcul;
        $this->libelleTypeCalcul = $DataClassDoc->libelleTypeCalcul;
        $this->isTvaConsider = $DataClassDoc->isTvaConsider;
        $this->isReglable = $DataClassDoc->isReglable;
        $this->isFacturable = $DataClassDoc->isFacturable;
        $this->isFacture = $DataClassDoc->isFacture;
        $this->isAvoir = $DataClassDoc->isAvoir;
        $this->isRep_Dachat = $DataClassDoc->isRep_Dachat;
        $this->fusionLigne = $DataClassDoc->fusionLigne;
        $this->decrementeStock = $DataClassDoc->decrementeStock;
        $this->incrementeStock = $DataClassDoc->incrementeStock;
        $this->incrementeStockFils = $DataClassDoc->incrementeStockFils;
        $this->decrementeStockFils = $DataClassDoc->decrementeStockFils;
        $this->incrementeSolde = $DataClassDoc->incrementeSolde;
        $this->decrementeSolde = $DataClassDoc->decrementeSolde;
        $this->useDestination = $DataClassDoc->useDestination;

        // Set tier-related attributes
        $this->codeTier = $DataTier->code;
        $this->nomPrenomTier = $DataTier->nomPrenom;
        $this->adresseTier = $DataTier->adresse;
        $this->idRegion = $DataTier->idRegion;
        $this->idSecteur = $DataTier->idSecteur;
        $this->telTier = $DataTier->tel;

        // Set regime-related attributes for tier
        $this->idRegimeTier = $DataRgime->id;
        $this->codeRegimeTier = $DataRgime->code;
        $this->libelleRegimeTier = $DataRgime->libelle;
        $this->isTierForfaitaire = $DataRgime->isForfetaire;

        // Set regime-related attributes for the station
        $this->idRegime = $DataRegimeStation->id;
        $this->codeRegime = $DataRegimeStation->code;
        $this->libelleRegime = $DataRegimeStation->libelle;
        $this->isForfaitaire = $DataRegimeStation->isForfetaire;
        $this->isexonoTva = $DataRegimeStation->isexonoTva;

        // Set additional fields
        $this->P_codeStation = $dataUser->code;
        $this->P_codeExercice = $Exercice->code;
        $this->j_operation = 'Insertion';
        $this->j_export = '0';
        $this->mntRecu = '0';
        $this->isAvoirNotification = '0';
        $this->isRegler = '0';
        $this->etattrans = 'Non Transformé';
        $this->IdEtattransformation = '1';
        $this->datecreation = Carbon::now()->format("Y-m-d H:i:s");
        $this->j_ddm = Carbon::now()->format("Y-m-d H:i:s");
    }

}
