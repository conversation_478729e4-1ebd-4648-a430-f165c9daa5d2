package com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.remote.api.inv

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_inventory.data.NestedItem
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.Inventaire
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class InventaireApiImpl(private val client: HttpClient) : InventaireApi {
    override suspend fun getInventaires(baseConfig: String): Flow<DataResult<List<Inventaire>>> = flow {
        val result = executePostApiCall<List<Inventaire>>(
            client = client,
            endpoint = Urls.GET_INVENTAIRES,
            baseConfig = baseConfig,
        )

        emitAll(result)
    }

    override suspend fun addBatchInventairesWithLines(baseConfig: String): Flow<DataResult<List<NestedItem<Inventaire, List<LigneInventaire>>>>> = flow {
        val result = executePostApiCall<List<NestedItem<Inventaire, List<LigneInventaire>>>>(
            client = client,
            endpoint = Urls.ADD_BATCH_INVENTAIRES_WITH_LINES,
            baseConfig = baseConfig,
        )

        emitAll(result)
    }
    }