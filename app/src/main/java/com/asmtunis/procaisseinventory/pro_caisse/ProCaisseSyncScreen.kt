package com.asmtunis.procaisseinventory.pro_caisse

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardReturn
import androidx.compose.material.icons.filled.CloudSync
import androidx.compose.material3.Button
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.utils.Sync.getProCaisseTotalNoSyncCount
import com.asmtunis.procaisseinventory.core.utils.Sync.getSharedTotalNoSyncCount
import com.asmtunis.procaisseinventory.core.utils.Sync.isSyncInProCaisse
import com.asmtunis.procaisseinventory.core.utils.Sync.syncAllProCaisse
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentCheck
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.SharedSyncView
import com.asmtunis.procaisseinventory.shared_ui_components.buttons.SyncButtons
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import kotlinx.coroutines.FlowPreview

@OptIn(FlowPreview::class)
@Composable
fun ProCaisseSyncScreen(
    navigate: (route: String) -> Unit,
    navigateUp: () -> Unit,
    dataViewModel: DataViewModel,
    mainViewModel: MainViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    networkViewModel: NetworkViewModel
) {
    val syncClientViewModel = syncProcaisseViewModels.syncClientViewModel
    val syncDistNumViewModel = syncProcaisseViewModels.syncDistNumViewModel
    val syncVcViewModel = syncProcaisseViewModels.syncVcViewModel
    val syncReglementViewModel = syncProcaisseViewModels.syncReglementViewModel
    val syncBonRetourViewModel = syncProcaisseViewModels.syncBonRetourViewModel
    val syncBonCommandeViewModel = syncProcaisseViewModels.syncBonCommandeViewModel
    val syncBonLivraisonVM = syncProcaisseViewModels.syncBonLivraisonVM
    val syncInvBatimentViewModel = syncProcaisseViewModels.syncInvBatimentViewModel
    val syncInvPatrimoineViewModel = syncProcaisseViewModels.syncInvPatrimoineViewModel
    val syncTourneeViewModel = syncProcaisseViewModels.syncTourneeViewModel

    val proCaisseAuthorization = getProCaisseDataViewModel.authorizationList

    val haveAutoFactureAuthorisation =
        proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.AUTO_FACTURE }


    val listActifTimber = mainViewModel.listActifTimber

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val noSyncCount =
        getProCaisseTotalNoSyncCount(
            syncProcaisseViewModels = syncProcaisseViewModels,
        )+ getSharedTotalNoSyncCount(
            syncSharedViewModels = syncSharedViewModels,
        )
    val isConnected = networkViewModel.isConnected

    Scaffold(
        topBar = {
            AppBar(
                onNavigationClick = {},
                showNavIcon = false,
                title = stringResource(id = R.string.sync_title),
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected
            )
        },
    ) { padding ->
        Column(
            modifier =
                Modifier
                    .padding(padding)
                    .verticalScroll(rememberScrollState())
                    //  .background(MaterialTheme.colorScheme.background)
                    .fillMaxSize(),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Button(
                enabled =
                    isConnected &&
                            !isSyncInProCaisse(
                                syncProcaisseViewModels = syncProcaisseViewModels,
                                syncSharedViewModels = syncSharedViewModels
                            ),
                onClick = {
                    if (noSyncCount > 0) {
                        syncAllProCaisse(
                            syncProcaisseViewModels = syncProcaisseViewModels
                        )
                    } else {
                        navigateUp()
                    }
                },
                modifier = Modifier,
                shape = MaterialTheme.shapes.medium,
            ) {
                if (!isConnected) LottieAnim(lotti = R.raw.no_connection, size = 25.dp)
                // Text(text = if (noSyncCount > 0) "Sync ALL" else "Exit")
                Icon(
                    imageVector = if (noSyncCount > 0) Icons.Default.CloudSync else Icons.AutoMirrored.Filled.KeyboardReturn,
                    contentDescription = stringResource(id = R.string.cd_favorite_button),
                )
                if (isSyncInProCaisse(
                        syncProcaisseViewModels = syncProcaisseViewModels,
                        syncSharedViewModels = syncSharedViewModels
                    )
                ) {
                    LottieAnim(lotti = R.raw.loading, size = 60.dp)
                }
            }

            if (noSyncCount == 0) LottieAnim(lotti = R.raw.emptystate, size = 250.dp)

            SharedSyncView (
                isConnected = isConnected,
                syncSharedViewModels = syncSharedViewModels,
            )
            /**
             * *********************
             *    CLIENT
             * *********************
             */
            SyncButtons(
                text =  "Sync Client ",
                nbrNotSync = syncClientViewModel.clientsNotSync.size,
                remoteResponseState = syncClientViewModel.clientsState,
                emailBody = syncClientViewModel.notSyncClientObj,
                isConnected = isConnected,
                onClickSync = { syncClientViewModel.syncClients(syncClientViewModel.clientsNotSync) }

            )


            /**
             * *********************
             *    CLIENT
             * *********************
             */

            /**
             * *********************
             *    DISTRIBUTION NUM
             * *********************
             */
            SyncButtons(
                text =  "Sync visite to delete ",
                nbrNotSync = syncDistNumViewModel.visiteDnNotSyncToDelete.size,
                remoteResponseState = syncDistNumViewModel.responseDeleteBatchVisiteDnState,
                emailBody = syncDistNumViewModel.notSyncVisiteDnToDeleteObj,
                isConnected = isConnected,
                onClickSync = { syncDistNumViewModel.syncVisitesDnToDelete(syncDistNumViewModel.visiteDnNotSyncToDelete) }

            )


            SyncButtons(
                text =  "Sync DN ",
                nbrNotSync = syncDistNumViewModel.visiteDnNotSync.size,
                remoteResponseState = syncDistNumViewModel.responseAddBatchVisiteDnState,
                emailBody = syncDistNumViewModel.notSyncVisiteDnObj,
                isConnected = isConnected,
                onClickSync = { syncDistNumViewModel.syncVisitesDn(syncDistNumViewModel.visiteDnNotSync) }

            )


            /**
             * *********************
             *    DISTRIBUTION NUM
             * *********************
             */

            /**
             * *********************
             *    VC
             * *********************
             */

            SyncButtons(
                text =  "Sync image ",
                nbrNotSync = syncVcViewModel.imagesNotSync.size,
                remoteResponseState = syncVcViewModel.imageState,
                emailBody = syncVcViewModel.notSyncImageObj,
                isConnected = isConnected,
                onClickSync = { syncVcViewModel.syncImages(syncVcViewModel.imagesNotSync) }

            )


            SyncButtons(
                text =  "Sync promo vc ",
                nbrNotSync = syncVcViewModel.promoVcNotSync.size,
                remoteResponseState = syncVcViewModel.promoState,
                emailBody = syncVcViewModel.notSyncPromoVcObj,
                isConnected = isConnected,
                onClickSync = { syncVcViewModel.syncPromoVc(syncVcViewModel.promoVcNotSync) }

            )


            SyncButtons(
                text =  "Sync prix vc ",
                nbrNotSync = syncVcViewModel.prixVcNotSync.size,
                remoteResponseState = syncVcViewModel.prixState,
                emailBody = syncVcViewModel.notSyncPrixVcObj,
                isConnected = isConnected,
                onClickSync = { syncVcViewModel.syncPrixVc(syncVcViewModel.prixVcNotSync) }

            )


            SyncButtons(
                text =  "Sync Autre vc ",
                nbrNotSync = syncVcViewModel.autreVcNotSync.size,
                remoteResponseState = syncVcViewModel.autreState,
                emailBody = syncVcViewModel.notSyncAutreVcObj,
                isConnected = isConnected,
                onClickSync = { syncVcViewModel.syncAutreVc(syncVcViewModel.autreVcNotSync) }

            )


            SyncButtons(
                text =  "Sync New product vc ",
                nbrNotSync = syncVcViewModel.newProductVcNotSync.size,
                remoteResponseState = syncVcViewModel.newProductState,
                emailBody = syncVcViewModel.notSyncNewProductVcObj,
                isConnected = isConnected,
                onClickSync = { syncVcViewModel.syncNewProductsVc(syncVcViewModel.newProductVcNotSync) }

            )


            SyncButtons(
                text =  "Sync deleted new prod vc ",
                nbrNotSync = syncVcViewModel.newProductDeletedNotSync.size,
                remoteResponseState = syncVcViewModel.newProductDeletedState,
                emailBody = syncVcViewModel.notSyncNewProductDeletedVcObj,
                isConnected = isConnected,
                onClickSync = { syncVcViewModel.syncNewProductToDelete(syncVcViewModel.newProductDeletedNotSync) }

            )

            SyncButtons(
                text =  "Sync deleted autre vc ",
                nbrNotSync = syncVcViewModel.autreDeletedNotSync.size,
                remoteResponseState = syncVcViewModel.autreDeletedState,
                emailBody = syncVcViewModel.notSyncAutreDeletedVcObj,
                isConnected = isConnected,
                onClickSync = { syncVcViewModel.syncAutreToDelete(syncVcViewModel.autreDeletedNotSync) }

            )


            SyncButtons(
                text =  "Sync deleted prix vc ",
                nbrNotSync = syncVcViewModel.prixDeletedNotSync.size,
                remoteResponseState = syncVcViewModel.prixDeletedState,
                emailBody = syncVcViewModel.notSyncPrixDeletedVcObj,
                isConnected = isConnected,
                onClickSync = { syncVcViewModel.syncPrixToDelete(syncVcViewModel.prixDeletedNotSync) }

            )

            SyncButtons(
                text =  "Sync deleted promo vc ",
                nbrNotSync = syncVcViewModel.promoDeletedNotSync.size,
                remoteResponseState = syncVcViewModel.promoDeletedState,
                emailBody = syncVcViewModel.notSyncPromoDeletedVcObj,
                isConnected = isConnected,
                onClickSync = { syncVcViewModel.syncPromoToDelete(syncVcViewModel.promoDeletedNotSync) }

            )


            /**
             * *********************
             *    VC
             * *********************
             */

            /**
             * *********************
             *    Reglement Libre
             * *********************
             */

            SyncButtons(
                text =  "Sync Réglement Libre ",
                nbrNotSync = syncReglementViewModel.reglementLibreNotSync.size,
                remoteResponseState = syncReglementViewModel.reglementCaisseUpdateState,
                emailBody = syncReglementViewModel.notSyncReglementCaisseUpdateObj,
                isConnected = isConnected,
                onClickSync = { syncReglementViewModel.syncReglementLibre() }

            )


            /**
             * *********************
             *    Reglement Libre
             * *********************
             */

            /**
             * *********************
             *    BON RETOUR
             * *********************
             */
            SyncButtons(
                text =  "Sync Bon Retour ",
                nbrNotSync = syncBonRetourViewModel.bonRetourNotSync.size,
                remoteResponseState = syncBonRetourViewModel.responseAddBonRetourState,
                emailBody = syncBonRetourViewModel.notSyncBonRetourObj,
                isConnected = isConnected,
                onClickSync = { syncBonRetourViewModel.syncBonRetour() }

            )


            /**
             * *********************
             *    BON RETOUR
             * *********************
             */

            /**
             * *********************
             *    BON COMMANDE
             * *********************
             */
            SyncButtons(
                text =  "Sync Bon Commande ",
                nbrNotSync = syncBonCommandeViewModel.bonCommandeNotSync.size,
                remoteResponseState = syncBonCommandeViewModel.responseAddBonCommandeState,
                emailBody = syncBonCommandeViewModel.notSyncBonCommandeObj,
                isConnected = isConnected,
                onClickSync = { syncBonCommandeViewModel.syncBonCommande() }

            )


            /**
             * *********************
             *    BON COMMANDE
             * *********************
             */

            /**
             * *********************
             *    BON LIVRAISON
             * *********************
             */
            SyncButtons(
                text =  "Sync Bon Livraison ",
                nbrNotSync = syncBonLivraisonVM.ticketsWithLinesAndPaymentsNotSync.size,
                remoteResponseState = syncBonLivraisonVM.responseAddBatchTicketWithLignesState,
                emailBody = syncBonLivraisonVM.notSyncAddBatchTicketWithLignesObj,
                isConnected = isConnected,
                onClickSync = { syncBonLivraisonVM.syncBonLivraison() }

            )


            /**
             * *********************
             *    BON LIVRAISON
             * *********************
             */

            /**
             * *********************
             *    INV PAT CODE BARE BATIMENT
             * *********************
             */


            SyncButtons(
                text =  "Sync Batiment Code Bare ",
                nbrNotSync = syncInvBatimentViewModel.batimentCBNotSync.size,
                remoteResponseState = syncInvBatimentViewModel.responseAffectCodeBareBatimentState,
                emailBody = syncInvBatimentViewModel.notSyncAffectCodeBareBatimentObj,
                isConnected = isConnected,
                onClickSync = {
                    syncInvBatimentViewModel.syncAffectCodeBareBatiment(batimentCheck = BatimentCheck(
                        cLICode = syncInvBatimentViewModel.batimentCBNotSync.first().cLICode,
                        cltImoCB = syncInvBatimentViewModel.batimentCBNotSync.first().cliImoCB!!
                    )
                    )
                }

            )


            /**
             * *********************
             *    INV PAT CODE BARE BATIMENT
             * *********************
             */


            /**
             * *********************
             *    INV PAT AFFECTATION
             * *********************
             */
            SyncButtons(
                text =  "Sync Affectation ",
                nbrNotSync = syncInvPatrimoineViewModel.affectationNotSync.size,
                remoteResponseState = syncInvPatrimoineViewModel.responseAddAffectationState,
                emailBody = syncInvPatrimoineViewModel.notSyncAffectationObj,
                isConnected = isConnected,
                onClickSync = { syncInvPatrimoineViewModel.syncAffectation() }

            )


            SyncButtons(
                text =  "Sync Affectation Batiment ",
                nbrNotSync = syncInvPatrimoineViewModel.affectationBatimentNotSync.size,
                emailBody = syncInvPatrimoineViewModel.notSyncAffectationBatimentObj,
                remoteResponseState = syncInvPatrimoineViewModel.responseAddAffectationBatimentState,
                isConnected = isConnected,
                onClickSync = { syncInvPatrimoineViewModel.syncAffectationBatiment() }

            )


            SyncButtons(
                text =  "Sync Image Affectation Batiment ",
                nbrNotSync = syncInvPatrimoineViewModel.affectationBatimentImageNotSync.size,
                remoteResponseState = syncInvPatrimoineViewModel.responseImageAddAffectationBatimentState,
                emailBody = syncInvPatrimoineViewModel.notImageAddAffectationBatimentObj,
                isConnected = isConnected,
                onClickSync = { syncInvPatrimoineViewModel.syncImageAffectationBatiment() }

            )


            /**
             * *********************
             *    INV PAT AFFECTATION
             * *********************
             */

            /**
             * *********************
             *    INV PAT DEP OUT
             * *********************
             */
            SyncButtons(
                text =  "Sync Dep Out ",
                nbrNotSync = syncInvPatrimoineViewModel.depOutNotSync.size,
                remoteResponseState = syncInvPatrimoineViewModel.responseAddDepOutState,
                emailBody = syncInvPatrimoineViewModel.notSyncDepOutObj,
                isConnected = isConnected,
                onClickSync = { syncInvPatrimoineViewModel.syncDepOut() }

            )


            SyncButtons(
                text =  "Sync Dep Out Batiment ",
                nbrNotSync = syncInvPatrimoineViewModel.depOutBatimentNotSync.size,
                remoteResponseState = syncInvPatrimoineViewModel.responseAddDepOutBatimentState,
                emailBody = syncInvPatrimoineViewModel.notSyncDepOutBatimentObj,
                isConnected = isConnected,
                onClickSync = { syncInvPatrimoineViewModel.syncDepOutBatiment() }

            )


            SyncButtons(
                text =  "Sync Image Dep Out Batiment ",
                nbrNotSync = syncInvPatrimoineViewModel.depOutBatimentImageNotSync.size,
                remoteResponseState = syncInvPatrimoineViewModel.responseImageDepOutBatimentState,
                emailBody = syncInvPatrimoineViewModel.notSyncImageDepOutBatimentObj,
                isConnected = isConnected,
                onClickSync = { syncInvPatrimoineViewModel.syncImageDepOutBatiment() }

            )


            /**
             * *********************
             *    INV PAT DEP OUT
             * *********************
             */

            /**
             * *********************
             *    INV PAT DEP IN
             * *********************
             */
            SyncButtons(
                text =  "Sync Dep In ",
                nbrNotSync = syncInvPatrimoineViewModel.depInNotSync.size,
                remoteResponseState = syncInvPatrimoineViewModel.responseAddDepInState,
                emailBody = syncInvPatrimoineViewModel.notSyncDepInObj,
                isConnected = isConnected,
                onClickSync = { syncInvPatrimoineViewModel.syncDepIn() }

            )


            SyncButtons(
                text =  "Sync Dep In Batiment ",
                nbrNotSync = syncInvPatrimoineViewModel.depInBatimentNotSync.size,
                remoteResponseState = syncInvPatrimoineViewModel.responseAddDepInBatimentState,
                emailBody = syncInvPatrimoineViewModel.notSyncDepInBatimentObj,
                isConnected = isConnected,
                onClickSync = { syncInvPatrimoineViewModel.syncDepInBatiment() }

            )


            SyncButtons(
                text =  "Sync Image Dep In Batiment ",
                nbrNotSync = syncInvPatrimoineViewModel.depInBatimentImageNotSync.size,
                remoteResponseState = syncInvPatrimoineViewModel.responseImageDepInBatimentState,
                emailBody = syncInvPatrimoineViewModel.notSyncImageDepInBatimentObj,
                isConnected = isConnected,
                onClickSync = { syncInvPatrimoineViewModel.syncImageDepInBatiment() }

            )


            /**
             * *********************
             *    INV PAT DEP IN
             * *********************
             */

            /**
             * *********************
             *    INV PAT INVENTAIRE
             * *********************
             */
            SyncButtons(
                text =  "Sync Inventaire ",
                nbrNotSync = syncInvPatrimoineViewModel.inventaireNotSync.size,
                remoteResponseState = syncInvPatrimoineViewModel.responseAddInventaireState,
                emailBody = syncInvPatrimoineViewModel.notSyncInventaireObj,
                isConnected = isConnected,
                onClickSync = { syncInvPatrimoineViewModel.syncInventaire() }

            )


            SyncButtons(
                text =  "Sync Inventaire Batiment ",
                nbrNotSync = syncInvPatrimoineViewModel.inventaireBatimentNotSync.size,
                remoteResponseState = syncInvPatrimoineViewModel.responseAddInventaireBatimentState,
                emailBody = syncInvPatrimoineViewModel.notSyncInventaireBatimentObj,
                isConnected = isConnected,
                onClickSync = { syncInvPatrimoineViewModel.syncInventaireBatiment() }

            )


            SyncButtons(
                text =  "Sync Image Inventaire Batiment ",
                nbrNotSync = syncInvPatrimoineViewModel.inventaireBatimentImageNotSync.size,
                remoteResponseState = syncInvPatrimoineViewModel.responseImageInventaireBatimentState,
                emailBody = syncInvPatrimoineViewModel.notSyncImageInventaireBatimentObj,
                isConnected = isConnected,
                onClickSync = { syncInvPatrimoineViewModel.syncImageInventaireBatiment() }

            )


            /**
             * *********************
             *    INV PAT INVENTAIRE
             * *********************
             */

            SyncButtons(
                text =  "Sync Ligne Ordre Mission ",
                nbrNotSync = syncTourneeViewModel.lgOrdMissionNotSync.size,
                remoteResponseState = syncTourneeViewModel.batchUpdateLigneOrdreMissionState,
                emailBody = syncTourneeViewModel.notSyncBatchUpdateLigneOrdreMissionObj,
                isConnected = isConnected,
                onClickSync = { syncTourneeViewModel.syncLgOrdMission() }

            )


            /**
             * *********************
             *    ORDRE MISSION
             * *********************
             */

            /**
             * *********************
             *    ORDRE MISSION
             * *********************
             */
        }
    }
}




