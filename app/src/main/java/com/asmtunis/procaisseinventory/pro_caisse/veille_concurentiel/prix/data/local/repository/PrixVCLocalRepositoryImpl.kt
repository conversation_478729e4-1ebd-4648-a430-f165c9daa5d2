package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.local.repository


import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine.PrixVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine.PrixVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.local.dao.PrixVCDAO
import kotlinx.coroutines.flow.Flow



class PrixVCLocalRepositoryImpl(
    private val prixVCDAO: PrixVCDAO
) : PrixVCLocalRepository {


    override fun upsertAll(value: List<PrixVC>) = prixVCDAO.insertAll(value)
    override fun upsert(value: PrixVC) = prixVCDAO.insert(value)
    override fun updateCloudCode(code: String, codeM: String) =
        prixVCDAO.updateCloudCode(code = code,codeM = codeM )

    override fun restDeleted(code: String,status : String, isSync:Boolean)  =
        prixVCDAO.restDeleted(code,status , isSync)

    override fun setDeleted(code: String, codeMobile: String)  =
        prixVCDAO.setDeleted(code, codeMobile)


    override fun deleteAll() = prixVCDAO.deleteAll()
    override fun deleteByCode(codeAutre: String) = prixVCDAO.deleteByCode(codeAutre)

    override fun getAll(): Flow<List<PrixVC>> = prixVCDAO.all
    override fun noSyncedToDelete(): Flow<List<PrixVC>?>  = prixVCDAO.noSyncedToDelete


    override fun noSyncedToAddOrUpdate(): Flow<List<PrixVC>> =
        prixVCDAO.noSyncedToAddOrUpdate


    override fun filterByNum(
        searchString: String,
        filterByTypComm: String,
        sortBy: String,
        isAsc: Int
    ): Flow<List<PrixVCWithImages>> = prixVCDAO.filterByNum(
        searchString = searchString,
        filterByTypComm = filterByTypComm,
        sortBy = sortBy,
        isAsc = isAsc
    )

    override fun filterByArtConcurrent(
        searchString: String,
        filterByTypComm: String,
        sortBy: String?,
        isAsc: Int?
    ): Flow<List<PrixVCWithImages>> = prixVCDAO.filterByArtConcurrent(
        searchString = searchString,
        filterByTypComm = filterByTypComm,
        sortBy = sortBy,
        isAsc = isAsc
    )

    override fun filterByCodeArtLocal(
        searchString: String,
        sortBy: String,
        filterByTypComm: String,
        isAsc: Int
    ): Flow<List<PrixVCWithImages>> = prixVCDAO.filterByCodeArtLocal(
        searchString = searchString,
        filterByTypComm = filterByTypComm,
        sortBy = sortBy,
        isAsc = isAsc
    )

    override fun getAllFiltred(
        isAsc: Int,
        filterByTypComm: String,
        sortBy: String
    ): Flow<List<PrixVCWithImages>> = prixVCDAO.getAllFiltred(
        filterByTypComm = filterByTypComm,
        sortBy = sortBy,
        isAsc = isAsc)

}