<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;

class VisiteWSMobile extends Controller
{


    public function getVisite(Request $request)

    {
        $data = $request->json()->all();
        $session = $data["object"];

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Visite')->where('TIK_IdSCaisse', $data["object"])->get());
    }


    public function getVisitesByCaisseId(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        $tickets = $connection->table('Visite')->where('TIK_IdSCaisse', $data["object"])->orderBy('TIK_DateHeureTicket', 'DESC')->get();


        if ($tickets != null) {
            return response()->json($tickets);

        } else {
            return response()->json("");

        }

    }


    public function addVisite(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);


        if (!empty ($data)) {
            $t = $connection->table('Visite')->insert($data);

            return response()->json($t);

        } else {
            return (false);
        }

    }

    public function addVisiteWithLignesVisite(Request $request)
    {
        $items = $request->json()->all();

        $connection = DatabaseConnection::setConnection($items);

        $result = null;


        if (!empty ($items)) {


            foreach ($items["object"] as $data) {


                if (!empty ($data)) {

                    $visite = $data["visite"];
                    if ($visite != null) {

                        $visite["TIK_DateHeureTicket"] = date('m/d/Y H:i:s', strtotime($visite["TIK_DateHeureTicket"]));

                        $t = $connection->table('Visite')->insert($visite);
                        if ($t) {

                            $lignesVisite = $data["lignesVisite"];

                            foreach ($lignesVisite as $ligneVisite) {

                                $ligne["LT_NumTicket"] = $ligneVisite["LT_NumTicket"];

                                $ligne["LT_IdCarnet"] = $ligneVisite["LT_IdCarnet"];

                                $ligne["LT_Exerc"] = $ligneVisite["LT_Exerc"];

                                $ligne["LT_CodArt"] = $ligneVisite["LT_CodArt"];

                                $ligne["LT_PrixVente"] = $ligneVisite["LT_PrixVente"];

                                $ligne["LT_Remise"] = $ligneVisite["LT_Remise"];

                                $ligne["LT_TVA"] = $ligneVisite["LT_TVA"];

                                $ligne["LT_MtHT"] = $ligneVisite["LT_MtHT"];

                                $ligne["LT_Unite"] = $ligneVisite["LT_Unite"];

                                $ligne["LT_Qte"] = $ligneVisite["LT_Qte"];

                                $ligne["LT_QtePiece"] = $ligneVisite["LT_QtePiece"];


                                $ligne["LT_MtTTC"] = $ligneVisite["LT_MtTTC"];

                                $ligne["LT_Annuler"] = $ligneVisite["LT_Annuler"];

                                $ligne["LT_NumOrdre"] = $ligneVisite["LT_NumOrdre"];

                                $ligne["LT_PrixEncaisse"] = $ligneVisite["LT_PrixEncaisse"];

                                $ligne["LT_PACHAT"] = $ligneVisite["LT_PACHAT"];

                                $ligne["LT_Taux_Remise"] = $ligneVisite["LT_Taux_Remise"];

                                $ligne["Exist"] = $ligneVisite["Exist"];

                                $connection->table('LigneVisite')->insert($ligne);
                            }
                        }

                        $result[] = $data;
                    } else {
                        $result[] = null;

                    }
                } else {

                    $result[] = null;

                }


            }

        }

        return response()->json($result);

    }


    public function updateVisite(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        if (!empty ($data)) {

            $response = $connection->table('Ticket')->where('TIK_NumTicket', $data["TIK_NumTicket"])->where('TIK_Exerc', $data["TIK_Exerc"])->where('TIK_IdCarnet', $data["TIK_IdCarnet"])->update($data);


            return response()->json(($response == 1) ? true : false);

        } else {
            return response()->json(false);
        }


    }


    public function deleteVisite(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        if (!empty ($data)) {
            $data["TIK_Annuler"] = true;
            $response = $connection->table('Visite')->where('TIK_NumTicket', $data["TIK_NumTicket"])->where('TIK_Exerc', $data["TIK_Exerc"])->where('TIK_IdCarnet', $data["TIK_IdCarnet"])->update($data);


            return response()->json(($response == 1) ? true : false);

        } else {
            return response()->json(false);
        }
    }


}





