<?php


namespace App\Helpers;


class Enum
{
    const Success_Code = 10200;
    const Error_Code = 10500;
    const CODE_ARTICLE_DONT_EXIST = 10001;
    const Unite_ARTICLE_DONT_EXIST = 10002;
    const Mnt_HT_BL_NULL = 10003;
    const Mnt_TTC_BL_NULL = 10004;
    const Error_Insert_LG_BL = 10005;
    const Error_Insert_BL = 10006;
    const Station_DONT_EXIST = 10007;
    const BON_Trans_StatSource_Equal_BON_Trans_StatDest = 10008;
    const Error_Insert_LG_BE = 10101;
    const Qte_Null = 10103;

    //Ticket
    const Error_Insert_Ticket = 10202;
    const Error_Insert_ReglementCaisse = 10203;
    const Error_Insert_TraiteCaisse = 10204;
    const Error_Insert_Cheque = 10205;
    const Error_Insert_Ligne_Ticket = 10206;
    const Error_Montant = 10207;
    const Error_exist_Ticket = 10201;
    const Error_Session_Dont_exist = 10208;



    //Facture
    const Error_Insert_Facture = 10302;
    const Error_Insert_Ligne_Facture = 10303;
    const Error_FACT_Num_EXIST = 10304;


    //Visite
    const Error_Insert_DN_Visite = 10503;
    const Error_Insert_Ligne_Visite = 10506;
    const Error_Update_Visite = 10507;
    const Error_Delete_Ligne_Visite = 10508;
    const Error_Delete_Visite = 10509;
    //Depence
    const  Error_Insert_Depense = 10600;
    const  Error_Update_Depense = 10601;
    const  Error_Insert_Depense_caisse = 10602;
    const  Error_Update_Depense_caisse = 10603;

    //Bon Commande
    const Error_BC_Exite = 10701;
    const Error_Insert_BC = 10702;
    const Error_Insert_LigneBC = 10703;

    //patrimoine
    const Error_article_pat_affecte = 10704;
    const Error_article_out = 10705;
    const Error_article_in = 10706;
    const Error_patrimoine = 10707;
    const Error_code_barre_batiment_deja_affecte = 10708;
    const Error_inventaire_code_barre_batiment_ = 10709;
    const Error_num_serie_egale_code_barre_batiment = 10720;


    //Bon Entree
    const Error_BE_Existe = 10710;
    const Error_Insert_BE = 10711;
    const Error_Insert_LigneBE = 10712;

    //VConc

    const Error_Upload_Image_VC = 10810;

    //Marque

    const Error_Insert_Marque = 10820;

    //Marque

    const Error_Insert_Ticket_Rayon = 10830;



}