package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.local.repository

import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.domaine.DeplacementOutByUser
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.domaine.DeplacementOutByUserWithImmobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.local.dao.DeplacementOutByUserDAO
import kotlinx.coroutines.flow.Flow


class DeplacementOutByUserLocalRepositoryImpl(
        private val deplacementOutByUserDAO: DeplacementOutByUserDAO
    ) : DeplacementOutByUserLocalRepository {
    override fun upsertAll(value: List<DeplacementOutByUser>) = deplacementOutByUserDAO.insertAll(value)

    override fun upsert(value: DeplacementOutByUser)  = deplacementOutByUserDAO.insert(value)
    override fun getByNumSerie(code: String): Flow<Map<DeplacementOutByUser, List<LigneBonCommande>>?> =
        deplacementOutByUserDAO.getByNumSerie(code = code)

    override fun deleteAll() = deplacementOutByUserDAO.deleteAll()

    override fun getAll(
        station: String,
        devEtat: String
    ): Flow<Map<DeplacementOutByUser, List<LigneBonCommande>>> =
        deplacementOutByUserDAO.getAll(
            station = station,
            devEtat = devEtat
        )

    override fun getAllFiltred(
        onlyWaiting: String,
        isAsc: Int,
        sortBy: String
    ): Flow<Map<DeplacementOutByUserWithImmobilisation, List<LigneBonCommande>>> =
        deplacementOutByUserDAO.getAllFiltred(
            onlyWaiting = onlyWaiting,
            isAsc = isAsc,
            sortBy = sortBy
        )

    override fun filterByNumSerie(
        onlyWaiting: String,
        searchString: String,
        sortBy: String,
        isAsc: Int
    ): Flow<Map<DeplacementOutByUserWithImmobilisation, List<LigneBonCommande>>>  =
        deplacementOutByUserDAO.filterByNumSerie(
            onlyWaiting = onlyWaiting,
            searchString = searchString,
            sortBy = sortBy,
            isAsc = isAsc
        )
    override fun filterByBonCommandeNum(
        onlyWaiting: String,
        searchString: String,
        sortBy: String,
        isAsc: Int
    ): Flow<Map<DeplacementOutByUserWithImmobilisation, List<LigneBonCommande>>>  =
        deplacementOutByUserDAO.filterByBonCommandeNum(
            onlyWaiting = onlyWaiting,
            searchString = searchString,
            sortBy = sortBy,
            isAsc = isAsc
        )

    override fun filterByBatiment(
        onlyWaiting: String,
        searchString: String,
        sortBy: String,
        isAsc: Int
    ): Flow<Map<DeplacementOutByUserWithImmobilisation, List<LigneBonCommande>>>  =
        deplacementOutByUserDAO.filterByBatiment(
            onlyWaiting = onlyWaiting,
            searchString = searchString,
            sortBy = sortBy,
            isAsc = isAsc
        )
}