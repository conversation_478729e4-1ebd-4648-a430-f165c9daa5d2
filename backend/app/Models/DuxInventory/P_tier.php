<?php

namespace App\Models\DuxInventory;



use Carbon\Carbon;


class P_tier extends BaseModel
{
    protected $connection = 'onthefly';
    protected $table = 'P_tier';
    public $incrementing = false;
    public $timestamps = false;
    protected $guarded = [];

    const ID_CLASSE = 'Fournisseur';
    const DEFAULT_ID_SOURCE = 3;
    const DEFAULT_TIER_RETOUR = false;
    const ID_TYPE_TIER = 2;

    protected $casts = [
        'isSync' => 'boolean',
        'isDefault' => 'boolean',
        'active' => 'boolean',
        'j_export' => 'boolean',
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->idClasse = self::ID_CLASSE;
        $this->useCode = true;
    }

//    public function typeTier()
//    {
//        return $this->belongsTo(E_TypeTier::class, 'idTypeTier');
//    }
//    public function regime()
//    {
//        return $this->belongsTo(P_Regime::class, 'idRegime');
//    }
//    public function pays()
//    {
//        return $this->belongsTo(P_pays::class, 'idPays');
//    }
//    public function ville()
//    {
//        return $this->belongsTo(P_ville::class, 'idVille');
//    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->setDefaultValues();
            $model->fillRelatedData();
        });

//        static::saved(function ($model) {
//            $model->load('typeTier', 'regime', 'pays', 'ville');
//            $model->fillRelatedData();
//        });
    }

    public function setDefaultValues()
    {
        $now = Carbon::now()->format('Y-m-d H:i:s');
        $this->dateCreation = $now;
        $this->dateClotureCompte = $now;
        $this->dateNaissance = $now;
        $this->idTypeTier = self::ID_TYPE_TIER;
        $this->Id_Source = self::DEFAULT_ID_SOURCE;
        $this->tierRetour = self::DEFAULT_TIER_RETOUR;
    }

//    public function fillRelatedData()
//    {
//        $relations = ['typeTier', 'regime', 'pays', 'ville'];
//        foreach ($relations as $relation) {
//            if ($this->$relation) {
//                $this->{"code" . ucfirst($relation)} = $this->$relation->code ?? null;
//                $this->{"libelle" . ucfirst($relation)} = $this->$relation->libelle ?? null;
//            }
//        }
//    }

    public function fillRelatedData()
    {
        $typeTier = E_TypeTier::find(self::ID_TYPE_TIER);
        $regime = P_Regime::find($this->idRegime);
        $pays = P_pays::find($this->idPays);
        $ville = P_ville::find($this->idVille);

        if ($typeTier) {
            $this->codeTypeTier = $typeTier->code;
            $this->libelleTypeTier = $typeTier->libelle;
        }

        if ($regime) {
            $this->codeRegime = $regime->code ?? null;
            $this->libelleRegime = $regime->libelle ?? null;
        }

        if ($pays) {
            $this->codePays = $pays->code ?? null;
            $this->libellePays = $pays->libelle ?? null;
        }

        if ($ville) {
            $this->codeVille = $ville->code ?? null;
            $this->libelleVille = $ville->libelle ?? null;
        }
    }

}
