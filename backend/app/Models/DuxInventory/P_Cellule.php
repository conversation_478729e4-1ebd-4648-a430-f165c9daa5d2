<?php

namespace App\Models\DuxInventory;


class P_Cellule extends BaseModel
{
    protected $table = 'P_cellule';
    protected $connection = 'onthefly';
    public $incrementing = false;
    public $timestamps = false;
    protected $guarded = [];
    protected $casts = [
        'IsDefault' => 'boolean',
        'j_export' => 'boolean',
        'isSync' => 'boolean',
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->idClasse = 'P_cellule';
        $this->useCode = true;
    }
}
