package com.asmtunis.blovedream

import android.app.Activity.RECEIVER_NOT_EXPORTED
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.device.ScanDevice
import android.os.Build

object BlovedreamPDAUtils {
    var smBlovedreamPDA: ScanDevice? = ScanDevice()
    const val SCAN_ACTION: String = "scan.rcv.message"




    fun mScanReceiver(
        onReceive: (Intent) -> Unit
    ): BroadcastReceiver {
      return  object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                val action = intent.action
                if (action == SCAN_ACTION) {
                    onReceive(intent)



                    /*  showScanResult.setText("")
                      showScanResult.append("Barcode=$barcodeStr")
                      showScanResult.append("\n")
                      showScanResult.append("AIM ID=$aimID")
                      showScanResult.append("\n")
                      showScanResult.append("temp= $temp")*/
                    smBlovedreamPDA?.stopScan()
                    //UtilSound.play()
                }
            }
        }
    }


    fun startBlovedreamPDA() {
          smBlovedreamPDA = ScanDevice()
          smBlovedreamPDA?.outScanMode = 0
    }

    fun releaseBlovedreamPDA() {
        if (smBlovedreamPDA != null) {
            smBlovedreamPDA?.stopScan()
            smBlovedreamPDA?.scanLaserMode = 8
        }
    }


    fun Context.registerBlovedreamPDAReceiver(mScanReceiverBlovedreamPDA: BroadcastReceiver?) {
        if(mScanReceiverBlovedreamPDA == null) return
        val filter = IntentFilter(SCAN_ACTION)
        //  filter.addAction(SCAN_ACTION)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(mScanReceiverBlovedreamPDA, filter, RECEIVER_NOT_EXPORTED)
        }
        else
            registerReceiver(mScanReceiverBlovedreamPDA, filter)

    }



    fun Context.unregisterBlovedreamPDAReceiver(mScanReceiverBlovedreamPDA: BroadcastReceiver?) {
       if(mScanReceiverBlovedreamPDA != null) unregisterReceiver(mScanReceiverBlovedreamPDA)
    }
}