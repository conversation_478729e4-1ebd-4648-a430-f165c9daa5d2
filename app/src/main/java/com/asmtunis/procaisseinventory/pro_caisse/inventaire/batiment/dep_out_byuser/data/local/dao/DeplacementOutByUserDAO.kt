package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.domaine.DeplacementOutByUser
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.domaine.DeplacementOutByUserWithImmobilisation
import kotlinx.coroutines.flow.Flow


@Dao
interface DeplacementOutByUserDAO {

    @Transaction
    @Query(
        "SELECT * FROM ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER} " +
                "JOIN ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} ON ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_Num = ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_NumBon " +
                " WHERE DEV_Etat LIKE :devEtat and DEV_Station LIKE :station"
    )
    fun getAll(station: String, devEtat: String): Flow<Map<DeplacementOutByUser, List<LigneBonCommande>>>



    @Query("SELECT * FROM ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER} where DEV_Station =:station and DEV_Etat =:BCType order by strftime('%Y-%m-%d %H-%M',DEV_Date) desc")
    fun getByStationMutable(station: String, BCType: String): Flow<List<DeplacementOutByUser>>


    @Transaction
    @Query(
        "SELECT * FROM ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER} " +
                "JOIN ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} ON ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_Num = ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_NumBon " +
                "where LG_DEV_NumSerie =:code " +
                //  "order by strftime('%Y-%m-%d %H-%M',DEV_Date) desc")
                "order by strftime('%Y-%m-%d %H-%M-%S',${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_DDm) desc")
    fun getByNumSerie(code: String): Flow<Map<DeplacementOutByUser, List<LigneBonCommande>>?>

    //List<LigneBonCommande> getByNumSerie(String code);
    @Transaction
    @Query("SELECT * FROM ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} where LG_DEV_NumSerie =:code order by strftime('%Y-%m-%d %H-%M-%S',LG_DEV_DDm) desc")
    fun getByNumSerieList(code: String): Flow<List<LigneBonCommande>>

    @Transaction
    @Query("SELECT * FROM ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} where LG_DEV_NumSerie =:code")
    fun getByNumSerieandCodeClient(code: String): Flow<LigneBonCommande>

    @Query("UPDATE ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER} SET DEV_CodeClient = :code_client where DEV_CodeClient = :oldCodeClient")
    fun updateCodeClient(code_client: String, oldCodeClient: String)

    @Query("SELECT ifnull(MAX(cast(substr(DEV_Num,length(:prefix) + 1 ,length('DEV_Num'))as integer)),0)+1 FROM ${ProCaisseConstants.BON_COMMANDE_TABLE} WHERE substr(DEV_Num, 0 ,length(:prefix)+1) = :prefix")
    fun getNewCode(prefix: String): Flow<String>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: DeplacementOutByUser)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<DeplacementOutByUser>)





    @Query("DELETE FROM ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}")
    fun deleteAll()

    @Query("DELETE FROM ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER} where DEV_Code_M= :codeCommande/* and BON_LIV_Exerc=:exercie*/")
    fun deleteByIdM(codeCommande: String)

    @Query("SELECT *  FROM ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER} where DEV_Code_M= :codeCommande and BON_LIV_Exerc=:exercie")
    fun getListByCodeM(codeCommande: String, exercie: String): Flow<DeplacementOutByUser>

    @Query("SELECT *  FROM ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER} where DEV_Num= :codeCommande and BON_LIV_Exerc=:exercie")
    fun getByCode(codeCommande: String, exercie: String): Flow<DeplacementOutByUser>

    @Query("SELECT *  FROM ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER} where DEV_CodeClient= :codeCommande and DEV_info3=:devinf3")
    fun getByCodeClientandPatEtat(codeCommande: String, devinf3: String): Flow<List<DeplacementOutByUser>>

    @Query("SELECT *  FROM ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER} where DEV_CodeClient= :codeCommande and DEV_info3=:devinf3")
    fun getByCodeClientandPatEtatLiveData(
        codeCommande: String,
        devinf3: String
    ): Flow<List<DeplacementOutByUser>>





    @Query("UPDATE ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER} SET DEV_Observation = :devObservation where DEV_Num =:devNum  and BON_LIV_Exerc=:exercie")
    fun updateObservation(devObservation: String, devNum: String, exercie: String)




    @Transaction
    @Query(
        "SELECT * FROM ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER} " +
                "JOIN ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} ON ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_Num = ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_NumBon" +
                " WHERE ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_Client  LIKE '%' || :searchString || '%' or  ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_TyMvtCode  LIKE '%' || :searchString || '%'" +
                "and  CASE WHEN :onlyWaiting !=  '' THEN ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon LIKE '%' || :onlyWaiting || '%' ELSE ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon LIKE '1' OR ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon LIKE '2' END" +
                " ORDER BY " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 1 THEN DEV_Num END ASC, " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 2 THEN DEV_Num END DESC, " +
                "CASE WHEN :sortBy = 'DEV_CodeClient'  AND :isAsc = 1 THEN DEV_CodeClient END ASC, " +
                "CASE WHEN :sortBy = 'DEV_CodeClient'  AND :isAsc = 2 THEN DEV_CodeClient END DESC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_Date) END ASC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_Date) END DESC "
    )
    fun filterByBatiment(onlyWaiting: String, searchString: String, sortBy: String, isAsc: Int): Flow<Map<DeplacementOutByUserWithImmobilisation, List<LigneBonCommande>>>

    @Transaction
    @Query(
        "SELECT * FROM ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER} " +
                "JOIN ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} ON ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_Num = ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_NumBon" +
                " WHERE ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_Num LIKE '%' || :searchString || '%' " +
                "and  CASE WHEN :onlyWaiting !=  '' THEN ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon LIKE '%' || :onlyWaiting || '%' ELSE ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon LIKE '1' OR ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon LIKE '2' END" +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 1 THEN DEV_Num END ASC, " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 2 THEN DEV_Num END DESC, " +
                "CASE WHEN :sortBy = 'DEV_CodeClient'  AND :isAsc = 1 THEN DEV_CodeClient END ASC, " +
                "CASE WHEN :sortBy = 'DEV_CodeClient'  AND :isAsc = 2 THEN DEV_CodeClient END DESC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_Date) END ASC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_Date) END DESC "
    )
    fun filterByBonCommandeNum(onlyWaiting: String, searchString: String,  sortBy: String, isAsc: Int): Flow<Map<DeplacementOutByUserWithImmobilisation, List<LigneBonCommande>>>

    @Transaction
    @Query(
        "SELECT * FROM ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER} " +
                "JOIN ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} ON ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_Num = ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_NumBon" +
                " WHERE ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_NumSerie LIKE '%' || :searchString || '%'  " +
                "and  CASE WHEN :onlyWaiting !=  '' THEN " +
                "CASE WHEN :onlyWaiting = '1' THEN (${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon = '1' OR ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon = 'En Instance') " +
                "WHEN :onlyWaiting = '2' THEN (${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon = '2' OR ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon = 'Validée') " +
                "ELSE ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon LIKE '%' || :onlyWaiting || '%' END " +
                "ELSE (${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon = '1' OR ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon = '2' OR ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon = 'En Instance' OR ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon = 'Validée') END" +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 1 THEN DEV_Num END ASC, " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 2 THEN DEV_Num END DESC, " +
                "CASE WHEN :sortBy = 'DEV_CodeClient'  AND :isAsc = 1 THEN DEV_CodeClient END ASC, " +
                "CASE WHEN :sortBy = 'DEV_CodeClient'  AND :isAsc = 2 THEN DEV_CodeClient END DESC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_Date) END ASC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_Date) END DESC "
    )
    fun filterByNumSerie(onlyWaiting: String, searchString: String, sortBy: String, isAsc: Int): Flow<Map<DeplacementOutByUserWithImmobilisation, List<LigneBonCommande>>>

    @Transaction
    @Query(
        "SELECT * FROM ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER} " +
                "JOIN ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} ON ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_Num = ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_NumBon " +
                "and  CASE WHEN :onlyWaiting !=  '' THEN " +
                "CASE WHEN :onlyWaiting = '1' THEN (${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon = '1' OR ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon = 'En Instance') " +
                "WHEN :onlyWaiting = '2' THEN (${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon = '2' OR ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon = 'Validée') " +
                "ELSE ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon LIKE '%' || :onlyWaiting || '%' END " +
                "ELSE (${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon = '1' OR ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon = '2' OR ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon = 'En Instance' OR ${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_EtatBon = 'Validée') END" +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 1 THEN DEV_Num END ASC, " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 2 THEN DEV_Num END DESC, " +
                "CASE WHEN :sortBy = 'DEV_CodeClient'  AND :isAsc = 1 THEN DEV_CodeClient END ASC, " +
                "CASE WHEN :sortBy = 'DEV_CodeClient'  AND :isAsc = 2 THEN DEV_CodeClient END DESC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_Date) END ASC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',${ProCaisseConstants.DEPLACEMENT_OUT_BY_USER}.DEV_Date) END DESC "
    )
    fun getAllFiltred(onlyWaiting: String, isAsc: Int,  sortBy: String): Flow<Map<DeplacementOutByUserWithImmobilisation, List<LigneBonCommande>>>

}


