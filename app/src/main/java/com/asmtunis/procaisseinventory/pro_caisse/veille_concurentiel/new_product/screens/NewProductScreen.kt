package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.navigation.NewProductDetailRoute
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ConcurrentVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.NewProductViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.text_validation.VcTextValidationViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.view_model.VeilleConcurentielViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.CameraViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.ListItem
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PullToRefreshLazyColumn
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel

@Composable
fun NewProductScreen (
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    listState: LazyListState,
    isConnected: Boolean,
    selectedBaseconfig: BaseConfig,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    vcViewModel: VeilleConcurentielViewModel,
    cameraViewModel: CameraViewModel,
    newProdViewModel: NewProductViewModel,
    mainViewModel: MainViewModel,
    textValidationViewModel: VcTextValidationViewModel = hiltViewModel()
) {

    /*LaunchedEffect(key1 = newProdViewModel.newProductSearchTextState.text, key2 = newProdState.lists, key3 = newProdState.search) {
        newProdViewModel.filterNewProductVC(newProdState)
    }*/
    val context = LocalContext.current

    val concurentList = mainViewModel.listConcurentVC

     val newProductList = newProdViewModel.newProductFilterListstate.lists


    val newProductState  = getProCaisseDataViewModel.newProductState.loading



if (newProductList.isNotEmpty())
    ListNewProd (
        navigate = { navigate(it) },
        popBackStack = { popBackStack() },
        isConnected = isConnected,
        selectedBaseconfig = selectedBaseconfig,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        listState = listState,
        cameraViewModel = cameraViewModel,
        textValidationViewModel = textValidationViewModel,
        newProductVCWithImages = newProductList,
        vcViewModel = vcViewModel,
        newProdViewModel = newProdViewModel,
        concurentList = concurentList,
        newProductState = getProCaisseDataViewModel.newProductState,
        utilisateur = mainViewModel.utilisateur,
        mainViewModel = mainViewModel
    )

    else Column(
    modifier = Modifier.fillMaxSize(),
    verticalArrangement = Arrangement.Center,
    horizontalAlignment = Alignment.CenterHorizontally
) {
    LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
}
}

@Composable
fun ListNewProd (
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    isConnected: Boolean,
    newProductState: RemoteResponseState<List<NewProductVC>>,
    selectedBaseconfig: BaseConfig,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    mainViewModel: MainViewModel,
    utilisateur: Utilisateur,
    concurentList: List<ConcurrentVC>,
    listState: LazyListState,
    cameraViewModel: CameraViewModel,
    textValidationViewModel: VcTextValidationViewModel,
    newProductVCWithImages: List<NewProductVCWithImages>,
    vcViewModel: VeilleConcurentielViewModel,
    newProdViewModel: NewProductViewModel
){

    val isRefreshing  = newProductState.loading
    val prefixList  = mainViewModel.prefixList
    PullToRefreshLazyColumn(
        items = newProductVCWithImages,
        lazyListState = listState,
        isRefreshing = isRefreshing,
        pullToRefreshEnabled = !newProductVCWithImages.any { it.newProductVC?.isSync != true } && isConnected,
        onRefresh = { getProCaisseDataViewModel.getVCNewProduct(baseConfig = selectedBaseconfig) },
        key = { newProdList -> newProdList.newProductVC?.id?: newProdList },
        content = { newProductVCWithImage ->

            val newProductVC = newProductVCWithImage.newProductVC
            val typeCom : String = vcViewModel.typeCommunicationVCList.firstOrNull { it.codeTypeCom == newProductVCWithImage.newProductVC?.codeTypeCom }?.typeCommunication?:newProductVCWithImage.newProductVC?.codeTypeCom?:""

            val concurrent = concurentList.firstOrNull { it.codeconcurrent == newProductVC?.codeConcur }?.concurrent?: newProductVC?.codeConcur

            ListItem(
                onItemClick={
                    val prefixe = prefixList.firstOrNull { it.pREIdTable == "VCLancementNP" }?.pREPrefixe?: "VC_NP_M"
                    val prefixImage = prefixList.firstOrNull { it.pREIdTable == "VC_Image" }?.pREPrefixe?: "VC_NP_M_IMG"

                    mainViewModel.generateCodeM(
                        utilisateur = utilisateur,
                        prefix = prefixe,
                        prefixImage = prefixImage
                    )


                    cameraViewModel.addListImageUri(newProductVCWithImage.imageList?: emptyList())

                    newProdViewModel.onselectedNewProdChange(newProductVCWithImage)
                    newProdViewModel.onModifyChange(false)
                   // cameraViewModel.addImageUri(imgUri = EMPTY_IMAGE_URI, context = context)
                    navigate(NewProductDetailRoute)

                    textValidationViewModel.resetVariable()
                },
                firstText = newProductVC?.codeVCLanP.toString(),
                secondText = stringResource(id = R.string.concurrent_value, concurrent?: "N/A"),
                thirdText = stringResource(id = R.string.type_communication_value, typeCom),
                forthText = stringResource(id = R.string.new_product, newProductVC?.produitLanP ?: "N/A"),
                dateText = newProductVC?.dateOp.toString(),
                isSync = newProductVC?.isSync == true,
                status = newProductVC?.status.toString(),
                onResetDeletedClick = {
                    newProdViewModel.restDeletedNewProd(newProductVCWithImage)
                },
                onMoreClick = {
                    newProdViewModel.onselectedNewProdChange(newProductVCWithImage)
                    vcViewModel.onShowCustomModalBottomSheetChange(true)
                }
            )
        },
    )


}
