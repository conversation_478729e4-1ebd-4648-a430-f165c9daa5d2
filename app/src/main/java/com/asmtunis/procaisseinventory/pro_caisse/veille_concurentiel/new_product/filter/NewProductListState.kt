package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.filter

import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVCWithImages
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch


data class NewProductListState(
    val lists: List<NewProductVCWithImages> = emptyList(),
    //val lists   : MutableList<NewProductVC> = mutableStateListOf(NewProductVC()),

      //val lists:   List<NewProductVC> = mutableStateListOf(),
    val listOrder: ListOrder = ListOrder.Date(OrderType.Descending),
    val search: ListSearch = ListSearch.FirstSearch(),
    val filterByConcurent: String = "",
    val filterByTypeCommunication: String = ""
)