package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.typeservice.repository


import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypeServicesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.typeservice.dao.TypeServicesDAO
import kotlinx.coroutines.flow.Flow


class TypeServicesLocalRepositoryImpl(
    private val typeServicesDAO: TypeServicesDAO
) : TypeServicesLocalRepository {
    override fun upsert(value: TypeServicesDn) = typeServicesDAO.insert(value)

    override fun upsertAll(value: List<TypeServicesDn>) = typeServicesDAO.insertAll(value)


    override fun deleteAll() = typeServicesDAO.deleteAll()

    override fun getAll(): Flow<List<TypeServicesDn>> = typeServicesDAO.all

}