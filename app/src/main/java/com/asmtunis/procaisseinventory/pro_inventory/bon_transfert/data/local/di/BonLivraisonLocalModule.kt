package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.bn_livraison.dao.BonLivraisonDAO
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.bn_livraison.repository.BonLivraisonLocalRepository
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.bn_livraison.repository.BonLivraisonLocalRepositoryImpl
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.ligne_bn_livraison.dao.LigneBonLivraisonDAO
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.ligne_bn_livraison.repository.LigneBonLivraisonLocalRepository
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.ligne_bn_livraison.repository.LigneBonLivraisonLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton



@Module
@InstallIn(SingletonComponent::class)
class BonLivraisonLocalModule {

    @Provides
    @Singleton
    fun provideBonLivraisonDao(
        proInventoryDataBase: ProCaisseDataBase
    ) = proInventoryDataBase.bonLivraisonDAO()

    @Provides
    @Singleton
    @Named("BonLivraison")
    fun provideBonLivraisonRepository(
        bonLivraisonDAO: BonLivraisonDAO
    ): BonLivraisonLocalRepository = BonLivraisonLocalRepositoryImpl(
        bonLivraisonDAO = bonLivraisonDAO
    )



    @Provides
    @Singleton
    fun provideLigneBonLivraisonDao(
        proInventoryDataBase: ProCaisseDataBase
    ) = proInventoryDataBase.ligneBonLivraisonDAO()

    @Provides
    @Singleton
    @Named("LigneBonLivraison")
    fun provideLigneBonLivraisonRepository(
        ligneBonLivraisonDAO: LigneBonLivraisonDAO
    ): LigneBonLivraisonLocalRepository = LigneBonLivraisonLocalRepositoryImpl(
        ligneBonLivraisonDAO = ligneBonLivraisonDAO
    )
}