package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.screens

import com.asmtunis.procaisseinventory.nav_components.NavDrawer
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.Scaffold
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationViewModule
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AddModifyDigitalDistributionRoute
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.LigneVisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.VisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.view_model.DistributionNumeriqueViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.bottom_sheet.CustomModalBottomSheet
import com.asmtunis.procaisseinventory.shared_ui_components.floating_button.SnapScrollingButton
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.ListItem
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PullToRefreshLazyColumn
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import kotlinx.coroutines.launch

@Composable
fun DigitalDistributionScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    clientId: String,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    mainViewModel: MainViewModel,
    locationViewModule : LocationViewModule,
    distNumViewModel: DistributionNumeriqueViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    networkViewModel: NetworkViewModel,
    dataViewModel: DataViewModel,
    settingViewModel: SettingViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncInventoryViewModel: SyncInventoryViewModel,

    ) {
    val syncDistNumViewModel = syncProcaisseViewModels.syncDistNumViewModel
    val uiWindowState = settingViewModel.uiWindowState
    val density = LocalDensity.current
    val listState = rememberLazyListState()
    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)
    val context = LocalContext.current
    val selectedVisite = distNumViewModel.selectedVisite
    val clientList = mainViewModel.clientList
    val clientByCode = clientList.firstOrNull { it.cLICode == clientId } ?: Client()//mainViewModel.clientByCode
    val scope = rememberCoroutineScope()
    val state = distNumViewModel.state
    val isConnected = networkViewModel.isConnected
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val listOrder = state.listOrder
    val searchTextState = distNumViewModel.searchTextState
    val showSearchView = distNumViewModel.showSearchView

    val lignesVisitesState = getProCaisseDataViewModel.lignesVisitesState
    val visitesState = getProCaisseDataViewModel.visitesState

    val listFilter = state.filter
    val filterList = context.resources.getStringArray(R.array.visite_filter)

    LaunchedEffect(key1 = searchTextState.text, key2 = state.lists, key3 = state.filter) {
        distNumViewModel.filterVisites(state)
    }
    LaunchedEffect(key1 = clientByCode) {
        distNumViewModel.onEvent(ListEvent.ListSearch(ListSearch.ThirdSearch()))
        distNumViewModel.onSearchValueChange(TextFieldValue(clientByCode.cLICode))
    }


    NavDrawer (
        navigate = { navigate(it) },
        drawer = drawer,
        navDrawerViewmodel = navigationDrawerViewModel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        mainViewModel = mainViewModel,
        dataViewModel = dataViewModel,
        syncInventoryViewModel = syncInventoryViewModel,
        syncSharedViewModels = syncSharedViewModels,
        syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingViewModel
        ) {
        Scaffold(
            topBar = {
                AppBar(
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    onNavigationClick = {
                        scope.launch { drawer.open() }
                    },
                    showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                    title = stringResource(id = navigationDrawerViewModel.proCaisseSelectedMenu.title),
                    titleVisibilty = !showSearchView && searchTextState.text.isEmpty(),
                    actions = {
                        SearchSectionComposable(
                            label = context.getString(R.string.filter_by,
                                when (listFilter) {
                                    is ListSearch.FirstSearch -> filterList[0]
                                    is ListSearch.SecondSearch -> filterList[1]
                                    else -> filterList[2]}),
                            searchVisibility  = showSearchView || searchTextState.text.isNotEmpty(),
                            searchTextState = searchTextState,
                            onSearchValueChange = {
                                distNumViewModel.onSearchValueChange(TextFieldValue(it))
                                if(it == "")   {
                                    /** this bloc to handle search visibility when custom search by client*/
                                    /** this bloc to handle search visibility when custom search by client*/
                                 //   mainViewModel.onSelectedClientChange(Client())
                                }
                            },
                            onShowSearchViewChange = {
                                distNumViewModel.onShowSearchViewChange(it)
                                if(!it) {
                                    /** this bloc to handle search visibility when custom search by client*/
                                    /** this bloc to handle search visibility when custom search by client*/
                                    distNumViewModel.onSearchValueChange(TextFieldValue(""))
                                   // mainViewModel.onSelectedClientChange(Client())
                                }
                            },
                            onShowCustomFilterChange = {
                                distNumViewModel.onShowCustomFilterChange(it)
                            }
                        )


                    }
                )
            },
            floatingActionButton = {
                SnapScrollingButton(
                    isScrollInProgress = listState.isScrollInProgress,
                    isVisible = remember { derivedStateOf { listState.firstVisibleItemIndex } }.value > 15,
                    density = density,
                    animateScrollToItem = {
                        listState.animateScrollToItem(index = it)
                    }
                )
            }
        ) { padding ->
            Column(
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding)
            ) {

                if (distNumViewModel.showCustomModalBottomSheet) {
                    CustomModalBottomSheet(
                        remoteResponseState = syncDistNumViewModel.responseAddBatchVisiteDnState,
                        title = selectedVisite.vIS_Num,
                        showPrintIcon = false,
                        status = selectedVisite.status,
                        onDismissRequest= {
                            distNumViewModel.onShowCustomModalBottomSheetChange(false)
                            distNumViewModel.onSelectedVisiteChange(emptyMap())
                        },
                        onDeleteRequest= {
                            (distNumViewModel::setIsDeletedVisite)(selectedVisite)
                        },
                        onPrintRequest= {},
                        onSyncRequest = {
                            syncDistNumViewModel.syncVisitesDn(notSyncVisites = distNumViewModel.selectedVisiteWithLinesMap)
                        }

                    )
                }

                if (distNumViewModel.showCustomFilter) {
                    FilterContainer(
                        filterList = filterList,
                        listFilter = listFilter,
                        listOrder = listOrder,
                        orderList = context.resources.getStringArray(R.array.visite_order),
                        onShowCustomFilterChange  = {
                            distNumViewModel.onShowCustomFilterChange(false)
                        },
                        onEvent = {
                            distNumViewModel.onEvent(event = it)
                        }
                    )
                }


                if(visitesState.loading)
                    LottieAnim(lotti = R.raw.loading, size = 250.dp)
                else {
                    if (state.lists.isNotEmpty()) {
                        VisiteList (
                            listState = listState,
                            navigate = { navigate(it) },
                            popBackStack = { popBackStack() },
                            isConnected = isConnected,
                            selectedBaseconfig = selectedBaseconfig,
                            clientList = clientList,
                            getProCaisseDataViewModel = getProCaisseDataViewModel,
                            distNumViewModel = distNumViewModel,
                            filteredList = state.lists,
                            visitesState = visitesState,
                        lignesVisitesState = lignesVisitesState,
                            locationViewModule = locationViewModule
                        )
                    } else LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
                }

            }
        }
    }
}

@Composable
fun VisiteList (
    listState: LazyListState,
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    isConnected: Boolean,
    visitesState: RemoteResponseState<List<VisitesDn>>,
    lignesVisitesState: RemoteResponseState<List<LigneVisitesDn>>,
    selectedBaseconfig: BaseConfig,
    clientList: List<Client>,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    distNumViewModel: DistributionNumeriqueViewModel,
    filteredList: Map<VisitesDn, List<LigneVisitesDn>>,
    locationViewModule: LocationViewModule
) {

    val visitesDn: MutableList<VisitesDn> = arrayListOf()
    val ligneVisitesDn: MutableList<LigneVisitesDn> = arrayListOf()

    filteredList.forEach { (key, value) ->
        run {
            visitesDn.add(key)
            ligneVisitesDn.addAll(value)
        }
    }

    val isRefreshing  = visitesState.loading || lignesVisitesState.loading

    PullToRefreshLazyColumn(
        items = visitesDn,
        lazyListState = listState,
        isRefreshing = isRefreshing,
        pullToRefreshEnabled = !visitesDn.any { !it.isSync } && isConnected,
        onRefresh = {
            getProCaisseDataViewModel.getVisitesDn(
                baseConfig = selectedBaseconfig,
                clientList = clientList
            )

            getProCaisseDataViewModel.getLignesVisitesDn(
                baseConfig = selectedBaseconfig
            )
        },
        key = { visiteDn -> visiteDn.id },
        content = { visiteDn ->
            ListItem(
                onItemClick={
                    locationViewModule.resetCurrentLocation()
                    distNumViewModel.onSelectedVisiteChange(filteredList.filter { it.key == visiteDn })


                    distNumViewModel.onModifyVisiteChange(false)

                    navigate(AddModifyDigitalDistributionRoute(clientId = visiteDn.vIS_CodeClient?: ""))
                },
                firstText = visiteDn.vIS_Num,
                secondText = stringResource(id = R.string.prospect, (visiteDn.vIS_NomClient?.ifEmpty { visiteDn.vIS_CodeClient })?: visiteDn.vIS_CodeClient?: "N/A"),
                thirdText =  stringResource(id = R.string.gerant, visiteDn.vIS_NomGerant),
                forthText = stringResource(id = R.string.magasin, visiteDn.vIS_NomMagazin?: "N/A"),
                dateText = if(visiteDn.vIS_Date != null) visiteDn.vIS_Date!! else  "N/A" ,
                isSync = visiteDn.isSync,
                status = visiteDn.status,
                onResetDeletedClick = {
                    distNumViewModel.restDeletedVisite(visiteDn)
                },
                onMoreClick = {
                    distNumViewModel.onSelectedVisiteChange(filteredList.filter { it.key == visiteDn })
                    distNumViewModel.onShowCustomModalBottomSheetChange(true)
                }
            )
        },
    )


}


