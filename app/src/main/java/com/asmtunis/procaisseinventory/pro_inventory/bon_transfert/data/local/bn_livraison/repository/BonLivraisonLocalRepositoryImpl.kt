package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.bn_livraison.repository

import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.BonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraisonWithArticle
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.bn_livraison.dao.BonLivraisonDAO
import kotlinx.coroutines.flow.Flow

class BonLivraisonLocalRepositoryImpl(
    private val bonLivraisonDAO: BonLivraisonDAO,
) : BonLivraisonLocalRepository {
    override fun upsertAll(value: List<BonLivraison>) = bonLivraisonDAO.insertAll(value)

    override fun upsert(value: BonLivraison) = bonLivraisonDAO.insert(value)

    override fun deleteAll() = bonLivraisonDAO.deleteAll()

    override fun delete(bonLivraison: BonLivraison) = bonLivraisonDAO.delete(bonLivraison = bonLivraison)

    override fun getNewCode(prefix: String): Flow<String> = bonLivraisonDAO.getNewCode(prefix)

    override fun updateState(
        code: String,
        codeM: String,
        exercice: String,
    ) = bonLivraisonDAO.updateState(code = code, codeM = codeM, exercice = exercice)

    override fun getAll(): Flow<List<BonLivraison>> = bonLivraisonDAO.all

    override fun count(): Flow<Int> = bonLivraisonDAO.count()

    override fun getNotSync(): Flow<List<BonLivraison>> = bonLivraisonDAO.nonSync

    override fun noSynced(): Flow<Map<BonLivraison, List<LigneBonLivraison>>?> = bonLivraisonDAO.noSynced()

    override fun filterByCodeStationDestination(
        searchString: String,
        filterByStationSource: String,
        filterByStationDestination: String,
        filterByEtatBnTransfert: String,
        sortBy: String,
        isAsc: Int,
    ): Flow<Map<BonLivraison, List<LigneBonLivraisonWithArticle>>> =
        bonLivraisonDAO.filterByCodeStationDestination(
            searchString = searchString,
            filterByStationSource = filterByStationSource,
            filterByStationDestination = filterByStationDestination,
            filterByEtatBnTransfert = filterByEtatBnTransfert,
            sortBy = sortBy,
            isAsc = isAsc,
        )

    override fun filterByCodeStationSource(
        searchString: String,
        filterByStationSource: String,
        filterByStationDestination: String,
        filterByEtatBnTransfert: String,
        sortBy: String,
        isAsc: Int,
    ): Flow<Map<BonLivraison, List<LigneBonLivraisonWithArticle>>> =
        bonLivraisonDAO.filterByCodeStationSource(
            searchString = searchString,
            filterByStationSource = filterByStationSource,
            filterByStationDestination = filterByStationDestination,
            filterByEtatBnTransfert = filterByEtatBnTransfert,
            sortBy = sortBy,
            isAsc = isAsc,
        )

    override fun filterByBontransfertNum(
        searchString: String,
        filterByStationSource: String,
        filterByStationDestination: String,
        filterByEtatBnTransfert: String,
        sortBy: String,
        isAsc: Int,
    ): Flow<Map<BonLivraison, List<LigneBonLivraisonWithArticle>>> =
        bonLivraisonDAO.filterByBontransfertNum(
            searchString = searchString,
            filterByStationSource = filterByStationSource,
            filterByStationDestination = filterByStationDestination,
            filterByEtatBnTransfert = filterByEtatBnTransfert,
            sortBy = sortBy,
            isAsc = isAsc,
        )

    override fun getAllFiltred(
        isAsc: Int,
        filterByStationSource: String,
        filterByStationDestination: String,
        filterByEtatBnTransfert: String,
        sortBy: String,
    ): Flow<Map<BonLivraison, List<LigneBonLivraisonWithArticle>>> =
        bonLivraisonDAO.getAllFiltred(
            isAsc = isAsc,
            filterByStationSource = filterByStationSource,
            filterByStationDestination = filterByStationDestination,
            filterByEtatBnTransfert = filterByEtatBnTransfert,
            sortBy = sortBy,
        )
}
