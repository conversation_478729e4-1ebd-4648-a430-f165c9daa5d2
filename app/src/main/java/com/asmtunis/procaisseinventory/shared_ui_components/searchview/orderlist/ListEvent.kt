package com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist

import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder

sealed class ListEvent {
    data class Order(val listOrder: ListOrder) : ListEvent()
    data class Delete(val mainTable: Any) : ListEvent()
    data class FirstCustomFilter(val firstFilter: String) : ListEvent()
    data class SecondCustomFilter(val secondFiter: String) : ListEvent()
    data class ThirdCustomFilter(val thirdFilter: String) : ListEvent()
    data class ListSearch(val listSearch: com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch) : ListEvent()
    data object Restore : ListEvent()
   // object ToggleOrderSection : ListEvent()
}
