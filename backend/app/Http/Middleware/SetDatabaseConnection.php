<?php

namespace App\Http\Middleware;

use App\Helpers\DatabaseConnection;
use Closure;

class SetDatabaseConnection
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {

        $data = $request->json()->all();
        DatabaseConnection::setConnection($data);
        return $next($request);
    }
}
