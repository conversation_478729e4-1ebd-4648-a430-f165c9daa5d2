package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.text_validation

import com.asmtunis.countrycodepicker.data.CountryData
import com.asmtunis.countrycodepicker.data.utils.getDefaultLangCode
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.FamilleDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.LigneVisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.SuperficieDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypePointVenteDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypeServicesDn
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ConcurrentVC
import java.util.Locale


data class AddVisiteFormState(

    val nomGerant: String = "",
    val nomGerantError: UiText? = null,

    val nomMagasin: String = "",
    val nomMagasinError: UiText? = null,

    val phone1: String = "",
    val phone1Error: UiText? = null,

    val gouvernorat: String = "",
    val gouvernoratError: UiText? = null,

    val delegation: String = "",
    val delegationError: UiText? = null,

    val typeService: TypeServicesDn = TypeServicesDn(),
    val typeServiceError: UiText? = null,

    val typePtVente: TypePointVenteDn = TypePointVenteDn(),
    val typePtVenteError: UiText? = null,

    val superficie: SuperficieDn = SuperficieDn(),
    val superficieError: UiText? = null,

    val fournisseur: ConcurrentVC = ConcurrentVC(),
    val fournisseurError: UiText? = null,

    val familleProduit: FamilleDn = FamilleDn(),
    val familleProduitError: UiText? = null,

    val ligneVisitesDn: List<LigneVisitesDn> = emptyList(),
    val ligneVisitesDnError: UiText? = null,

    val countryData: CountryData = CountryData(getDefaultLangCode().lowercase(Locale.getDefault())),

    val adresse: String = "",
    val adresseError: UiText? = null,


    val phone: String = "",
    val phoneError: UiText? = null,

    val longitude: String = "",
    val longitudeError: UiText? = null,

    val latitude: String = "",
    val latitudeError: UiText? = null,

    val remarque: String = "",
    val remarqueError: UiText? = null

)
