package com.asmtunis.procaisseinventory.shared_ui_components.swipe_actions.swipe_to_delete

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.SwipeToDismissBox
import androidx.compose.material3.SwipeToDismissBoxState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SwipeToDeleteContainer(
    canModify: Boolean,
    state: SwipeToDismissBoxState,
    content: @Composable () -> Unit
) {
        if(canModify) {
            SwipeToDismissBox(
                state = state,
                enableDismissFromEndToStart = true,
                enableDismissFromStartToEnd = true,
                backgroundContent = {
                    DeleteBackground (
                        swipeDismissState = state
                    )
                },
                content = {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .scale(if(state.progress == 1f)  1f else 1f- state.progress)
                    ) {


                        content()
                    }
                }
            )
        }

        else content()








}