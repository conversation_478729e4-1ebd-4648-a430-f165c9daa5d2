<?php

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

use App\Helpers\DatabaseConnection;
use App\Http\Controllers\PrefixWSMobile;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::any('settings/connectivity', 'MiscWSMobile@checkConnectivity');
Route::any('settings/migrate', 'MiscWSMobile@migrate');
Route::any('settings/refresh', 'MiscWSMobile@refresh');
Route::any('test', function (Request $request) {
    /*    $table = "FDSFJ";
        //    dd(base64_encode(file_get_contents($image->getRealPath())));

        $items = $request->json()->all();
        foreach ($items["object"] as $data) {
            $image = $data["Image"];
            unset($data["Image"]);
            $image = str_replace('data:image/png;base64,', '', $image);
            $image = str_replace(' ', '+', $image);
            $imageName = str_random(10) . '.' . 'png';
            $storage = Storage::disk('proCaisse_path');
            $storage->put($data["Code_IMG"] . $imageName, base64_decode($image));
            $data["Chemin_Img"] = $storage->getDriver()->getAdapter()->getPathPrefix() . $data["Code_IMG"] . $imageName;
            VCImage::query()->updateOrCreate(["Code_IMG" => $data["Code_IMG"]], $data);
        }
        return json_encode(true);*/

    /*   $disk = Storage::disk('proCaisse_path');

       return $disk->download("E:\adb\ProCaisse\storage\app/public\Img_0002/oxIXsGS7K6.png");*/
    //  $url = VCImage::all();
    // //   echo Storage::disk('proCaisse_path')->download($url[1]->Chemin_Img);
    //  return response()->json($url);
   /* $items = $request->json()->all();
    $connection= DatabaseConnection::setConnection($items);


    if (!empty($items)) {
        foreach ($items["object"] as &$data) {
             app()->make(\App\Http\Controllers\TicketWSMobile::class)->callAction('addTraites', [$data,$connection,]);
        }
    }
    $rate_limiter = new Cschalenborgh\RateLimiter\RateLimiter('action_name', 5, 60);

    if ($rate_limiter->check($request->ip())) {


        return "jknkj";
    } else {
        // oops.. limit reached
        return response()->json("dfvdfv");
    }*/

    $data = $request->json()->all();
    $connection = DatabaseConnection::setConnection($data);



    $prefix = new PrefixWSMobile();
    $year = Carbon::now()->format('y');
    $month = Carbon::now()->format('m');
    $prefix_genere  = $prefix->getFacturePrefix($connection,$year,$month );
    return response()->json($prefix_genere);


});

Route::get('debug-sentry', function () {
    throw new Exception('My first Sentry error!');
});


