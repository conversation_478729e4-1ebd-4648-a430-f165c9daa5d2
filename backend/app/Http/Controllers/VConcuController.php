<?php

namespace App\Http\Controllers;

use App\Helpers\AppHelper;
use App\Helpers\DatabaseConnection;
use App\Helpers\DB;
use App\Helpers\Enum;
use App\Models\VCImage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class VConcuController extends Controller
{

    // Liste des Images de la table VC_image par codeTypeVC (optionnel)
    public function getVCImage(Request $request)
    {
        $data = $request->json()->all();
        //dd($request->query('codeTypeVC'));
        $connection = DatabaseConnection::setConnection($data);
        $query = VCImage::query();
        if ($request->hasAny('codeTypeVC') && !!$request->query('codeTypeVC')) {
            $query = $query->where('Code_TypeVC', $request->query('codeTypeVC'));
        }
        return response()->json($query->get());

    }

    // Liste de tous les Concurrent  de la table VCListeConcurrent
    public function getVCListeConcurrent(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('VCListeConcurrent')->get());
    }

    // Liste de tous les Type Communication  de la table VCTypeCommunication
    public function getVCTypeCommunication(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('VCTypeCommunication')->get());

    }

    // Liste des VConcurentielle selon un table donne au paramétre(VCAutre,VCPrix,VCPromo,VCLancementNP) et par codeUser(optionnel)
    public function getDataVConcu(Request $request, $table)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        try {
            $query = $connection->table($table);
            if ($request->hasAny('user') && !!$request->query('user')) {
                $query = $query->where('CodeUser', $request->query('user'));
            }
            return response()->json($query->get());
        } catch (\Exception $e) {
        }
        return response()->json(array());
    }

    // Liste des VConcurentielle selon un table donne au paramétre(VCAutre,VCPrix,VCPromo,VCLancementNP) et par Code
    public function getDataVConcuByCode(Request $request, $table)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $primaryKeys = DB::connection()
            ->getDoctrineSchemaManager()
            ->listTableDetails($table)
            ->getPrimaryKey();
        $key = $primaryKeys->getColumns();
        return response()->json($connection->table($table)->where($key[0], $data["object"])->first());
    }

    // Insertion d'un objet VConcurentielle selon un table donne au paramétre(VCAutre,VCPrix,VCPromo,VCLancementNP)
    public function addDataVConcu(Request $request, $table)
    {
        $data = $request->json()->all();

        if (!empty($data)) {
            $connection = DatabaseConnection::setConnection($data);

            $primaryKeys = DB::connection()
                ->getDoctrineSchemaManager()
                ->listTableDetails($table)
                ->getPrimaryKey();
            $key = $primaryKeys->getColumns();
            $prefixController = new PrefixWSMobile();
            $codeVC = $prefixController->getPrefixVconcu($data,
                $table,
                $request->header('user'));

            $data["object"][$key[0]] = $codeVC;
            if ($table == 'VCLancementNP') {
                $data["object"]['PrixLanP'] = floatval($data["object"]['PrixLanP']);
                $data["object"]['TauxPromo'] = floatval($data["object"]['TauxPromo']);
            } else if ($table == 'VCPromo') {
                $data["object"]['PrixConcur'] = floatval($data["object"]['PrixConcur']);
                $data["object"]['TauxPromo'] = floatval($data["object"]['TauxPromo']);
            } else if ($table == 'VCPrix') {
                $data["object"]['PrixConcur'] = floatval($data["object"]['PrixConcur']);
            }

            $c = $connection->table($table)->insert($data["object"]);
            if ($c) {
                return response()->json($data['object']);
            } else {
                return response()->json(null);
            }

        } else {
            return response()->json(null);
        }
    }

    // Suppression d'un objet VConcurentielle selon un table donne au paramétre(VCAutre,VCPrix,VCPromo,VCLancementNP) et par code
    public function deleteDataVConcu(Request $request)
    {
        if ($request->hasAny('table') && !!$request->query('table')) {
            $table = $request->query('table');

            $data = $request->json()->all();
            $items = $data['object'];
            $connection = DatabaseConnection::setConnection($data);
            $primaryKeys = DB::connection()
                ->getDoctrineSchemaManager()
                ->listTableDetails($table)
                ->getPrimaryKey();
            $key = $primaryKeys->getColumns();

            $deleteVCImage = true;
            $result = array();
            foreach ($items as &$item) {

                if ($table === 'VCLancementNP') {
                    $codeM = $connection->table($table)
                        ->select('CodeVCLanPM')
                        ->where($key[0], $item[$key[0]])->first();
                    $VCImage = $connection->table('VC_Image')->where('Code_TypeVC', $codeM->CodeVCLanPM)->first();
                    if (!empty($VCImage)) {
                        $deleteVCImage = $connection->table('VC_Image')->where('Code_TypeVC', $codeM->CodeVCLanPM)->delete();
                    }

                } else if ($table === 'VCPromo') {
                    $codeM = $connection->table($table)
                        ->select('CodeVCPromoM')
                        ->where($key[0], $item[$key[0]])->first();
                    $VCImage = $connection->table('VC_Image')->where('Code_TypeVC', $codeM->CodeVCPromoM)->first();
                    if (!empty($VCImage)) {
                        $deleteVCImage = $connection->table('VC_Image')->where('Code_TypeVC', $codeM->CodeVCPromoM)->delete();
                    }

                } else if ($table === 'VCPrix') {
                    $codeM = $connection->table($table)
                        ->select('CodeVCPrixM')
                        ->where($key[0], $item[$key[0]])->first();
                    $VCImage = $connection->table('VC_Image')->where('Code_TypeVC', $codeM->CodeVCPrixM)->first();
                    if (!empty($VCImage)) {
                        $deleteVCImage = $connection->table('VC_Image')->where('Code_TypeVC', $codeM->CodeVCPrixM)->delete();
                    }

                } else if ($table === 'VCAutre') {
                    $codeM = $connection->table($table)
                        ->select('Code_Mob')
                        ->where($key[0], $item[$key[0]])->first();
                    $VCImage = $connection->table('VC_Image')->where('Code_TypeVC', $codeM->Code_Mob)->first();
                    if (!empty($VCImage)) {
                        $deleteVCImage = $connection->table('VC_Image')->where('Code_TypeVC', $codeM->Code_Mob)->delete();

                    }

                }
                $delete = false;
                if ($deleteVCImage) {
                    $delete = $connection->table($table)->where($key[0], $item[$key[0]])->delete();
                }

                if ($delete) {
                    $result[] = [
                        $key[0] => $item[$key[0]],
                        'table' => $table,
                        'message' => 'DELETED',
                        'code' => Enum::Success_Code
                    ];
                } else {
                    $result[] = [
                        $key[0] => $item[$key[0]],
                        'table' => $table,
                        'message' => 'NOT DELETED',
                        'code' => Enum::Success_Code
                    ];
                }
            }
            return response()->json($result);
        }
    }

    // Insertion ou modification  d'un ou plusieurs objet VConcurentielle selon un table donne au paramétre(VCAutre,VCPrix,VCPromo,VCLancementNP)
    public function addBatchDataVConcu(Request $request)
    {
        $items = $request->json()->all();
        $connection = DatabaseConnection::setConnection($items);

        if ($request->hasAny('table') && !!$request->query('table')) {

            $table = $request->query('table');
            if (!empty($items)) {

                $primaryKeys = $connection
                    ->getDoctrineSchemaManager()
                    ->listTableDetails($table)
                    ->getPrimaryKey();

                $key = $primaryKeys->getColumns();

                $result = null;
                $tab = null;
                foreach ($items["object"] as $data) {

                    if (!$this->isExistCode($key[0], $data[$key[0]], $table, $connection)) {

                        $item = $data;
                        $prefixController = new PrefixWSMobile();

                        $codeVC = $prefixController->getPrefixVconcu($items,
                            $table,
                            $request->header('user'));

                        $item[$key[0]] = $codeVC;
                        if (isset($data['DateOp'])) {
                            $item['DateOp'] = AppHelper::setDateFormat($data['DateOp']);
                        }
                        if ($table == 'VCLancementNP') {
                            $item['PrixLanP'] = floatval($item['PrixLanP']);
                            $item['TauxPromo'] = floatval($item['TauxPromo']);
                            $tab['codeM'] = $item['CodeVCLanPM'];
                        } else if ($table == 'VCPromo') {
                            $item['PrixConcur'] = floatval($item['PrixConcur']);
                            $item['TauxPromo'] = floatval($item['TauxPromo']);
                            $tab['codeM'] = $item['CodeVCPromoM'];
                        } else if ($table == 'VCPrix') {
                            $item['PrixConcur'] = floatval($item['PrixConcur']);
                            $tab['codeM'] = $item['CodeVCPrixM'];
                        } else if ($table == 'VCAutre') {

                            $tab['codeM'] = $item['Code_Mob'];
                        }


                        $tab['code'] = $codeVC;
                        $tab['table'] = $table;


                        $connection->table($table)->insert($item);

                    } else {

                        $item = $data;
                        $tab['code'] = $item[$key[0]];
                        if ($table == 'VCAutre') {
                            $connection->table('VCAutre')->where("CodeAutre", $data["CodeAutre"])->update([

                                "Autre" => $data["Autre"] ?? null,
                                "AutreNote" => $data["AutreNote"] ?? null,
                                "DateOp" => $data["DateOp"] ?? null,
                                "CodeConcur" => $data["CodeConcur"] ?? null,
                                "NoteOp" => $data["NoteOp"] ?? null,
                                'CodeUser' => $request->header('user'),
                                "InfoOp1" => $data["InfoOp1"] ?? null,
                                "CodeTypeCom" => $data["CodeTypeCom"] ?? null,
                                "ACodClt" => $data["ACodClt"] ?? null,
                                "ALatitude" => $data["ALatitude"] ?? null,
                                "ALongitude" => $data["ALongitude"] ?? null,

                            ]);
                            $tab['codeM'] = $data["Code_Mob"];
                        } else if ($table == 'VCLancementNP') {
                            $connection->table('VCLancementNP')->where("CodeVCLanP", $data["CodeVCLanP"])->update([
                                "CodeVCLanPM" => $data["CodeVCLanPM"] ?? null,
                                "ProduitLanP" => $data["ProduitLanP"] ?? null,
                                "DateOp" => $data["DateOp"] ?? null,
                                "CodeConcur" => $data["CodeConcur"] ?? null,
                                "NoteOp" => $data["NoteOp"] ?? null,
                                "PrixLanP" => floatval($data['PrixLanP']) ?? null,
                                "TauxPromo" => floatval($data['TauxPromo']) ?? null,
                                'CodeUser' => $request->header('user'),
                                "InfoOp1" => $data["InfoOp1"] ?? null,
                                "CodeTypeCom" => $data["CodeTypeCom"] ?? null,
                                "LNPCodClt" => $data["LNPCodClt"] ?? null,
                                "LNPLatitude" => $data["LNPLatitude"] ?? null,
                                "LNPLongitude" => $data["LNPLongitude"] ?? null,
                            ]);
                            $tab['codeM'] = $data["CodeVCLanPM"];

                        } else if ($table == 'VCPromo') {
                            $connection->table('VCPromo')->where("CodeVCPromo", $data["CodeVCPromo"])->update([
                                "CodeVCPromoM" => $data["CodeVCPromoM"] ?? null,
                                "CodeArtLocal" => $data["CodeArtLocal"] ?? null,
                                "ArticleConcur" => $data["ArticleConcur"] ?? null,
                                "DateOp" => $data["DateOp"] ?? null,
                                "CodeConcur" => $data["CodeConcur"] ?? null,
                                "NoteOp" => $data["NoteOp"] ?? null,
                                "PrixConcur" => floatval($data['PrixConcur']) ?? null,
                                "TauxPromo" => floatval($data['TauxPromo']) ?? null,
                                'CodeUser' => $request->header('user'),
                                "InfoOp1" => $data["InfoOp1"] ?? null,
                                "CodeTypeCom" => $data["CodeTypeCom"] ?? null,
                                "PrCodClt" => $data["PrCodClt"] ?? null,
                                "PrLatitude" => $data["PrLatitude"] ?? null,
                                "PrLongitude" => $data["PrLongitude"] ?? null,
                            ]);
                            $tab['codeM'] = $data["CodeVCPromoM"];
                        } else if ($table == 'VCPrix') {
                            $connection->table('VCPrix')->where("CodeVCPrix", $data["CodeVCPrix"])->update([
                                "CodeVCPrixM" => $data["CodeVCPrixM"] ?? null,
                                "CodeArtLocal" => $data["CodeArtLocal"] ?? null,
                                "ArticleConcur" => $data["ArticleConcur"] ?? null,
                                "DateOp" => $data["DateOp"] ?? null,
                                "CodeConcur" => $data["CodeConcur"] ?? null,
                                "NoteOp" => $data["NoteOp"] ?? null,
                                "PrixConcur" => floatval($data['PrixConcur']) ?? null,
                                'CodeUser' => $request->header('user'),
                                "InfoOp1" => $data["InfoOp1"] ?? null,
                                "CodeTypeCom" => $data["CodeTypeCom"] ?? null,
                                "PCodClt" => $data["PCodClt"] ?? null,
                                "PLatitude" => $data["PLatitude"] ?? null,
                                "PLongitude" => $data["PLongitude"] ?? null,
                            ]);
                            $tab['codeM'] = $data["CodeVCPrixM"];
                        }


                        $tab['table'] = $table;

                    }
                    $result[] = $tab;
                }
            }

        }
        return response()->json($result);
    }


    public function isExistCode($key, $codeAutre, $table, $connection)
    {
        return $connection->table($table)
            ->where($key, $codeAutre)->first();
    }


    public function upload(Request $request)
    {
        $result = [];
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        foreach ($data["object"] as $item) {
            $connection->beginTransaction();
            try {
                $image = $item["Image"];
                unset($item["Image"]);
                $image = str_replace('data:image/png;base64,', '', $image);
                $image = str_replace(' ', '+', $image);
                $imageName = str_random(10) . '.' . 'png';
                $storage = (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') ? Storage::disk('proCaisse_path') : Storage::disk('public');
                $storage->put($item["Code_IMG"] . '/' . $imageName, base64_decode($image));
                $item["Chemin_Img"] = $storage->getDriver()->getAdapter()->getPathPrefix() . $item["Code_IMG"] . '/' . $imageName;
                $inserted = $connection->table('VC_Image')->updateOrInsert(["Code_IMG" => $item["Code_IMG"]], $item);

                if (!$inserted) {
                    $result[] = $this->setGlobalResult("Code_IMG",$item["Code_IMG"],
                        "Erreur dans l'insertion d'image VCon", Enum::Error_Upload_Image_VC
                    );
                    Log::error("Erreur dans l'insertion d'image VCon", $result);
                    throw new Exception("", Enum::Error_Upload_Image_VC);
                } else {
                    $result[] = $this->setGlobalResult("Code_IMG",$item["Code_IMG"],
                        "INSERTED", Enum::Success_Code
                    );
                }
                $connection->commit();
            } catch (\Exception|\Throwable $e) {
                $connection->rollBack();
            }
        }
        return json_encode($result);
    }

    public function getImages(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $query = VCImage::query();
        if ($request->hasAny('id') && !!$request->input('id')) {
            $query->where('Code_TypeVC', '=', $request->input('id'));
        }
        return response()->json($query->get());
    }


}

