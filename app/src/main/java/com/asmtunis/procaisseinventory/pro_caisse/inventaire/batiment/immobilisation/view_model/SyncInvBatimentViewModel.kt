package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.view_model

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.sync.SyncEntity
import com.asmtunis.procaisseinventory.core.sync.SyncManager
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentCheck
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentCheckResponse
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject


@HiltViewModel
class SyncInvBatimentViewModel @Inject constructor(
    private val syncManager: SyncManager,
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val listenNetwork: ListenNetwork,
    // app: Application
) : ViewModel() {

    // Sync state from SyncManager
    val syncState = syncManager.syncState

    private var autoSyncState  by mutableStateOf(false)

    private var autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(key = PROCAISSE_AUTO_SYNC_AUTHORISATION, default = true).distinctUntilChanged()

    private var  connected  by mutableStateOf(false)

    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()

    init {
            getNotSyncBatimentCodeBare()

    }




    var batimentCBNotSync: List<Immobilisation> by mutableStateOf(emptyList())
        private set



    private fun getNotSyncBatimentCodeBare() {

        viewModelScope.launch {
            val batimentCBNotSyncFlow = proCaisseLocalDb.immobilisation.getNotSync().distinctUntilChanged()


            combine(networkFlow, batimentCBNotSyncFlow, autoSyncFlow) { isConnected, batimentCBNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                batimentCBNotSyncList.ifEmpty { emptyList() }
            }.collect {
                if (it.isEmpty()) {
                    batimentCBNotSync = emptyList()
                    return@collect
                }
                batimentCBNotSync = it
                if(connected && autoSyncState && it.first().cliImoCB!= null)   syncAffectCodeBareBatiment(batimentCheck = BatimentCheck(
                    cLICode = it.first().cLICode,
                    cltImoCB = it.first().cliImoCB!!
                )
                )
            }
        }
    }



    var responseAffectCodeBareBatimentState: RemoteResponseState<BatimentCheckResponse>  by mutableStateOf(RemoteResponseState())
        private set

    var notSyncAffectCodeBareBatimentObj : String by mutableStateOf("")
        private set
    fun resetResponseAffectCodeBareBatimentState() {
        responseAffectCodeBareBatimentState = RemoteResponseState()
    }

    fun syncAffectCodeBareBatiment(batimentCheck: BatimentCheck, showErrorResult: Boolean = false) {
        Log.d("SyncInvBatimentVM", "syncAffectCodeBareBatiment - Network connected: $connected")

        viewModelScope.launch(dispatcherIO) {
            // Check network state first - prioritize offline mode when offline
            if (!connected) {
                Log.d("SyncInvBatimentVM", "Network offline - using offline affectation")
                verifyPatLocaly(batimentCheck = batimentCheck, error = null, showErrorResult = showErrorResult)
                return@launch
            }

            // Network is available - try remote affectation
            Log.d("SyncInvBatimentVM", "Network online - attempting remote affectation")

            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()?.let { Json.decodeFromString(it) }?: BaseConfig(),
                Json.encodeToJsonElement(batimentCheck)
            )

            notSyncAffectCodeBareBatimentObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.inventaireBatiment.affectCodeBareBatiment(notSyncAffectCodeBareBatimentObj).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                       val response = result.data!!
                       Log.d("SyncInvBatimentVM", "Remote affectation success: ${response.code}")

                        if(response.code == 10708)
                            responseAffectCodeBareBatimentState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = null,
                                message = response.message
                            )
                        else {
                            responseAffectCodeBareBatimentState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null,
                                message = response.message
                            )

                          proCaisseLocalDb.immobilisation.setZoneConsomationImmoCB(
                              immoCB = batimentCheck.cltImoCB,
                              cliCode = batimentCheck.cLICode,
                              isSynced = true,
                              status = ItemStatus.SELECTED.status
                          )
                        }
                    }

                    is DataResult.Loading -> {
                        responseAffectCodeBareBatimentState = RemoteResponseState(data = null, loading = true, error = null, message = null)
                    }

                    is DataResult.Error -> {
                        Log.e("SyncInvBatimentVM", "Remote affectation failed - falling back to local: ${result.message}")
                        verifyPatLocaly(batimentCheck = batimentCheck, error = result.message, showErrorResult = showErrorResult)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }
    private suspend fun verifyPatLocaly(batimentCheck: BatimentCheck, error: String?, showErrorResult: Boolean) {
        proCaisseLocalDb.invePatrimoine.getByNumSerie(batimentCheck.cltImoCB).collect { listImmobilisation ->
                if (listImmobilisation.isNullOrEmpty()) {
                    affectCodeBareBatimentOffline(batimentCheck = batimentCheck, error = error, showErrorResult = showErrorResult)
                   return@collect // Continue to the next emission, isEmpty stays true
                } else {


                    responseAffectCodeBareBatimentState = RemoteResponseState(
                        data = null,
                        loading = false,
                        error = error,
                        message = if(showErrorResult) "Déjà affecté: '" + listImmobilisation.keys.first().dEVNum + "' (Local vérification)" else null
                    )

                    return@collect // Stop collecting (optimization, assuming first emission is representative)
                }
            }

       // return isEmpty // Return true if the list was empty, false otherwise
    }
    private fun affectCodeBareBatimentOffline(batimentCheck: BatimentCheck, error: String?, showErrorResult: Boolean) {
        Log.d("SyncInvBatimentVM", "affectCodeBareBatimentOffline - CB: ${batimentCheck.cltImoCB}, Client: ${batimentCheck.cLICode}")

        viewModelScope.launch(dispatcherIO) {
            try {
                proCaisseLocalDb.immobilisation.getAllZoneConsomationByImoCB(imoCB = batimentCheck.cltImoCB).cancellable().collectLatest {
                    cancel()

                    Log.d("SyncInvBatimentVM", "Existing affectations found: ${it?.size ?: 0}")

                    //if showErrorResult show message
                    if(it.isNullOrEmpty()) {
                        Log.d("SyncInvBatimentVM", "No existing affectation - creating new offline affectation")

                        responseAffectCodeBareBatimentState = RemoteResponseState(
                            data = null,
                            loading = false,
                            error = error,
                            message = if(showErrorResult) "Code à Barres: ${batimentCheck.cltImoCB} valide (Offline)" else null
                        )

                        // Save offline affectation with sync flag set to false
                        proCaisseLocalDb.immobilisation.setZoneConsomationImmoCB(
                            immoCB = batimentCheck.cltImoCB,
                            cliCode = batimentCheck.cLICode,
                            isSynced = false,
                            status = ItemStatus.INSERTED_CODE_BARE.status
                        )

                        Log.d("SyncInvBatimentVM", "Offline affectation saved successfully")
                        return@collectLatest
                    }

                    // Handle existing affectations
                    Log.d("SyncInvBatimentVM", "Existing affectation found - preventing duplicate")

                    responseAffectCodeBareBatimentState = if(it.size>=2) {
                        RemoteResponseState(
                            data = null,
                            loading = false,
                            error = error,
                            message = if(showErrorResult) "Code à Barres " +batimentCheck.cltImoCB+ " affecté à "+ it.size.toString() + " zone de consomation (Offline)" else null
                        )

                    } else {
                        RemoteResponseState(
                            data = null,
                            loading = false,
                            error = error,
                            message = if(showErrorResult) "Code à Barres: ${batimentCheck.cltImoCB} déja affecté à "+ it.first().cLINomPren + " (Offline)" else null
                        )
                    }
                }
            } catch (e: Exception) {
                Log.e("SyncInvBatimentVM", "Error in affectCodeBareBatimentOffline: ${e.message}")
                responseAffectCodeBareBatimentState = RemoteResponseState(
                    data = null,
                    loading = false,
                    error = "Erreur lors de l'affectation offline: ${e.message}",
                    message = null
                )
            }
        }
    }

    // ========================================
    // SYNCMANAGER INTEGRATION & CONVENIENCE METHODS
    // ========================================

    /**
     * Trigger manual sync for all Batiment entities using SyncManager
     */
    fun triggerManualSyncAll() {
        viewModelScope.launch {
            try {
                val result = syncManager.syncEntity(SyncEntity.INVENTAIRE_BATIMENT)
                if (!result.isSuccess && batimentCBNotSync.isNotEmpty()) {
                    // Fallback to individual sync methods if SyncManager fails
                    batimentCBNotSync.firstOrNull()?.let { immobilisation ->
                        if (immobilisation.cliImoCB != null) {
                            syncAffectCodeBareBatiment(
                                BatimentCheck(
                                    cLICode = immobilisation.cLICode,
                                    cltImoCB = immobilisation.cliImoCB!!
                                )
                            )
                        }
                    }
                }
            } catch (exception: Exception) {
                // Fallback to individual sync methods on exception
                batimentCBNotSync.firstOrNull()?.let { immobilisation ->
                    if (immobilisation.cliImoCB != null) {
                        syncAffectCodeBareBatiment(
                            BatimentCheck(
                                cLICode = immobilisation.cLICode,
                                cltImoCB = immobilisation.cliImoCB!!
                            )
                        )
                    }
                }
            }
        }
    }

    /**
     * Check if sync is currently in progress
     */
    val isSyncing: Boolean
        get() = responseAffectCodeBareBatimentState.loading

    /**
     * Get the current sync error if any
     */
    val syncError: String?
        get() = responseAffectCodeBareBatimentState.error

    /**
     * Get the count of unsynchronized items
     */
    val unsyncedCount: Int
        get() = batimentCBNotSync.size

    /**
     * Reset all sync states to initial state
     */
    fun resetAllSyncStates() {
        responseAffectCodeBareBatimentState = RemoteResponseState()
    }

    /**
     * Get sync status summary for UI display
     */
    val syncStatusSummary: String
        get() = when {
            isSyncing -> "Synchronisation bâtiment en cours..."
            syncError != null -> "Erreur de synchronisation: $syncError"
            unsyncedCount > 0 -> "$unsyncedCount codes à barres bâtiment non synchronisés"
            else -> "Tous les codes à barres bâtiment sont synchronisés"
        }
}