<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use App\Http\Requests;
use Illuminate\Http\Request;

class ParametrageWSMobile extends Controller
{
    public function getParametrage(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $result = $connection->table('parametrage')->first();
        $result->PARAM_Logo = base64_encode($result->PARAM_Logo);
        return response()->json($result);
    }


    public function getAuthorisation(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('AutorisationUser')->
        join('Autorisation', 'Autorisation.AutoCode',
            '=', 'AutorisationUser.AutoCodeAu')
            ->where('Autorisation.AutoTypeMenu', 'like', sprintf("%%%s%%",
                preg_replace("/\s+/", "", $request->hasHeader('Application-name') && !empty($request->header('Application-name')) ?
                    $request->header('Application-name') : 'ASM')))
            ->where('AutEtat', true)->get());
    }


}





