package com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util

sealed class ListOrder(val orderType: OrderType) {
    class Title(orderType: OrderType): ListOrder(orderType)
    class Date(orderType: OrderType): ListOrder(orderType)
    class Third(orderType: OrderType): ListOrder(orderType)

    fun copy(orderType: OrderType): ListOrder {
        return when(this) {
            is Title -> Title(orderType)
            is Date -> Date(orderType)
            is Third -> Third(orderType)
        }
    }
}


