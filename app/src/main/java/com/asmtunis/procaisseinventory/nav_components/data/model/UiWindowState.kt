package com.asmtunis.procaisseinventory.nav_components.data.model

import androidx.compose.material3.windowsizeclass.WindowSizeClass
import androidx.window.layout.DisplayFeature
import com.asmtunis.procaisseinventory.core.utils.DevicePosture
import com.asmtunis.procaisseinventory.core.utils.ReplyContentType
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationContentPosition
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType

data class UiWindowState(
    var windowSize: WindowSizeClass? = null,
    var displayFeatures: List<DisplayFeature> = emptyList(),
    var navigationType: ReplyNavigationType =  ReplyNavigationType.NAVIGATION_DRAWER,
    var contentType: ReplyContentType = ReplyContentType.SINGLE_PANE,
    var foldingDevicePosture: DevicePosture = DevicePosture.NormalPosture,
    var navigationContentPosition: ReplyNavigationContentPosition = ReplyNavigationContentPosition.TOP
)
