package com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.domaine

import com.asmtunis.procaisseinventory.core.model.BaseModel
import com.asmtunis.procaisseinventory.data.cheque_caisse.domaine.ChequeCaisse
import com.asmtunis.procaisseinventory.data.ticket_resto.domaine.TraiteCaisse
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import kotlinx.serialization.SerialName


@kotlinx.serialization.Serializable
data class TicketWithLinesAndPayments (
    @SerialName("ticket")
    var ticket: Ticket? = null,

    @SerialName("lignesTicket")
    var ligneTicket: List<LigneTicket>? = null,

    @SerialName("cheques")
    var cheques: List<ChequeCaisse>? = null,

    @SerialName("traites")
    var traites: List<TraiteCaisse>? = null,

    @SerialName("reglement")
    var reglement: ReglementCaisse? = null

): BaseModel()

