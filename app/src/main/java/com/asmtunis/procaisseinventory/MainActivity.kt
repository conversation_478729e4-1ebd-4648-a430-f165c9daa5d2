@file:OptIn(ExperimentalMaterial3Api::class)

package com.asmtunis.procaisseinventory

import android.content.BroadcastReceiver
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import com.asmtunis.blovedream.BlovedreamPDAUtils.mScanReceiver
import com.asmtunis.blovedream.BlovedreamPDAUtils.registerBlovedreamPDAReceiver
import com.asmtunis.blovedream.BlovedreamPDAUtils.releaseBlovedreamPDA
import com.asmtunis.blovedream.BlovedreamPDAUtils.startBlovedreamPDA
import com.asmtunis.blovedream.BlovedreamPDAUtils.unregisterBlovedreamPDAReceiver
import com.asmtunis.honeywellbarcodereader.BarCodeReaderManager
import com.asmtunis.honeywellbarcodereader.barCodeReaderScanner
import com.asmtunis.procaisseinventory.core.DeviceManufacture.BLD
import com.asmtunis.procaisseinventory.core.DeviceManufacture.HONEYWELL
import com.asmtunis.procaisseinventory.core.DeviceManufacture.POINTMOBILE
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProInventory
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.BluetoothUtil
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.nav_graph.AppNavigation
import com.asmtunis.procaisseinventory.core.sync_workmanager.SyncWorkerViewModel
import com.asmtunis.procaisseinventory.core.utils.CheckForUpdate.checkForUpdate
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.CameraUtils.checkCamera
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.theme.AppTheme
import com.asmtunis.procaisseinventory.shared_ui_components.theme.ChangeSystemBarsTheme
import com.asmtunis.procaisseinventory.shared_ui_components.theme.SetAdaptiveLayoutParams
import com.asmtunis.procaisseinventory.shared_ui_components.theme.ThemeValues
import dagger.hilt.android.AndroidEntryPoint
import java.util.Locale

@AndroidEntryPoint
class MainActivity : ComponentActivity() {




    private val settingViewModel: SettingViewModel by viewModels()
    private val dataViewModel: DataViewModel by viewModels()
    private val barCodeViewModel: BarCodeViewModel by viewModels()
    private val bluetoothVM: BluetoothViewModel by viewModels()
    private val syncWorkerVM: SyncWorkerViewModel by viewModels()
    private val navigationDrawerViewModel: NavigationDrawerViewModel by viewModels()


    private var barCodeReader: BarCodeReaderManager? = null

    private val manufacturer = Build.MANUFACTURER?:""


    private val mScanReceiverBlovedreamPDA: BroadcastReceiver? =
        if(manufacturer != BLD) null else mScanReceiver(
        onReceive = {
            val grantedAuthorizationList = barCodeViewModel.grantedAuthorizationList
            val canScanBarCode = grantedAuthorizationList.contains(AuthorizationValuesProInventory.SCAN_BARECODE)
            val isProInventory = navigationDrawerViewModel.isProInventory

            if(!canScanBarCode && isProInventory) {
                Toast.makeText(this@MainActivity, this.getString(R.string.no_authorisation_scan_code_bar), Toast.LENGTH_LONG).show()
                return@mScanReceiver
            }
            var barcodeStr = ""
            val barcode = it.getByteArrayExtra("barocode")
            val barcodeLen = it.getIntExtra("length", 0)
            val type = it.getStringExtra("barcodeType")
            val aimID = it.getStringExtra("aimid")

            barcodeStr = String(barcode!!, 0, barcodeLen)


            val bareCode = BareCode(
                value = barcodeStr,
               // barecodetype = if(type != null) if(keepNumbers(str = type).isNotEmpty()) keepNumbers(str = type).toInt() else -1 else -1,
                barecodetype = type?: "",
                aim = aimID?:""
            )
           // onBarCodeInfo(bareCode)
        barCodeViewModel.onBarCodeInfo(barCode = bareCode)
    }
        )


    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)


        if(manufacturer == BLD) startBlovedreamPDA()



        installSplashScreen().apply {
            /*  setKeepOnScreenCondition() {
                //    viewModel.isLoading.value
                         authViewModel.proCaisseState.value.loading ||
                        authViewModel.proInventoryState.value.loading ||
                        authViewModel.proCaisseLicence.loading ||
                        authViewModel.proInventoryLicence.loading
            }*/
        }



        checkForUpdate(activity = this@MainActivity)

        checkCamera(activity = applicationContext, dataViewModel = dataViewModel)

        //check BT
       BluetoothUtil.checkBT(bluetoothVM = bluetoothVM, context = applicationContext)

//        val isBlueToothAvailable = checkBluetoothAvailability(bluetoothVM = bluetoothVM, context = applicationContext)
//        // ask to enable BT
//        val enableBluetoothLauncher = registerForActivityResult(
//            ActivityResultContracts.StartActivityForResult()
//        ) { /* Not needed */ }




        if (intent.getBooleanExtra(Globals.FROM_NOTIFICATION, false)) {
            Log.d("dgrfsffdgs", Globals.FROM_NOTIFICATION)

        }
        else   Log.d("dgrfsffdgs", "false " + Globals.FROM_NOTIFICATION)

        setContent {
            val selectedTheme = settingViewModel.selectedTheme
            val darkTheme = if(selectedTheme == ThemeValues.SYSTEM_THEME.theme || selectedTheme == ""){
                isSystemInDarkTheme()
            }
            else   selectedTheme == ThemeValues.DARK.theme


            settingViewModel.onIsDarkThemeChange(darkTheme)

                    AppTheme(darkTheme = settingViewModel.isDarkTheme) {
                ChangeSystemBarsTheme(activity = this@MainActivity, lightTheme = !settingViewModel.isDarkTheme)


            SetAdaptiveLayoutParams(
                activity = this@MainActivity,
                onUiWindowStateChange =  {
                    settingViewModel.onUiWindowStateChange(it)
                }
            )
                

                    AppNavigation(
                        intent = intent,
                        dataViewModel = dataViewModel,
                        settingViewModel = settingViewModel,
                        barCodeViewModel = barCodeViewModel,
                        bluetoothVM = bluetoothVM,
                        syncWorkerVM = syncWorkerVM,
                        navigationDrawerViewModel = navigationDrawerViewModel
                    )


            }
        }
    }



    override fun onResume() {
        super.onResume()
        if(manufacturer == BLD) registerBlovedreamPDAReceiver(mScanReceiverBlovedreamPDA = mScanReceiverBlovedreamPDA)



      else if(manufacturer == HONEYWELL || manufacturer.lowercase(Locale.ROOT) == POINTMOBILE.lowercase(Locale.ROOT)) {
          barCodeReader = BarCodeReaderManager()

          barCodeReaderScanner(
              activity = this@MainActivity,
              barCodeReader = barCodeReader,
              ifSuccess = {
                  val grantedAuthorizationList = barCodeViewModel.grantedAuthorizationList
                  val canScanBarCode = grantedAuthorizationList.contains(AuthorizationValuesProInventory.SCAN_BARECODE)
                  val isProInventory = navigationDrawerViewModel.isProInventory
                  if(!canScanBarCode && isProInventory) {
                      Toast.makeText(this@MainActivity, this.getString(R.string.no_authorisation_scan_code_bar), Toast.LENGTH_LONG).show()
                      return@barCodeReaderScanner
                  }


                  if(it != null) {
                      val bareCode = BareCode(value = it)

                      barCodeViewModel.onBarCodeInfo(barCode = bareCode)
                  }
                  else {
                      Toast.makeText(this@MainActivity, "Bare code vide !!!", Toast.LENGTH_LONG).show()

                  }

              },
              ifFail = {
                  Toast.makeText(this@MainActivity, it, Toast.LENGTH_LONG).show()
              }
          )
      }



    }

    override fun onPause() {
        super.onPause()
        if(manufacturer == BLD) unregisterBlovedreamPDAReceiver(mScanReceiverBlovedreamPDA = mScanReceiverBlovedreamPDA)

        else if(manufacturer == HONEYWELL || manufacturer.lowercase(Locale.ROOT) == POINTMOBILE.lowercase(Locale.ROOT))
            barCodeReader?.destroy()

    }


    override fun onDestroy() {
        super.onDestroy()
        if(manufacturer == HONEYWELL || manufacturer.lowercase(Locale.ROOT) == POINTMOBILE.lowercase(Locale.ROOT))
            barCodeReader?.destroy()

      else  if(manufacturer == BLD)   releaseBlovedreamPDA()
    }



    //todo implement onTrimMemory properly to deal with lowmemeory :
    // exp on pda when fetching large data from api

     override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)
        when (level) {
            TRIM_MEMORY_UI_HIDDEN -> {
                // Release resources related to the UI

                Log.d("evdsfdsgvfdsfdfdvvfd", "TRIM_MEMORY_UI_HIDDEN")
            }

            TRIM_MEMORY_BACKGROUND,
//            ComponentCallbacks2.TRIM_MEMORY_MODERATE,
//            ComponentCallbacks2.TRIM_MEMORY_COMPLETE
                 -> {
              //  System.gc()
                // Release large objects, caches, or resources that can be easily recreated
                Log.d("evdsfdsgvfdsfdfdvvfd", "TRIM_MEMORY_BACKGROUND")
            }

//            TRIM_MEMORY_COMPLETE -> {
//                TODO()
//            }
//
//            TRIM_MEMORY_MODERATE -> {
//                TODO()
//            }
//
//            TRIM_MEMORY_RUNNING_CRITICAL -> {
//                TODO()
//            }
//
//            TRIM_MEMORY_RUNNING_LOW -> {
//                TODO()
//            }
//
//            TRIM_MEMORY_RUNNING_MODERATE -> {
//                TODO()
//            }
        }
    }



}


