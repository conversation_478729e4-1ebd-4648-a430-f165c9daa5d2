package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.image.repository


import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.image.dao.ImageVCDAO
import kotlinx.coroutines.flow.Flow



class ImageVCLocalRepositoryImpl(
    private val imageVCDAO: ImageVCDAO
) : ImageVCLocalRepository {


    override fun upsertAll(value: List<ImagePieceJoint>) = imageVCDAO.insertAll(value)


    override fun deleteAll() = imageVCDAO.deleteAll()
    override fun deleteByCodeTypeVc(codeTypeVc: String) = imageVCDAO.deleteByCodeTypeVc(codeTypeVc)

    override fun setDeleted(codeIMG: String) = imageVCDAO.setDeleted(codeIMG)

    override fun getAll(): Flow<List<ImagePieceJoint>?> = imageVCDAO.all
    override fun getNotSynced(): Flow<List<ImagePieceJoint>> = imageVCDAO.noSynced

    override fun getImageByCode(code: String): Flow<List<ImagePieceJoint>>
    = imageVCDAO.getImageByCode(code)

    override fun setImagesSynced(code: String) = imageVCDAO.setImagesSynced(codeM = code)




}