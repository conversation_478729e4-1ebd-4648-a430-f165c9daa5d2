package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.typepointvente.repository


import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypePointVenteDn
import kotlinx.coroutines.flow.Flow

interface TypePointVenteLocalRepository {

    fun upsert(value: TypePointVenteDn)

    fun upsertAll(value: List<TypePointVenteDn>)


    fun deleteAll()

    fun getAll(): Flow<List<TypePointVenteDn>>
}