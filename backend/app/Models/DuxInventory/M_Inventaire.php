<?php

namespace App\Models\DuxInventory;



class M_Inventaire extends BaseModel
{
    protected $connection = 'onthefly';
    protected $table = 'M_Inventaire';
    public $incrementing = false;
    public $timestamps = false;
    protected $guarded = [];
    const DEFAULT_ID_SOURCE = 3;
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->idClasse = 'M_inventaire';
        $this->useCode = false;
    }

    public static function defaultFields()
    {
        return [
            'id', 'Code', 'dateInv', 'j_ddm', 'P_codeExercice', 'idStation',
            'idEtatInventaire', 'j_codeUser', 'dateValidation', 'validePar'
        ];
    }
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->IdSource = self::DEFAULT_ID_SOURCE;
            $prefixe = P_Prefixe::select('compt', 'prefixe')
                ->where('idTable', 'M_inventaire')
                ->first();
            if (!$prefixe) {
                throw new \Exception('No prefix found to generate code');
            }
            $model->code = $prefixe->prefixe . $prefixe->compt;
        });

        static::created(function() {
            P_Prefixe::where('idTable', 'M_inventaire')
                ->increment('compt');
        });

    }


    public function lignesInventaire()
    {
        return $this->hasMany(L_LigneInventaire::class, 'idInventaire');
    }

}
