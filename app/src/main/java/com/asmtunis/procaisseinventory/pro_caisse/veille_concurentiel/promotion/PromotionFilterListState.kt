package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion


import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVCWithImages
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch


data class PromotionFilterListState(
    val lists: List<PromoVCWithImages> = emptyList(),
    val listOrder: ListOrder = ListOrder.Date(OrderType.Descending),
    val search: ListSearch = ListSearch.FirstSearch(),
    val filterByTypeCommunication: String = ""
)