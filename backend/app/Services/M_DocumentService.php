<?php

namespace App\Services;



use App\Models\DuxInventory\L_LigneDocument;
use App\Models\DuxInventory\M_Document;

class M_DocumentService
{
    public function getDocumentsOuLignes($modelClass,$idClasseDocument, $dateFilter, $mois = null)
    {
        $query = $this->buildQuery($modelClass, $idClasseDocument);
        if ($dateFilter) {
            $this->applyDateFilter($query, $mois,$this->getDateField($modelClass));
        }
        return $query->select($modelClass::defaultFields())->get();
    }


    private function buildQuery($modelClass, $idClasseDocument)
    {
        if ($modelClass === M_Document::class) {
            return M_Document::where('idClasseDocument', $idClasseDocument);
        } elseif ($modelClass === L_LigneDocument::class) {
            return L_LigneDocument::leftJoin('M_Document as d', 'L_LigneDocument.idDocument', '=', 'd.id')
                ->where('d.idClasseDocument', $idClasseDocument);
        }
    }

    private function getDateField($modelClass)
    {
        return $modelClass === M_Document::class ? 'M_Document.dateDocument' : 'L_LigneDocument.dateLigne';
    }

    private function applyDateFilter($query, $filterMois, $dateField)
    {
        $dateDebutMoisPrecedent = date('Y-m-01', strtotime('-' . $filterMois . 'months'));
        if ($filterMois != "") {
            $query->where($dateField, '<=', date('Y-m-d 23:59:59'))
                ->where($dateField, '>=', $dateDebutMoisPrecedent);
        } else {
            $moisCourant = date('Y-m');
            $query->whereRaw("FORMAT($dateField, 'yyyy-MM') = '$moisCourant'");
        }
    }


}