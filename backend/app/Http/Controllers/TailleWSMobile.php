<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use DB;

class TailleWSMobile extends Controller
{
    	
	
	
	public function getTailleByCode(Request $request)
	 {
		  return response()->json(DB::table('Taille')->where('TAI_Taille', $request->id)->first());
		 
	 }
	
	
	/*fonction get data from database*/
	 
	public function getTaille()
	 {
	$taille = DB::table('Taille')->get();
  		  //afficher tous les lignes
  		  foreach($taille as $T){
				
		  $data[]=['TAI_Taille'=>$T->TAI_Taille,
			   'TAI_Station'=>$T->TAI_Station,
			   'TAI_User'=>$T->TAI_User,
			   'T_Station'=>$T->T_Station
			   ];
		 	 
          }
		  
		  return $data;
	 }
	
	
	public function getTailleByX(Request $request)
	 {
		  return response()->json(DB::table('Taille')->where($request->field, $request->value)->get());
		 
	 }
	
	 
	 public function addTaille(Request $request)
    {
                $data = $request->json()->all();
		
		
		if ( !empty ( $data ) ) {
		$T = 	DB::table('Taille')->insert($data);
		
			return response()->json($T);
			
			}
		else{
			return(false);
		}
			
		
    }
	 

		
	public function updateTaille(Request $request )
    {
         $data = $request->json()->all();
	
		
		
		DB::table('Taille')->where('TAI_Taille',$data["TAI_Taille"])->update($data);
		
		return("Data modified");
		
    }

	
	public function deleteTaille(Request $request)
    {
              
		DB::table('Taille')->where('TAI_Taille', $request->id)->delete();
		return("Data Deleted");		
    }

	
}
