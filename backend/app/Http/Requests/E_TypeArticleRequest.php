<?php

namespace App\Http\Requests;



use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Symfony\Component\HttpFoundation\Response;

class E_TypeArticleRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $baseRules = parent::rules();

        $additionalRules = [
            'object' => 'required|array',
            'object.*.AllowChangePrix' => 'nullable|boolean',
            'object.*.affect_CMP' => 'nullable|boolean',
            'object.*.affect_DPA' => 'nullable|boolean',
            'object.*.familleObligatoire' => 'nullable|boolean',
            'object.*.idStationE' => 'required|string|max:255',
            'object.*.idStationS' => 'required|string|max:255',
            'object.*.periodique' => 'nullable|boolean',
        ];
        return array_merge($baseRules, $additionalRules);
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY)
        );
    }
}
