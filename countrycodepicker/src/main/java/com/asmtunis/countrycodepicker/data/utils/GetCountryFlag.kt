package com.asmtunis.countrycodepicker.data.utils

import com.asmtunis.asm.countrycodepicker.R


fun getFlags(countryName: String): Int {
    return when (countryName) {
        "ad" -> R.drawable.ad
        "ae" -> R.drawable.ae
        "af" -> R.drawable.af
        "ag" -> R.drawable.ag
        "ai" -> R.drawable.ai
        "al" -> R.drawable.al
        "am" -> R.drawable.am
        "ao" -> R.drawable.ao
        "aq" -> R.drawable.aq
        "ar" -> R.drawable.ar
        "as" -> R.drawable.`as`
        "at" -> R.drawable.at
        "au" -> R.drawable.au
        "aw" -> R.drawable.aw
        "ax" -> R.drawable.ax
        "az" -> R.drawable.az
        "ba" -> R.drawable.ba
        "bb" -> R.drawable.bb
        "bd" -> R.drawable.bd
        "be" -> R.drawable.be
        "bf" -> R.drawable.bf
        "bg" -> R.drawable.bg
        "bh" -> R.drawable.bh
        "bi" -> R.drawable.bi
        "bj" -> R.drawable.bj
        "bl" -> R.drawable.bl
        "bm" -> R.drawable.bm
        "bn" -> R.drawable.bn
        "bo" -> R.drawable.bo
        "br" -> R.drawable.br
        "bs" -> R.drawable.bs
        "bt" -> R.drawable.bt
        "bw" -> R.drawable.bw
        "by" -> R.drawable.by
        "bz" -> R.drawable.bz
        "ca" -> R.drawable.ca
        "cc" -> R.drawable.cc
        "cd" -> R.drawable.cd
        "cf" -> R.drawable.cf
        "cg" -> R.drawable.cg
        "ch" -> R.drawable.ch
        "ci" -> R.drawable.ci
        "ck" -> R.drawable.ck
        "cl" -> R.drawable.cl
        "cm" -> R.drawable.cm
        "cn" -> R.drawable.cn
        "co" -> R.drawable.co
        "cr" -> R.drawable.cr
        "cu" -> R.drawable.cu
        "cv" -> R.drawable.cv
        "cw" -> R.drawable.cw
        "cx" -> R.drawable.cx
        "cy" -> R.drawable.cy
        "cz" -> R.drawable.cz
        "de" -> R.drawable.de
        "dj" -> R.drawable.dj
        "dk" -> R.drawable.dk
        "dm" -> R.drawable.dm
        "do" -> R.drawable.ic_do
        "dz" -> R.drawable.dz
        "ec" -> R.drawable.ec
        "ee" -> R.drawable.ee
        "eg" -> R.drawable.eg
        "er" -> R.drawable.er
        "es" -> R.drawable.es
        "et" -> R.drawable.et
        "fi" -> R.drawable.fi
        "fj" -> R.drawable.fj
        "fk" -> R.drawable.fk
        "fm" -> R.drawable.fm
        "fo" -> R.drawable.fo
        "fr" -> R.drawable.fr
        "ga" -> R.drawable.ga
        "gb" -> R.drawable.gb
        "gd" -> R.drawable.gd
        "ge" -> R.drawable.ge
        "gf" -> R.drawable.gf
        "gg" -> R.drawable.gg
        "gh" -> R.drawable.gh
        "gi" -> R.drawable.gi
        "gl" -> R.drawable.gl
        "gm" -> R.drawable.gm
        "gn" -> R.drawable.gn
        "gp" -> R.drawable.gp
        "gq" -> R.drawable.gq
        "gr" -> R.drawable.gr
        "gt" -> R.drawable.gt
        "gu" -> R.drawable.gu
        "gw" -> R.drawable.gw
        "gy" -> R.drawable.gy
        "hk" -> R.drawable.hk
        "hn" -> R.drawable.hn
        "hr" -> R.drawable.hr
        "ht" -> R.drawable.ht
        "hu" -> R.drawable.hu
        "id" -> R.drawable.id
        "ie" -> R.drawable.ie
        "il" -> R.drawable.il
        "im" -> R.drawable.im
        "is" -> R.drawable.`is`
        "in" -> R.drawable.`in`
        "io" -> R.drawable.io
        "iq" -> R.drawable.iq
        "ir" -> R.drawable.ir
        "it" -> R.drawable.it
        "je" -> R.drawable.je
        "jm" -> R.drawable.jm
        "jo" -> R.drawable.jo
        "jp" -> R.drawable.jp
        "ke" -> R.drawable.ke
        "kg" -> R.drawable.kg
        "kh" -> R.drawable.kh
        "ki" -> R.drawable.ki
        "km" -> R.drawable.km
        "kn" -> R.drawable.kn
        "kp" -> R.drawable.kp
        "kr" -> R.drawable.kr
        "kw" -> R.drawable.kw
        "ky" -> R.drawable.ky
        "kz" -> R.drawable.kz
        "la" -> R.drawable.la
        "lb" -> R.drawable.lb
        "lc" -> R.drawable.lc
        "li" -> R.drawable.li
        "lk" -> R.drawable.lk
        "lr" -> R.drawable.lr
        "ls" -> R.drawable.ls
        "lt" -> R.drawable.lt
        "lu" -> R.drawable.lu
        "lv" -> R.drawable.lv
        "ly" -> R.drawable.ly
        "ma" -> R.drawable.ma
        "mc" -> R.drawable.mc
        "md" -> R.drawable.md
        "me" -> R.drawable.me
        "mf" -> R.drawable.mf
        "mg" -> R.drawable.mg
        "mh" -> R.drawable.mh
        "mk" -> R.drawable.mk
        "ml" -> R.drawable.ml
        "mm" -> R.drawable.mm
        "mn" -> R.drawable.mn
        "mo" -> R.drawable.mo
        "mp" -> R.drawable.mp
        "mq" -> R.drawable.mq
        "mr" -> R.drawable.mr
        "ms" -> R.drawable.ms
        "mt" -> R.drawable.mt
        "mu" -> R.drawable.mu
        "mv" -> R.drawable.mv
        "mw" -> R.drawable.mw
        "mx" -> R.drawable.mx
        "my" -> R.drawable.my
        "mz" -> R.drawable.mz
        "na" -> R.drawable.na
        "nc" -> R.drawable.nc
        "ne" -> R.drawable.ne
        "nf" -> R.drawable.nf
        "ng" -> R.drawable.ng
        "ni" -> R.drawable.ni
        "nl" -> R.drawable.nl
        "no" -> R.drawable.no
        "np" -> R.drawable.np
        "nr" -> R.drawable.nr
        "nu" -> R.drawable.nu
        "nz" -> R.drawable.nz
        "om" -> R.drawable.om
        "pa" -> R.drawable.pa
        "pe" -> R.drawable.pe
        "pf" -> R.drawable.pf
        "pg" -> R.drawable.pg
        "ph" -> R.drawable.ph
        "pk" -> R.drawable.pk
        "pl" -> R.drawable.pl
        "pm" -> R.drawable.pm
        "pn" -> R.drawable.pn
        "pr" -> R.drawable.pr
        "ps" -> R.drawable.ps
        "pt" -> R.drawable.pt
        "pw" -> R.drawable.pw
        "py" -> R.drawable.py
        "qa" -> R.drawable.qa
        "re" -> R.drawable.re
        "ro" -> R.drawable.ro
        "rs" -> R.drawable.rs
        "ru" -> R.drawable.ru
        "rw" -> R.drawable.rw
        "sa" -> R.drawable.sa
        "sb" -> R.drawable.sb
        "sc" -> R.drawable.sc
        "sd" -> R.drawable.sd
        "se" -> R.drawable.se
        "sg" -> R.drawable.sg
        "sh" -> R.drawable.sh
        "si" -> R.drawable.si
        "sk" -> R.drawable.sk
        "sl" -> R.drawable.sl
        "sm" -> R.drawable.sm
        "sn" -> R.drawable.sn
        "so" -> R.drawable.so
        "sr" -> R.drawable.sr
        "ss" -> R.drawable.ss
        "st" -> R.drawable.st
        "sv" -> R.drawable.sv
        "sx" -> R.drawable.sx
        "sy" -> R.drawable.sy
        "sz" -> R.drawable.sz
        "tc" -> R.drawable.tc
        "td" -> R.drawable.td
        "tg" -> R.drawable.tg
        "th" -> R.drawable.th
        "tj" -> R.drawable.tj
        "tk" -> R.drawable.tk
        "tl" -> R.drawable.tl
        "tm" -> R.drawable.tm
        "tn" -> R.drawable.tn
        "to" -> R.drawable.to
        "tr" -> R.drawable.tr
        "tt" -> R.drawable.tt
        "tv" -> R.drawable.tv
        "tw" -> R.drawable.tw
        "tz" -> R.drawable.tz
        "ua" -> R.drawable.ua
        "ug" -> R.drawable.ug
        "us" -> R.drawable.us
        "uy" -> R.drawable.uy
        "uz" -> R.drawable.uz
        "va" -> R.drawable.va
        "vc" -> R.drawable.vc
        "ve" -> R.drawable.ve
        "vg" -> R.drawable.vg
        "vi" -> R.drawable.vi
        "vn" -> R.drawable.vn
        "vu" -> R.drawable.vu
        "wf" -> R.drawable.wf
        "ws" -> R.drawable.ws
        "xk" -> R.drawable.xk
        "ye" -> R.drawable.ye
        "yt" -> R.drawable.yt
        "za" -> R.drawable.za
        "zm" -> R.drawable.zm
        "zw" -> R.drawable.zw
        else -> R.drawable.tr
    }
}