<?php

namespace App\Models\DuxInventory;


class P_Modele extends BaseModel
{
    protected $table = 'P_Modele';
    protected $connection = 'onthefly';
    public $incrementing = false;
    public $timestamps = false;
    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->idClasse = 'Modele';
        $this->useCode = true;

    }
}
