package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.liste_concurrent.repository


import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ConcurrentVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.liste_concurrent.dao.ListeConcurrentVCDAO
import kotlinx.coroutines.flow.Flow



class ListConcurrentVCLocalRepositoryImpl(
    private val listeConcurrentVCDAO: ListeConcurrentVCDAO
) : ListConcurrentVCLocalRepository {


    override fun upsertAll(value: List<ConcurrentVC>) = listeConcurrentVCDAO.insertAll(value)


    override fun deleteAll() = listeConcurrentVCDAO.deleteAll()

    override fun getAll(): Flow<List<ConcurrentVC>?> = listeConcurrentVCDAO.all

}