<?php

namespace App\Http\Controllers;

use App\Helpers\AppHelper;
use App\Helpers\DatabaseConnection;
use Illuminate\Http\Request;

class ConfigWSMobile extends Controller
{

    private $dates = ["MM/dd/yyyy HH:mm:ss" => "10-23-2021", "dd-MM-yyyy HH:mm:ss" => "23-10-2021", "yyyy-MM-dd HH:mm:ss" => "2021-10-23"];

    public function getCollation(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        try {
            $result = $connection->select($connection->raw("SELECT CONVERT (varchar(256), DATABASEPROPERTYEX('ReportServer','collation')) AS collation"));
            $result = sizeof($result) > 0 ? $result[0]->collation : null;
        } catch (\Exception $e) {
            $result = null;
        }
        return response()->json($result);
    }

    public function getDateTimeFormat(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        $dateFormat = "MM/dd/yyyy HH:mm:ss";

        /*        try {
                    $result = $connection->select($connection->raw("SELECT CONVERT (varchar(256), DATABASEPROPERTYEX('ReportServer','collation')) AS collation"));
                    $result = sizeof($result) > 0 ? $result[0]->collation : null;
                } catch (\Exception $e) {
                    $result = null;
                }
                switch ($result) {
                    case "SQL_Latin1_General_CP1_CI_AS":
                    case "Latin1_General_CI_AS_KS_WS":
                        $dateFormat = "yyyy-MM-dd HH:mm:ss";
                        break;
                    default:
                        $dateFormat = "dd-MM-yyyy HH:mm:ss";
                }*/
        try {
            $connection->table('Utilisateur')->select('*')->where("DDm", "=", "10-23-2021")->get();
            $dateFormat = "MM/dd/yyyy HH:mm:ss";
        } catch (\Exception $e) {
            if ($e->getCode() == "22007") {
                $dateFormat = "yyyy-MM-dd HH:mm:ss";
            }
        }
        return response()->json($dateFormat);

    }


    public function getConfig(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        try {
            $result = $connection->select($connection->raw("SELECT CONVERT (varchar(256), DATABASEPROPERTYEX('ReportServer','collation')) AS collation"));
            $result = sizeof($result) > 0 ? $result[0]->collation : null;
        } catch (\Exception $e) {
            $result = null;
        }
    //    $dateFormat = "MM/dd/yyyy HH:mm:ss";
        /*
                $dateFormat = "dd-MM-yyyy HH:mm:ss";
                switch ($result) {
                    case "SQL_Latin1_General_CP1_CI_AS":
                    case "Latin1_General_CI_AS_KS_WS":
                        $dateFormat = "yyyy-MM-dd HH:mm:ss";
                        break;
                    default:
                        $dateFormat = "dd-MM-yyyy HH:mm:ss";
                }*/

  /*      try {
            $connection->table('Utilisateur')->select('*')->where("DDm", "=", "10/23/2021")->get();
            $dateFormat = "MM/dd/yyyy HH:mm:ss";
        } catch (\Exception $e) {
            if ($e->getCode() == "22007") {
                $dateFormat = "yyyy-MM-dd HH:mm:ss";
            }
        }*/
        /*      config(['app.date_format' => $dateFormat]);

              if (env("FORCED_DATE_FORMAT", false)) {
                  switch (env("DATE_FORMAT", "Y-m-d H:i:s")) {
                      case "d-m-Y H:i:s":
                          $dateFormat = "dd-MM-yyyy HH:mm:ss";
                          break;
                      case "m/d/Y H:i:s":
                          $dateFormat = "MM/dd/yyyy HH:mm:ss";
                          break;
                      case "Y-m-d H:i:":
                      default:
                          $dateFormat = "yyyy-MM-dd HH:mm:ss";
                          break;
                  }
              }*/

        $dateFormat = AppHelper::getDateFormat($connection);

        return response()->json([
            "date" => $dateFormat, "collation" => $result]);
    }

}
