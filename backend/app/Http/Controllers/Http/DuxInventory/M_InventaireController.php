<?php

namespace App\Http\Controllers\Http\DuxInventory;

use App\Helpers\DatabaseConnection;
use App\Http\Requests\M_InventaireRequest;
use App\Models\DuxInventory\BaseModel;
use App\Models\DuxInventory\E_EtatInventaire;
use App\Models\DuxInventory\L_LigneInventaire;
use App\Models\DuxInventory\M_Inventaire;
use App\Models\DuxInventory\P_Exercice;
use App\Models\DuxInventory\P_Prefixe;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;


class M_InventaireController extends BaseController
{
    public function __construct()
    {
        $this->modelClass = M_Inventaire::class;
        $this->ligneModelClass = L_LigneInventaire::class;
        $this->requestClass = M_InventaireRequest::class;
        $this->parentIdField = 'idInventaire';
    }

    public function getInventaires()
    {
        try {
            $year = P_Exercice::where('active','1')->value('code');
            if (!$year) {
            throw new \Exception('Exercice code not found in response');
            }
            $inventaires = M_Inventaire::whereYear('P_codeExercice', $year)
                ->select(M_Inventaire::defaultFields())
                ->get();
            return response()->success($inventaires);
        } catch (\Exception $e) {
            return response()->error($e);
        }
    }


    public function addInventaire(Request $request)
    {
        try {
            $utilisateur = (new P_UtilisateurController())->getUtilisateurById($request)->original;
            $exercice = (new P_ExerciceController())->getExercice($request)->original;
            $tz_object = new \DateTimeZone('Africa/Tunis');
            $datetime = new \DateTime();
            $datetime->setTimezone($tz_object);
            //$this->validateData($request);
            $data = [
                'id' => (new BaseModel())->getID(),
                'version' => 0,
                'Code' => $this->getNumInv(),
                'dateInv' => $request["object"]["dateInv"],
                'j_operation' => 'Insertion',
                'j_export' => '0',
                'j_ddm' => $datetime->format('Y-m-d H:i:s'),
                'P_codeExercice' => $exercice['data']->code,
                'P_codeStation' => $utilisateur['data']->P_codeStation,
                'idStation' => $request["object"]["idStation"],
                'idEtatInventaire' => $request["object"]["idEtatInventaire"],
                'j_codeUser' => $request["object"]["j_codeUser"],
                'IdSource' => '2',
            ];

            return M_Inventaire::create($data);
        } catch (\Exception $e) {
            throw new \Exception("Error adding the inventory" . $e->getMessage());
        }
    }

    public function addBatchInventairesWithLines(Request $request)
    {
        try {

            $data = $request->json()->all();
            $connection = DatabaseConnection::setConnection($data);
            $connection->beginTransaction();

            $inventaire = $this->addInventaire($request);

            $dataLignes = $data["object"]['listeArticles'];
            foreach ($dataLignes as $dataligne) {
                $dataligne['idInventaire'] = $inventaire['id'];
                $lignesInventaire[] = (new L_LigneInventaireController())->addLigneInventaires($request, $dataligne);
            }
            $inventaire['lignesInventaire'] = $lignesInventaire;
            $connection->commit();
            return response()->json(['status' => 'success', 'data' => $inventaire], Response::HTTP_CREATED);
        } catch (ValidationException $e) {
            return response()->json(['errors' => $e->validator->errors()], Response::HTTP_BAD_REQUEST);
        } catch (\Exception $e) {
            $connection->rollBack();
            return response()->json(['status' => 'error', 'message' => 'Error adding the inventory with lines.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getEtatInventaire(Request $request)
    {
        try {
            $etatInventaires = E_EtatInventaire::get();
            return response()->success($etatInventaires);
        } catch (\Exception $e) {
            return response()->error($e);
        }
    }

    public function getNumInv()
    {
        $resp = P_Prefixe::from("P_Prefixe")
            ->select("compt", "prefixe")
            ->where("idTable", 'M_inventaire')->first();
        $num = $resp->compt;
        $prefixe = $resp->prefixe;
        return $prefixe . $num;
    }

}
