<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Symfony\Component\HttpFoundation\Response;

class M_InventaireRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'object' => 'required|array',
            'object.*.dateInv' => 'required|date_format:Y-m-d H:i:s',
            'object.*.idEtatInventaire' => 'required|string',
            'object.*.idStation' => 'required|string',
            'object.*.j_codeUser' => 'required|string',
            'object.*.idMobile' => 'required|string',
            'object.*.lignes' => 'required|array',
            'object.*.lignes.*.id' => 'nullable|string',
            'object.*.lignes.*.version' => 'required|string',
            'object.*.lignes.*.idArticle' => 'required|string',
            'object.*.lignes.*.idCodeABarre' => 'required|string',
            'object.*.lignes.*.qtetheorique' => 'required|numeric',
            'object.*.lignes.*.cmp' => 'required|string',
            'object.*.lignes.*.P_codeExercice' => 'required|string',
            'object.*.lignes.*.j_export' => 'required|boolean',
            'object.*.lignes.*.j_ddm' => 'required',
            'object.*.lignes.*.j_codeStation' => 'required|string',
            'object.*.lignes.*.j_codeUser' => 'required|string',
            'object.*.lignes.*.j_operation' => 'required|string',
            'object.*.lignes.*.isSync' => 'nullable|string',
            'object.*.lignes.*.idMobile' => 'nullable|string',
            'object.*.lignes.*.syncState' => 'nullable|string',
            'object.*.lignes.*.code' => 'nullable|string',
            'object.*.lignes.*.desig' => 'required|string',
            'object.*.lignes.*.prixAchat' => 'required|numeric',
            'object.*.lignes.*.prixvente' => 'required|numeric',
            'object.*.lignes.*.crp' => 'required|numeric',
            'object.*.lignes.*.P_codeStation' => 'required|string',
            'object.*.lignes.*.qtereel' => 'required|numeric',
        ];

    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY)
        );
    }
}
