package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.visite.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.LIGNE_VISITE_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.LigneVisitesDn
import kotlinx.coroutines.flow.Flow


@Dao
interface LigneVisiteDAO {
    @get:Query("SELECT * FROM $LIGNE_VISITE_TABLE")
    val all: Flow<List<LigneVisitesDn>>

    @get:Query("SELECT * FROM $LIGNE_VISITE_TABLE")
    val allList: Flow<List<LigneVisitesDn>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(dnLigneVisite: List<LigneVisitesDn>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(DnSuperficie: LigneVisitesDn)

    @get:Query("SELECT * FROM $LIGNE_VISITE_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ")
    val noSynced: List<LigneVisitesDn>

    @get:Query("SELECT count(*) FROM $LIGNE_VISITE_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val noSyncCountMubtale: Flow<Int>

    @Query("SELECT * FROM $LIGNE_VISITE_TABLE WHERE LG_VISNum = :code")
    fun getByCode(code: String): LigneVisitesDn?

    @Query("SELECT * FROM $LIGNE_VISITE_TABLE WHERE LG_VISNum = :code")
    fun getListByCode(code: String): Flow<List<LigneVisitesDn>>

    @Query("delete from $LIGNE_VISITE_TABLE")
    fun deleteAll()

    @Query("DELETE FROM $LIGNE_VISITE_TABLE where LG_VISNum=:lgVISNum and LG_VISExerc=:exercice")
    fun deleteByVisNum(lgVISNum: String, exercice : String)

    @Delete
    fun delete(dnLigneVisite: LigneVisitesDn)



    @Query("UPDATE $LIGNE_VISITE_TABLE SET LG_VISNum = :code , Status = 'SELECTED' , IsSync = 1 where LG_VISNum = :CodeMobile")
    fun updateLgVisNum(code: String, CodeMobile: String)






}
