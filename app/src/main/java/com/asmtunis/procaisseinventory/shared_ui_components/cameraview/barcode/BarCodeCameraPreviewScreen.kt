package com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode

import android.annotation.SuppressLint
import android.media.AudioManager
import android.media.ToneGenerator
import android.util.Log
import androidx.camera.mlkit.vision.MlKitAnalyzer
import androidx.camera.view.CameraController
import androidx.camera.view.LifecycleCameraController
import androidx.camera.view.PreviewView
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.twotone.ArrowBack
import androidx.compose.material.icons.twotone.QrCodeScanner
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProInventory
import com.asmtunis.procaisseinventory.core.navigation.BarCodeCameraRoute
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.dokar.sonner.ToasterState
import com.dokar.sonner.rememberToasterState
import com.google.mlkit.vision.barcode.BarcodeScanner
import com.google.mlkit.vision.barcode.BarcodeScannerOptions
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.simapps.ui_kit.FlashLightComposable

@Composable
fun BarCodeCameraPreviewScreen(
    navigate: (route: String) -> Unit,
    popBackStack: () -> Unit,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    barCodeViewModel: BarCodeViewModel,
    settingViewModel: SettingViewModel
) {
    val grantedAuthorizationList = barCodeViewModel.grantedAuthorizationList
    val canScanBarCode = grantedAuthorizationList.contains(AuthorizationValuesProInventory.SCAN_BARECODE)

    val isProInventory = navigationDrawerViewModel.isProInventory

    val context = LocalContext.current

   /* val infiniteTransition = rememberInfiniteTransition()
    val infinitelyAnimatedFloat = infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(),
            // The value will infinitely repeat from 0 to 1 and 1 to 0
            repeatMode = RepeatMode.Reverse
        )
    )*/

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)
    Scaffold() { padding ->
        Box(modifier = Modifier.padding(30.dp)) {
            if(!canScanBarCode && isProInventory) {
                Column (
                    modifier = Modifier.fillMaxSize().verticalScroll(rememberScrollState()),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {


                    Icon(
                        modifier = Modifier.size(150.dp),
                        imageVector = Icons.TwoTone.QrCodeScanner,
                      //  tint = MaterialTheme.colorScheme.error,
                        contentDescription = "clear"
                    )
                    Text(
                        modifier = Modifier.padding(top = 60.dp, bottom = 60.dp),
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.titleLarge,
                        color = MaterialTheme.colorScheme.error,
                        text = stringResource(R.string.no_authorisation_scan_code_bar)
                    )

                    IconButton(
                        onClick = { popBackStack() }
                    ) {
                        Icon(
                            modifier = Modifier.size(50.dp),
                            imageVector = Icons.AutoMirrored.TwoTone.ArrowBack,
                            //   tint = MaterialTheme.colorScheme.error,
                            contentDescription = "clear"
                        )
                    }
                }

            } else {
                BareCodeCameraView(
                    toaster = toaster,
                    barCodeViewModel = barCodeViewModel,
                    onImageCapturedAndCorrectCode = {
                        Log.d("AuthenticationCamera", "Scanned QR Code is conformed!")
                        popBackStack()
                    },
                    onImageCapturedButNotCorrectCode = {
                        Log.d("AuthenticationCamera", "Scanned QR Code isn't conformed!")
                        popBackStack()
                    }
                )

                FlashLightComposable(
                    flashOn = barCodeViewModel.flashState,
                    onflashStateChange = {
                        barCodeViewModel.onflashState(it)
                    })

                HorizontalDivider(
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.Center)
                        .width(1.dp),
                    // .alpha(infinitelyAnimatedFloat.value)
                    color = MaterialTheme.colorScheme.error
                )


                Text(
                    modifier = Modifier
                        //  .size(100.dp)
                        .padding(bottom = 10.dp)
                        .align(Alignment.BottomCenter),
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.error,
                    text = stringResource(R.string.Placé_ligner_parallèle_numéro_code_barres_pour_scanner)
                )
            }


        }

    }
}





@SuppressLint("ClickableViewAccessibility")
@Composable
fun BareCodeCameraView(
    toaster: ToasterState,
    barCodeViewModel: BarCodeViewModel,
    onImageCapturedAndCorrectCode: () -> Unit,
    onImageCapturedButNotCorrectCode: () -> Unit
) {
    val context = LocalContext.current
    val cameraController = LifecycleCameraController(context)
    val lifecycleOwner = androidx.lifecycle.compose.LocalLifecycleOwner.current

    val previewView = remember { PreviewView(context) }

    val options = BarcodeScannerOptions.Builder()
        // .setBarcodeFormats()
        .build()

    val barcodeScanner: BarcodeScanner = BarcodeScanning.getClient(options)

    cameraController.setImageAnalysisAnalyzer(
        ContextCompat.getMainExecutor(context),
        MlKitAnalyzer(
            listOf(barcodeScanner),
            CameraController.TAP_TO_FOCUS_NOT_STARTED,
            ContextCompat.getMainExecutor(context)
        ) { result: MlKitAnalyzer.Result? ->
            val barcodeResults = result?.getValue(barcodeScanner)
            if (barcodeResults.isNullOrEmpty() ||
                (barcodeResults.first() == null)
            ) {
                previewView.overlay.clear()
                previewView.setOnTouchListener { _, _ -> false } //no-op
                return@MlKitAnalyzer
            }

            barcodeResults.first()?.let { barcodeValue ->
                val barCode = BareCode(
                        value = barcodeValue.rawValue?:"",
                        barecodeformat = getBarcodeFormat(barcodeValue.format),
                        barecodetype = barcodeValue.valueType.toString()
                    )
                barCodeViewModel.onBarCodeInfo(barCode = barCode)

            }


            previewView.overlay.clear()

            beep()

            barcodeResults.clear()
            barcodeScanner.close()

            //  if(isCorrectQrCode) {
            onImageCapturedAndCorrectCode.invoke()
            //  } else {
            //     onImageCapturedButNotCorrectCode.invoke()
            //  }


        }
    )

    // If `lifecycleOwner` changes, dispose and reset the effect
    DisposableEffect(lifecycleOwner) {
        // Create an observer that triggers our remembered callbacks
        // for sending analytics events
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_START) {
                consoleLog("on start")
            } else if (event == Lifecycle.Event.ON_STOP) {
                consoleLog("on stop")
            }
        }

        // Add the observer to the lifecycle
        lifecycleOwner.lifecycle.addObserver(observer)

        // When the effect leaves the Composition, remove the observer
        onDispose {
            barcodeScanner.close()//test
            consoleLog("The effect is leaving the Composition")
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }






    cameraController.bindToLifecycle(lifecycleOwner)
    previewView.controller = cameraController





    cameraController.enableTorch(barCodeViewModel.flashState)






    CameraView(previewView = previewView)
}

@Composable
fun CameraView(previewView: PreviewView) {
    Box(
        contentAlignment = Alignment.BottomCenter,
        modifier = Modifier
            .fillMaxSize()
            .semantics { contentDescription = "Camera View" }
    ) {
        AndroidView({ previewView }, modifier = Modifier.fillMaxSize())
    }
}

private fun consoleLog(messageToLog: String) {
    Log.i("CameraView.kt", messageToLog)
}

private fun beep() {
    val toneGen = ToneGenerator(AudioManager.STREAM_MUSIC, 100)
    toneGen.startTone(ToneGenerator.TONE_CDMA_ALERT_CALL_GUARD, 200)
}

fun openBareCodeScanner(
    navigate: (route: Any) -> Unit,
    onBarCodeInfo: (BareCode) -> Unit
) {
    onBarCodeInfo(BareCode())
    navigate(BarCodeCameraRoute)
}







