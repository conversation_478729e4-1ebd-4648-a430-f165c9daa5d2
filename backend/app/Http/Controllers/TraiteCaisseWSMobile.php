<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;

use App\Http\Controllers\PrefixWSMobile;

class TraiteCaisseWSMobile extends Controller
{
    
	
	
	
	
	/*fonction get data from database*/
	 
	public function getTraiteCaisse(Request $request)
	 {



 $data = $request->json()->all();

	 	$connection = DatabaseConnection::setConnection($data);
	 	return response()->json($connection->table('TraiteCaisse')->get());
	 }

	




public function getTraiteCaisseByReglement(Request $request)
	 {
	 	 $data = $request->json()->all();

	 	$connection = DatabaseConnection::setConnection($data);
	 	return response()->json($connection->table('TraiteCaisse')
	 		->where('TRAIT_Reglement', $item["REGC_Code"])
	 		->where('TRAIT_IdSession', $item["REGC_IdSCaisse"])
	 		->where('TRAIT_Exercice', $item["REGC_Exercice"])
	 		->get());

	 }




public function getTraiteCaisseByReglements(Request $request)
	 {
	 	 $data = $request->json()->all();

	 	$connection = DatabaseConnection::setConnection($data);
	 /*	return response()->json($connection->table('LigneTicket')
	 		->where('LT_NumTicket', $data["object"]["TIK_NumTicket"])
	 		->where('LT_IdCarnet', $data["object"]["TIK_IdCarnet"])
	 		->where('LT_Exerc', $data["object"]["TIK_Exerc"])
	 		->get());*/
   $items=null;

    foreach ($data["object"] as &$item) {
         	  $items[] =   $connection->table('TraiteCaisse')
	 		->where('TRAIT_Reglement', $item["REGC_Code"])
	 		->where('TRAIT_IdSession', $item["REGC_IdSCaisse"])
	 		->where('TRAIT_Exercice', $item["REGC_Exercice"])
	->get();



         }


	return response()->json($items);


}

	public function getDeviseByTicket(Request $request)
	 {
		  return response()->json(DB::table('Devise')->where($request->field, $request->value)->get());
		 
	 }
	
     public function getDeviseByActive(Request $request)
	 {
	 	 $data = $request->json()->all();

	 	$connection = DatabaseConnection::setConnection($data);
		   return response()->json($connection->table('Devise')->where('Principale', 1)->where('Activite', 1)->get()->first());
		 
	 }


			

	
}





