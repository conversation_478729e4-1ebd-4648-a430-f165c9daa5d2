<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use DB;

class Unite_articleWSMobile extends Controller
{


    public function getUnite_articleByCode(Request $request)
    {
        return response()->json(DB::table('Unite_article')->
        where('UNITE_ARTICLE_CodeUnite', $request->UNITE_ARTICLE_CodeUnite)->where('UNITE_ARTICLE_CodeArt', $request->UNITE_ARTICLE_CodeArt)->first());

    }


    public function getUnite_article(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection( $data);
        return response()->json( $connection->table('Unite_article')->
        leftJoin('unite', 'Unite_article.UNITE_ARTICLE_CodeUnite', '=', 'unite.UNI_Code')->
            select("Unite_article.*","unite.UNI_Designation")->get());
    }


    public function getUnite_articleByX(Request $request)
    {
        return response()->json(DB::table('Unite_article')->where($request->field, $request->value)->get());

    }


    public function addUnite_article(Request $request)
    {
        $data = $request->json()->all();


        if (!empty ($data)) {
            $u = DB::table('Unite_article')->insert($data);

            return response()->json($u);

        } else {
            return (false);
        }


    }


    public function updateUnite_article(Request $request)
    {
        $data = $request->json()->all();
        DB::table('Unite_article')->where('UNITE_ARTICLE_CodeUnite', $data["UNITE_ARTICLE_CodeUnite"])->where('UNITE_ARTICLE_CodeArt', $data["UNITE_ARTICLE_CodeArt"])->update($data);
        return ("Data modified");

    }


    public function deleteUnite_article(Request $request)
    {

        DB::table('Unite_article')->where('UNITE_ARTICLE_CodeUnite', $request->UNITE_ARTICLE_CodeUnite)->where('UNITE_ARTICLE_CodeArt', $request->UNITE_ARTICLE_CodeArt)->delete();
        return ("Data Deleted");
    }


}





