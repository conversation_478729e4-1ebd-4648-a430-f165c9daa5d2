package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.remote.api.ligne_ticket

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class LigneTicketApiImpl(private val client: HttpClient) : LigneTicketApi {

    override suspend fun getLigneTicketByTickets(
        baseConfig: String
    ): Flow<DataResult<List<List<LigneTicket>>>> = flow {

        val result = executePostApiCall<List<List<LigneTicket>>>(
            client = client,
            endpoint = Urls.GET_LIGNE_TICKET_BY_TICKETS,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
}