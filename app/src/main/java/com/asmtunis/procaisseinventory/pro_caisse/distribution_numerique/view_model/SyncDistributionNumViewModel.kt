package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.sync.SyncEntity
import com.asmtunis.procaisseinventory.core.sync.SyncManager
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.LigneVisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.ResponseAddBatchVisiteDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.VisiteWithLinesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.VisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject


@HiltViewModel
class SyncDistributionNumViewModel @Inject constructor(
    private val syncManager: SyncManager,
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val listenNetwork: ListenNetwork,
    // app: Application
) : ViewModel() {

    // Sync state from SyncManager
    val syncState = syncManager.syncState

    private var autoSyncState  by mutableStateOf(false)
    private var autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(key = PROCAISSE_AUTO_SYNC_AUTHORISATION, default = true).distinctUntilChanged()
    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()

    private var  connected  by mutableStateOf(false)
    init {
        getNotSyncVisiteDn()
        noSyncedToDelete()

    }
    var notSyncAddBatchVisiteDnObj : String by mutableStateOf("")
        private set
        var responseAddBatchVisiteDnState: RemoteResponseState<List<ResponseAddBatchVisiteDn>> by mutableStateOf(RemoteResponseState())
            private set



    var notSyncVisiteDnObj : String by mutableStateOf("")
        private set

    var visiteDnNotSync: Map<VisitesDn, List<LigneVisitesDn>> by mutableStateOf(emptyMap())
        private set
        private fun getNotSyncVisiteDn() {
            viewModelScope.launch {
                val visiteDnNotSyncFlow =  proCaisseLocalDb.visitesDn.getNotSyncedVisite().distinctUntilChanged()


                combine(networkFlow, visiteDnNotSyncFlow, autoSyncFlow) { isConnected, visiteDnNotSyncList, autoSync ->
                    connected = isConnected
                    autoSyncState = autoSync
                    visiteDnNotSyncList.ifEmpty { emptyMap() }
                }.collect {

                    if (it.isEmpty()) {
                        visiteDnNotSync = emptyMap()
                        return@collect
                    }
                    visiteDnNotSync = it
                    if(connected && autoSyncState) syncVisitesDn(it)
                }
            }
        }


    fun syncVisitesDn(notSyncVisites: Map<VisitesDn, List<LigneVisitesDn>>) {
        viewModelScope.launch(dispatcherIO) {
            val listVisiteWithLinesDn = ArrayList<VisiteWithLinesDn>()
            notSyncVisites.forEach { (key, value) ->
                run {
                    listVisiteWithLinesDn.add(
                        VisiteWithLinesDn(
                            visite = key,
                            ligneVisitesDn = value
                        )
                    )
                }
            }




            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                    ?.let { Json.decodeFromString(it) }?: BaseConfig(),
                Json.encodeToJsonElement(listVisiteWithLinesDn)
            )

            notSyncVisiteDnObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.distributionNumerique.addBatchVisite(notSyncVisiteDnObj).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        // for(client in result.data!!){
                        //    local.clients.updateSyncClient(client.cLICodeM!! ,client.cLICode)
                        //  }



                        for (i in result.data!!.indices) {
                            if (result.data[i].code == "10200") {
                                proCaisseLocalDb.visitesDn.updateLgVisNum(result.data[i].VIS_Num, result.data[i].VIS_Code_M)

                                proCaisseLocalDb.visitesDn.updateVisite(result.data[i].VIS_Num, result.data[i].VIS_Code_M)
                            }
                        }

                        responseAddBatchVisiteDnState = RemoteResponseState(data = result.data, loading = false, error = null)
                    }

                    is DataResult.Loading -> {
                        responseAddBatchVisiteDnState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        responseAddBatchVisiteDnState = RemoteResponseState(data = null, loading = false, error = result.message, message = notSyncVisites.keys.first().vIS_Num)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }



    var responseDeleteBatchVisiteDnState: RemoteResponseState<List<ResponseAddBatchVisiteDn>> by mutableStateOf(RemoteResponseState())
        private set


    var visiteDnNotSyncToDelete: Map<VisitesDn, List<LigneVisitesDn>> by mutableStateOf(emptyMap())
        private set

    var notSyncVisiteDnToDeleteObj : String by mutableStateOf("")
        private set

        fun syncVisitesDnToDelete(notSyncVisitesToDelete: Map<VisitesDn, List<LigneVisitesDn>>) {
            viewModelScope.launch(dispatcherIO) {
                val listVisiteToDelete = ArrayList<VisitesDn>()
                notSyncVisitesToDelete.forEach { (key, value) ->
                    run {

                        listVisiteToDelete.add(key)


                    }
                }




                val baseConfigObj = GenericObject(
                    proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                        ?.let { Json.decodeFromString(it) }?: BaseConfig(),
                    Json.encodeToJsonElement(listVisiteToDelete)
                )

                notSyncVisiteDnToDeleteObj = Json.encodeToString(baseConfigObj)
                proCaisseRemote.distributionNumerique.deleteVisite(notSyncVisiteDnToDeleteObj).onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            for (i in result.data!!.indices) {
                                if (result.data[i].code == "10200") {
                                    proCaisseLocalDb.visitesDn.deleteVisiteByVisNum(result.data[i].VIS_Num, result.data[i].VIS_Exerc)

                                    proCaisseLocalDb.visitesDn.deleteLigneVisiteByVisNum(result.data[i].VIS_Num, result.data[i].VIS_Exerc)
                                }
                            }

                            responseDeleteBatchVisiteDnState = RemoteResponseState(data = result.data, loading = false, error = null)
                        }

                        is DataResult.Loading -> {
                            responseDeleteBatchVisiteDnState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            responseDeleteBatchVisiteDnState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }
        }



        fun noSyncedToDelete() {
            viewModelScope.launch {

                val visitToDeleteNotSyncFlow =  proCaisseLocalDb.visitesDn.getNoSyncedToDelete().distinctUntilChanged()


                combine(networkFlow, visitToDeleteNotSyncFlow, autoSyncFlow) { isConnected, visitToDeleteNotSyncList, autoSync ->
                    connected = isConnected
                    autoSyncState = autoSync
                    visitToDeleteNotSyncList.ifEmpty { emptyMap() }
                }.collect {

                    if (it.isEmpty()) {
                        visiteDnNotSyncToDelete = emptyMap()
                        return@collect
                    }
                    visiteDnNotSyncToDelete = it
                    if(connected && autoSyncState) syncVisitesDnToDelete(it)
                }
            }
        }

    // ========================================
    // SYNCMANAGER INTEGRATION & CONVENIENCE METHODS
    // ========================================

    /**
     * Trigger manual sync for all DistributionNum entities using SyncManager
     */
    fun triggerManualSyncAll() {
        viewModelScope.launch {
            try {
                val result = syncManager.syncEntity(SyncEntity.DISTRIBUTION_NUMERIQUE)
                if (!result.isSuccess) {
                    // Fallback to individual sync methods if SyncManager fails
                    syncVisitesDn(visiteDnNotSync)
                    syncVisitesDnToDelete(visiteDnNotSyncToDelete)
                }
            } catch (exception: Exception) {
                // Fallback to individual sync methods on exception
                syncVisitesDn(visiteDnNotSync)
                syncVisitesDnToDelete(visiteDnNotSyncToDelete)
            }
        }
    }

    /**
     * Check if any sync operation is currently in progress
     */
    val isSyncing: Boolean
        get() = responseAddBatchVisiteDnState.loading || responseDeleteBatchVisiteDnState.loading

    /**
     * Get the current sync error from any active sync operation
     */
    val syncError: String?
        get() = responseAddBatchVisiteDnState.error ?: responseDeleteBatchVisiteDnState.error

    /**
     * Get the total count of unsynchronized items
     */
    val unsyncedCount: Int
        get() = visiteDnNotSync.size + visiteDnNotSyncToDelete.size

    /**
     * Reset all sync states to initial state
     */
    fun resetAllSyncStates() {
        responseAddBatchVisiteDnState = RemoteResponseState()
        responseDeleteBatchVisiteDnState = RemoteResponseState()
    }

    /**
     * Get sync status summary for UI display
     */
    val syncStatusSummary: String
        get() = when {
            isSyncing -> "Synchronisation en cours..."
            syncError != null -> "Erreur de synchronisation: $syncError"
            unsyncedCount > 0 -> "$unsyncedCount visites non synchronisées"
            else -> "Toutes les visites sont synchronisées"
        }
}