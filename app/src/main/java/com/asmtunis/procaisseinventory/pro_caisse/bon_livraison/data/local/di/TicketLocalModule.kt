package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.ligne_ticket.dao.LigneTicketDAO
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.ligne_ticket.repository.LigneTicketLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.ligne_ticket.repository.LigneTicketLocalRepositoryImpl
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.ticket.dao.TicketDAO
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.ticket.repository.TicketLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.ticket.repository.TicketLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class TicketLocalModule {

    @Provides
    @Singleton
    fun provideTicketDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.ticketDAO()

    @Provides
    @Singleton
    @Named("Ticket")
    fun provideTicketRepository(
        ticketDAO: TicketDAO
    ): TicketLocalRepository = TicketLocalRepositoryImpl(ticketDAO = ticketDAO)




    @Provides
    @Singleton
    fun provideLigneTicketDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.ligneTicketDAO()

    @Provides
    @Singleton
    @Named("LigneTicket")
    fun provideLigneTicketRepository(
        ligneTicketDAO: LigneTicketDAO
    ): LigneTicketLocalRepository = LigneTicketLocalRepositoryImpl(ligneTicketDAO = ligneTicketDAO)



}
