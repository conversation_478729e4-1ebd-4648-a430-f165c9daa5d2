<?php

namespace App\Http\Controllers\Http\DuxInventory;

use App\Http\Requests\BaseRequest;
use App\Http\Requests\M_DocumentRequest;
use App\Models\DuxInventory\L_LigneDocument;
use App\Models\DuxInventory\M_Document;
use App\Services\M_DocumentService;



class M_DocumentController extends  BaseController
{
    protected $documentService;
    protected $modelClass;
    protected $requestClass;

    public function __construct(M_DocumentService $documentService)
    {
        $this->documentService = $documentService;
        $this->requestClass = M_DocumentRequest::class;
        $this->modelClass = M_Document::class;
        $this->ligneModelClass = L_LigneDocument::class;
        $this->parentIdField = 'idDocument';
    }


    public function getDocumentsOuLignes(BaseRequest $request)
    {
        try {
            $mois = $request->validated()['object']['mois'];
            $documents = $this->documentService->getDocumentsOuLignes(
                $this->modelClass, $this->idClasseDocument, $this->dateFilter, $mois);
            return response()->success($documents);
        } catch (\Exception $e) {
            return response()->error($e);
        }
    }



}
