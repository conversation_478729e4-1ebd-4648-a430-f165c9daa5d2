<?php

namespace App\Http\Controllers\Http\DuxInventory;

use App\Helpers\DatabaseConnection;
use App\Http\Requests\LoginRequest;
use App\Models\DuxInventory\P_Utilisateur;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;


class P_UtilisateurController extends Controller
{
    public function getUtilisateurs()
    {
        try {
            $utilisateurs = P_Utilisateur::where('active', '=', '1')
                ->orderby('j_ddm', 'desc')->get();
            return response()->success($utilisateurs);
        } catch (\Exception $e) {
            return response()->error($e);
        }
    }

    public function getUtilisateurById(Request $request)
    {
        try {
            $data = $request->json()->all();
            DatabaseConnection::setConnection($data);
            $codeUser = $request["object"]["j_codeUser"];
            $utilisateur = P_Utilisateur::where('id', '=', $codeUser)->first();
            return response()->success($utilisateur);
        } catch (\Exception $e) {
            return response()->error($e);
        }
    }

    public function authentification(LoginRequest $request)
    {
        try {
            $dataUser = $request->validated();
            $user = P_Utilisateur::where('login', $dataUser['object']['login'])
                ->where('psw', md5($dataUser['object']['password']))
                ->where('active', 1)
                ->first();
            if (!$user) {
                throw new \Exception('Invalid credentials or user not active');
            }
            return response()->success($user);
        } catch (\Exception $e) {
            return response()->error($e);
        }

    }

    public function getUsersTechnicien()
    {
        try {
            $User = P_Utilisateur::from('p_utilisateur as u')
                ->leftjoin('E_TypeUser as t', 'u.idType', '=', 't.id')
                ->select('u.*')
                ->where('t.technicien', '=', '1')
                ->where('u.active', '=', '1')
                ->get();
            return response()->success($User);
        } catch (\Exception $e) {
            return response()->error($e);
        }
    }


}
