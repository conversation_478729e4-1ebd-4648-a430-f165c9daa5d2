package com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.inv.repository

import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.Inventaire
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire
import kotlinx.coroutines.flow.Flow


interface InventaireLocalRepository {



    fun upsertAll(value: List<Inventaire>)
    fun upsert(value: Inventaire)

    fun setToInserted(codeM: String)

    fun deleteAll()
    fun delete(inventaire: Inventaire)
    fun getAll(): Flow<List<Inventaire>>

    fun count(): Flow<Int>

    fun counts(): Flow<Map<Inventaire, List<LigneInventaire>>>
    fun getNotSync(): Flow<List<Inventaire>>
    fun updateNumAndEtat(code: String, codeLocal : String, etat: String)
    fun noSynced(): Flow<Map<Inventaire, List<LigneInventaire>>>

    fun getNewCode(prefix: String): Flow<String>
    fun getAllFiltred(
                      isAsc: Int,
                      filterByEtatInv: String,
                      sortBy: String
    ): Flow<Map<Inventaire, List<LigneInventaire>>>



    fun filterByInvCode(
                        searchString: String,
                        filterByEtatInv: String,
                        sortBy: String,
                        isAsc: Int
    ): Flow<Map<Inventaire, List<LigneInventaire>>>

}