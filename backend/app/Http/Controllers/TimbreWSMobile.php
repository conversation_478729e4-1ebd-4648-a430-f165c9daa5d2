<?php

namespace App\Http\Controllers;

use App\Helpers\AppHelper;
use App\Helpers\DatabaseConnection;
use DateTime;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;


class TimbreWSMobile extends Controller
{


    public function getAll(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $query = $connection->table('Timbre')
            ->select('*');
        /*   if (!is_null($ddm) && !empty($ddm) && (strtotime($ddm))) {
               $query->whereDate('CLI_DDm', '>=',  AppHelper::setDateFormat($ddm));
           }*/
        return response()->json(collect($query->get()));
    }


    public function getClientByX(Request $request)
    {
        return response()->json(DB::table('client')->where($request->field, $request->value)->get());
    }


    public function addBatchClient(Request $request): JsonResponse
    {
        $items = $request->json()->all();
        if (!empty($items)) {
            $connection = DatabaseConnection::setConnection($items);
            $result = [];
            foreach ($items["object"] as $data) {
                $item = $data;
                unset($item['Solde']);
                $item['CLI_Date_Cre'] = AppHelper::setDateFormat($data['CLI_Date_Cre']);
                $item['CLI_Code'] = (new PrefixWSMobile)->getPrefix($connection, "Client",
                    $item['exercice'],
                    $item['CLI_Exo_Valable']);
                unset($item['exercice']);
                $item['CLI_Exo_Valable'] = 0;
                $item['Clt_Latitude'] = AppHelper::getNumber($data['Clt_Latitude']);
                $item['Clt_Longitude'] = AppHelper::getNumber($data['Clt_Longitude']);
                $data['Clt_Latitude'] = $item['Clt_Latitude'];
                $data['Clt_Longitude'] = $item['Clt_Longitude'];
                $data['CLI_Exo_Valable'] = $item['CLI_Exo_Valable'];
                $data['CLI_Code'] = $item['CLI_Code'];
                $connection->table('client')->insert($data);
                $result[] = $data;
            }
        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }


    public function addClient(Request $request)
    {
        $data = $request->json()->all();

        if (!empty($data)) {
            $connection = DatabaseConnection::setConnection($data);

            $c = $connection->table('client')->insert($data["object"]);


            if ($c) {
                return response()->json($data['object']);
            } else {
                return response()->json(null);
            }

        } else {
            return response()->json(null);
        }
    }


    public function updateClient(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        $connection->table('client')->where('CLI_Code', $data["CLI_Code"])->update($data);

        return ("Data modified");
    }


    public function deleteClient(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $connection->table('client')->where('CLI_Code', $data["object"])->delete();
        return ("Data Deleted");
    }


}
