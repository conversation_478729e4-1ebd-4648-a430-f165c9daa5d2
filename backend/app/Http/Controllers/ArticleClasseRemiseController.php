<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ArticleClasseRemiseController extends Controller
{

    public function getArticleClasseRemise(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Article_classe_remise')->get());

    }

    public function addArticleClasseRemiseMobile(Request $request)
    {
        $items = $request->json()->all();

        $connection = DatabaseConnection::setConnection($items);

        if (!empty($items)) {
            // $f = $connection->table('famille')->insert($data);
            foreach ($items["object"]["articleClasseRemises"] as $data) {
                if ($data["ART_CLASS_REM_DDm"] == "NULL") {
                    $data["ART_CLASS_REM_DDm"] = null;
                }

                if ($data["ART_CLASS_REM_export"] == "NULL") {
                    $data["ART_CLASS_REM_export"] = null;
                }

                if ($data["ART_CLASS_REM_station"] == "NULL") {
                    $data["ART_CLASS_REM_station"] = null;
                }

                if ($data["ART_CLASS_REM_user"] == "NULL") {
                    $data["ART_CLASS_REM_user"] = null;
                }
                $exist = $connection->table('Article_classe_remise')
                    ->where('ART_CLASS_REM_Code', '=', $data['ART_CLASS_REM_Code'])
                    ->where('ART_CLASS_REM_ArtCode', '=', $data['ART_CLASS_REM_ArtCode'])
                    ->get();
                if (is_null($exist) || $exist->isEmpty()) {
                    $f = $connection->table('Article_classe_remise')->insert($data);
                } else {
                    $f = $connection->table('Article_classe_remise')
                        ->where('ART_CLASS_REM_Code', $data['ART_CLASS_REM_Code'])
                        ->where('ART_CLASS_REM_ArtCode', $data['ART_CLASS_REM_ArtCode'])
                        ->update([
                            'ART_CLASS_REM_DDm' => $data['ART_CLASS_REM_DDm'],

                            'ART_CLASS_REM_export' => $data['ART_CLASS_REM_export'],
                            'ART_CLASS_REM_station' => $data['ART_CLASS_REM_station'],
                            'ART_CLASS_REM_user' => $data['ART_CLASS_REM_user'],
                        ]);
                }
            }
            return response()->json($f);

        } else {
            return (false);
        }

    }

    public function updateFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        $connection->table('famille')->where('FAM_Code', $data["FAM_Code"])->update($data);

        return ("Data modified");

    }
    

    public function deleteFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $connection->table('famille')->where('FAM_Code', $data["object"])->delete();
        return ("Data Deleted");
    }

    public function updateOrCreate($connection, $data, $table, $whereClauses)
    {
        $exists = $connection->table($table)->where($whereClauses)->first();

        if ($exists) {
            $connection->table($table)->where($whereClauses)->delete();
        }

        return $connection->table($table)->insert($data);

    }
}
