package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.type_communication.repository

import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.TypeCommunicationVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.type_communication.dao.TypeCommunicationVCDAO
import kotlinx.coroutines.flow.Flow



class TypeCommunicationVCLocalRepositoryImpl(
    private val typeCommmunicationVCDAO: TypeCommunicationVCDAO
) : TypeCommunicationVCLocalRepository {


    override fun upsertAll(value: List<TypeCommunicationVC>) = typeCommmunicationVCDAO.insertAll(value)


    override fun deleteAll() = typeCommmunicationVCDAO.deleteAll()

    override fun getAll(): Flow<List<TypeCommunicationVC>?> = typeCommmunicationVCDAO.all

}