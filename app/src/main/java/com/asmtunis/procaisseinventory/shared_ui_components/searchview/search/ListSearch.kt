package com.asmtunis.procaisseinventory.shared_ui_components.searchview.search

sealed class ListSearch() {
    class FirstSearch : ListSearch()
    class SecondSearch : ListSearch()
    class ThirdSearch : ListSearch()

    fun copy(): ListSearch {
        return when (this) {
            is FirstSearch -> FirstSearch()
            is SecondSearch -> SecondSearch()
            is ThirdSearch -> ThirdSearch()
        }
    }
}
