package com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.domaine.AddTicketRayonItem
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class TicketRayonApiImpl(private val client: HttpClient) : TicketRayonApi {
    override suspend fun addTicketRayon(baseConfig: String): Flow<DataResult<List<AddTicketRayonItem>>> = flow {
        val result = executePostApiCall<List<AddTicketRayonItem>>(
            client = client,
            endpoint = Urls.ADD_TICKET_RAYON,
            baseConfig = baseConfig,
        )

        emitAll(result)
    }
}