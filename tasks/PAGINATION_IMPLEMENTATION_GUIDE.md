# Standardized Pagination System Implementation Guide

## Overview

This document outlines the implementation of a standardized pagination system across all list screens in the ProCaisse Inventory Android app using Room database with Paging 3 library.

## Architecture

### 1. Database Layer (DAOs)

#### Updated DAOs with Pagination Support:
- ✅ **ArticleDAO** - Already partially implemented
- ✅ **ClientDAO** - Added pagination methods
- ✅ **InventaireDAO** - Added pagination methods
- 🔄 **BonEntreeDAO** - Pending implementation
- 🔄 **BonLivraisonDAO** - Pending implementation
- 🔄 **BonRetourDAO** - Pending implementation

#### Implementation Pattern:
```kotlin
@Dao
interface ExampleDAO {
    // Existing Flow<List<T>> methods (keep for backward compatibility)
    @Query("SELECT * FROM table_name ORDER BY relevant_field")
    fun getAll(): Flow<List<Entity>>
    
    // New PagingSource<Int, T> methods for pagination
    @Query("SELECT * FROM table_name ORDER BY relevant_field")
    fun getAllPaginated(): PagingSource<Int, Entity>
    
    @Query("SELECT * FROM table_name WHERE field LIKE '%' || :search || '%' ORDER BY relevant_field")
    fun searchPaginated(search: String): PagingSource<Int, Entity>
}
```

### 2. Repository Layer

#### Updated Repositories:
- ✅ **ClientRoomRepository** - Added pagination methods
- 🔄 **InventaireRepository** - Pending implementation
- 🔄 **ArticleRepository** - Needs completion

#### Implementation Pattern:
```kotlin
class ExampleRepositoryImpl(private val dao: ExampleDAO) : ExampleRepository {
    // Existing methods (keep for backward compatibility)
    override fun getAll(): Flow<List<Entity>> = dao.getAll()
    
    // New paginated methods
    override fun getAllPaginated(): Flow<PagingData<Entity>> {
        return Pager(
            config = PagingConfigRoom.pagingConfig,
            pagingSourceFactory = { dao.getAllPaginated() }
        ).flow
    }
}
```

### 3. ViewModel Layer

#### Updated ViewModels:
- ✅ **ClientViewModel** - Added pagination support
- 🔄 **InventaireViewModel** - Pending implementation
- 🔄 **ArticleViewModel** - Pending implementation

#### Implementation Pattern:
```kotlin
class ExampleViewModel @Inject constructor(
    private val repository: ExampleRepository
) : ViewModel() {
    
    // Existing Flow<List<T>> (keep for backward compatibility)
    val items: Flow<List<Entity>> = repository.getAll()
    
    // New paginated flow
    val paginatedItems: Flow<PagingData<Entity>> = repository
        .getAllPaginated()
        .cachedIn(viewModelScope)
}
```

### 4. UI Layer

#### New Components:
- ✅ **PaginatedLazyColumn** - Reusable paginated list component
- ✅ **PaginatedClientListExample** - Example implementation
- ✅ **DataBasePaginationLoadState** - Already exists for load state handling

#### Implementation Pattern:
```kotlin
@Composable
fun ExampleScreen(viewModel: ExampleViewModel = hiltViewModel()) {
    val paginatedItems = viewModel.paginatedItems.collectAsLazyPagingItems()
    
    PaginatedLazyColumn(
        lazyPagingItems = paginatedItems,
        pullToRefreshEnabled = true,
        onRefresh = { /* refresh logic */ },
        key = { item -> item.id }
    ) { item ->
        // Existing item composable - no changes needed!
        ExistingItemRow(item = item)
    }
}
```

## Configuration

### PagingConfig Settings:
```kotlin
object PagingConfigRoom {
    val pagingConfig = PagingConfig(
        pageSize = 20,           // Items per page
        prefetchDistance = 5,    // Items to prefetch
        enablePlaceholders = false // Disable placeholders for better UX
    )
}
```

## Migration Guide

### For Existing Screens:

1. **Update DAO** (if not already done):
   ```kotlin
   // Add paginated methods
   @Query("SELECT * FROM table ORDER BY field")
   fun getAllPaginated(): PagingSource<Int, Entity>
   ```

2. **Update Repository**:
   ```kotlin
   // Add paginated methods
   fun getAllPaginated(): Flow<PagingData<Entity>>
   ```

3. **Update ViewModel**:
   ```kotlin
   // Add paginated flow
   val paginatedItems = repository.getAllPaginated().cachedIn(viewModelScope)
   ```

4. **Update UI**:
   ```kotlin
   // Replace PullToRefreshLazyColumn with PaginatedLazyColumn
   val items = viewModel.paginatedItems.collectAsLazyPagingItems()
   PaginatedLazyColumn(lazyPagingItems = items) { item ->
       ExistingItemComposable(item) // Keep existing UI components!
   }
   ```

### Backward Compatibility:
- All existing `Flow<List<T>>` methods are preserved
- Existing UI components can be reused without changes
- Migration can be done incrementally, screen by screen

## Benefits

### Performance Improvements:
- **Memory Efficiency**: Only loads visible items + prefetch buffer
- **Smooth Scrolling**: Lazy loading prevents UI freezing
- **Network Optimization**: Reduces data transfer for large datasets
- **Battery Life**: Less CPU and memory usage

### User Experience:
- **Consistent Loading States**: Standardized loading, error, and empty states
- **Pull-to-Refresh**: Built-in refresh functionality
- **Error Handling**: Automatic retry mechanisms
- **Offline Support**: Works with existing offline-first architecture

### Developer Experience:
- **Reusable Components**: Single PaginatedLazyColumn for all lists
- **Type Safety**: Strongly typed pagination with generics
- **Easy Integration**: Minimal changes to existing code
- **Consistent Patterns**: Standardized implementation across the app

## Implementation Status

### Completed ✅:
- Core pagination infrastructure
- PaginatedLazyColumn component
- ClientDAO pagination support
- ClientRepository pagination methods
- ClientViewModel pagination integration
- Example implementation and documentation

### In Progress 🔄:
- InventaireDAO pagination methods
- BonEntreeDAO pagination support
- BonLivraisonDAO pagination support
- Additional ViewModel updates

### Pending ⏳:
- Screen-by-screen migration
- Performance testing with large datasets
- Integration testing
- User acceptance testing

## Testing Strategy

### Unit Tests:
- DAO pagination methods
- Repository pagination logic
- ViewModel pagination flows

### Integration Tests:
- End-to-end pagination flow
- Error handling scenarios
- Offline/online data sync

### Performance Tests:
- Large dataset scrolling performance
- Memory usage optimization
- Battery consumption analysis

## Next Steps

1. **Complete DAO implementations** for remaining entities
2. **Update repository layers** with pagination support
3. **Migrate high-traffic screens** first (client lists, inventory, articles)
4. **Conduct performance testing** with production-like data
5. **Gather user feedback** and iterate on UX improvements
