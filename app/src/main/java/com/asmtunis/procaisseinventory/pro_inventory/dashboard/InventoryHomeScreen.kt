package com.asmtunis.procaisseinventory.pro_inventory.dashboard

import com.asmtunis.procaisseinventory.nav_components.NavDrawer

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Sync
import androidx.compose.material.icons.filled.Update
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.ProInventorySyncRoute
import com.asmtunis.procaisseinventory.core.navigation.UpdateLocalDbInventoryRoute
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.core.utils.Sync.getProInventoryTotalNoSyncCount
import com.asmtunis.procaisseinventory.core.utils.Sync.getSharedTotalNoSyncCount
import com.asmtunis.procaisseinventory.core.utils.UpdateLoadingStateUtils
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.network_errors.view_model.NetworkErrorsViewModel
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.stat.StatementBody
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.stat.components.BaseRow
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.stat.data.PieChartSliceData
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.buttons.ErrorGetDataButton
import com.asmtunis.procaisseinventory.view_model.*
import kotlinx.coroutines.launch


@Composable
fun InventoryHomeScreen(
    navigate: (route: Any) -> Unit,
    navDrawerViewModel: NavigationDrawerViewModel,
    networkErrorsVM: NetworkErrorsViewModel,
    dataViewModel: DataViewModel,
    mainViewModel: MainViewModel,
    networkViewModel: NetworkViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncInventoryViewModel: SyncInventoryViewModel,
    settingViewModel: SettingViewModel
    ) {
    val uiWindowState = settingViewModel.uiWindowState

    val isConnected = networkViewModel.isConnected
    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val density = LocalDensity.current

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val networkErrorsList = networkErrorsVM.networkErrorsList


    val utilisateur = mainViewModel.utilisateur

      val articleMapByBarCode = mainViewModel.articleMapByBarCode
   // val articleList = mainViewModel.listStationStockArticl.filter { it.sARTCodeSatation == utilisateur.Station }

    val isLoadingInventoryData = UpdateLoadingStateUtils.isLoadingInventoryData(getProInventoryDataViewModel = getProInventoryDataViewModel)
    val isLoadingSharedData = UpdateLoadingStateUtils.isLoadingSharedData(getSharedDataViewModel = getSharedDataViewModel)
    val isLoadingCommenSharedData = UpdateLoadingStateUtils.isLoadingCommenSharedData(getSharedDataViewModel = getSharedDataViewModel)


    val isLoading = isLoadingInventoryData || isLoadingSharedData || isLoadingCommenSharedData

    val articleCount = dataViewModel.getArticleCount()
    val noSyncCount = getProInventoryTotalNoSyncCount(
        syncInventoryViewModel = syncInventoryViewModel,
    ) + getSharedTotalNoSyncCount(syncSharedViewModels = syncSharedViewModels)


//    LaunchedEffect(key1 = Unit){
//        BASE_URL = String.format(validateBaseUrl(selectedBaseconfig), selectedBaseconfig.adresse_ip, selectedBaseconfig.port)
//
//    }





    val bills: List<PieChartSliceData> = listOf(
        PieChartSliceData(
            radius = 25.dp,
            name = UiText.StringResource(resId = R.string.article_en_stock),
           // amount = stringToDouble(articleList.filter { stringToDouble(it.sARTQteStation) >0 }.size.toString()),
             amount = stringToDouble(articleMapByBarCode.filter { stringToDouble(it.value.aRTQteStock) >0 }.size.toString()),
           // amount = stringToDouble(articleList.filter { it.sARTQte >0 }.size.toString()),
            color =   MaterialTheme.colorScheme.outlineVariant
        ),
        PieChartSliceData(
            radius = 15.dp,
            name = UiText.StringResource(resId = R.string.article_hors_stock),
          //  amount = stringToDouble(articleList.filter { stringToDouble(it.sARTQteStation) <=0 }.size.toString()),
             amount = stringToDouble(articleMapByBarCode.filter { stringToDouble(it.value.aRTQteStock) <=0 }.size.toString()),
          //  amount = stringToDouble(articleList.filter { it.sARTQte <=0 }.size.toString()),
            color =   MaterialTheme.colorScheme.error
        )
    )

    NavDrawer(
        navigate = { navigate(it) },
        drawer = drawer,
        navDrawerViewmodel = navDrawerViewModel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        mainViewModel = mainViewModel,
        syncProcaisseViewModels = syncProcaisseViewModels,
        syncInventoryViewModel = syncInventoryViewModel,
        syncSharedViewModels = syncSharedViewModels,
        settingViewModel = settingViewModel
    ) {
        Scaffold(
            topBar = {
                AppBar(
                    showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    onNavigationClick = { scope.launch { drawer.open() } },
                    title = stringResource(id = navDrawerViewModel.proInventorySelectedMenu.title),
                )
            },
            //    containerColor = colorResource(id = R.color.black),
            floatingActionButton = {
                Column(
                    verticalArrangement = Arrangement.Bottom,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {

                    //TODO IMPLEMENT SAME FUN FOR INV AND PRO CAISSE


                    if(noSyncCount==0 && isConnected)
                        FloatingActionButton(
                            onClick = {
                                if(!isLoadingInventoryData && !isLoadingSharedData && !isLoadingCommenSharedData)
                                navigate(UpdateLocalDbInventoryRoute)
                            }) {


                            if (isLoading)
                                LottieAnim(lotti = R.raw.loading, size = 25.dp)
                            else
                                Icon(
                                    imageVector = Icons.Default.Update,
                                    contentDescription = stringResource(id = R.string.update_db_title),
                                    /* modifier = Modifier
                                         .padding(end = 4.dp)*/
                                )
                        }


                    Spacer(modifier = Modifier.height(12.dp))

                    if(noSyncCount>0) {
                        ExtendedFloatingActionButton(
                            onClick = {
                                navigate(ProInventorySyncRoute)
                            }) {
                            Icon(
                                Icons.Filled.Sync,
                                contentDescription = stringResource(id = R.string.sync_title),
                                Modifier.size(AssistChipDefaults.IconSize)
                            )
                        }
                    }
                }
            }
        ) { padding ->
            Column(
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding)
            ) {

                Spacer(modifier = Modifier.height(12.dp))

                ErrorGetDataButton(
                    navigate = { navigate(it) },
                   networkErrorsList = networkErrorsList
                )

                if(networkErrorsList.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(12.dp))
                }
                

                if(articleCount != 0 && articleMapByBarCode.size != articleCount /*&& !isLoading*/) {
                    Text(text = stringResource(R.string.total_article, articleCount))
                    Spacer(modifier = Modifier.height(12.dp))
                }

                StatementBody(
                    items = bills,
                    priceFormatTotalSum = false,
                    circleLableTotal = articleMapByBarCode.size.toDouble(),
                    circleLabel = if(articleMapByBarCode.size>1) stringResource(R.string.article_field_title) else stringResource(R.string.article_title),
                    rows = { bill ->
                        BaseRow(
                            title = bill.name.asString()?: "error : id string UITEXT",
                            amount = bill.amount,
                            color =   bill.color,
                            showArrow = false,
                            formatAmount = false,
                            imageVector = Icons.Filled.ChevronRight
                        )
                    }
                )
            }
        }
    }
}

