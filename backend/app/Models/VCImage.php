<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\URL;

/**
 * @property string $Code_IMG
 * @property string $Code_Mob
 * @property string $Type_VC
 * @property string $DateOp
 * @property string $Chemin_Img
 * @property string $Code_TypeVC
 * @property string $Image
 * @property int $CodeUser
 */
class VCImage extends Model
{

    protected $connection = 'onthefly';
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'VC_Image';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'Code_IMG';

    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;


    public $timestamps = false;


    /**
     * @var array
     */
    protected $fillable = ['Code_Mob', 'Type_VC', 'DateOp', 'Chemin_Img', 'Code_TypeVC', 'Image', 'CodeUser'];


    //protected $appends = ['imgUrl'];


    public function VCLancementNP()
    {
        $this->belongsTo(VCLancementNP::class);
    }

    public function VCAutre()
    {
        $this->belongsTo(VCAutre::class);
    }

    public function VCPrix()
    {
        $this->belongsTo(VCPrix::class);
    }

    public function VCPromo()
    {
        $this->belongsTo(VCPromo::class);
    }

    public function getImgurlAttribute(): ?string
    {
        try {
            $url = explode('/', str_replace('\\', '/', $this->Chemin_Img));
            return 'storage/' . $url[sizeof($url) - 2] . '/' . $url[sizeof($url) - 1];
        } catch (\Exception $e) {
            return null;
        }
    }

    public function getLink(): string
    {
        $url = explode('/', str_replace('\\', '/', $this->Chemin_Img));
        return URL::to('/') . '/storage/' . $url[sizeof($url) - 2] . '/' . $url[sizeof($url) - 1];
    }


}
