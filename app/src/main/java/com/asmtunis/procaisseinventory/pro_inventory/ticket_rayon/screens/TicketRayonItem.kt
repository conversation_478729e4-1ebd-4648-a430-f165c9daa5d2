package com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.screens

import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.domaine.TicketRayon
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.generateBarCode
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.getBarcodeFormat

@Composable
fun TicketRayonItem(
    article: Article,
    ticketRayon: TicketRayon,
    borderColor: Color = MaterialTheme.colorScheme.outline,
    setBottomSheetVisibility: (Boolean) -> Unit,
    setSelectedTicketRayon: (TicketRayon) -> Unit,
    haveTicketRayonAuthorisation: Boolean
){
    //todo compose to bitmap  "dev.shreyaspatil:capturable:1.0.3"



    val barCode = BareCode(
        value = article.aRTCodeBar,
        //barecodeformat  = getBarcodeFormat(articl.aRTCode),
       // barecodeformat  = BarcodeFormat.CODE_128, // crush if use getBarcodeFormat(articl.aRTCode) and return BarcodeFormat != CODE_128 to generate barCode bitmap
        barecodeformat  = getBarcodeFormat(article.aRTCodeBar),
        barecodetype  = ""
    )

    Column(
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxWidth()
            .padding(12.dp)
            .border(
                width = 1.dp,
                color = borderColor,
                shape = RoundedCornerShape(5.dp)
            )
            .clickable {
                if (!haveTicketRayonAuthorisation) return@clickable
                setSelectedTicketRayon(ticketRayon)

                setBottomSheetVisibility(true)
                //ticketRayonViewModel.bottomSheetVisibility(true)
            }
        // .heightIn(min = 70.dp, max = 150.dp)
    ) {
        Spacer(modifier = Modifier.height(12.dp))
        Text(
            text = StringUtils.convertStringToPriceFormat(article.prixSolde),
            style = MaterialTheme.typography.titleLarge
        )
        Spacer(modifier = Modifier.height(6.dp))
        Text(text = article.aRTDesignation)

        Spacer(modifier = Modifier.height(6.dp))


            Image(
                bitmap = generateBarCode(barcod = barCode).asImageBitmap(),
                contentDescription = stringResource(R.string.generate_barCode_image),
                modifier = Modifier
                    .fillMaxWidth(0.85f)
                    .heightIn(min = 40.dp, max = 60.dp)
            )



        Text(text = article.aRTCodeBar)
        // if(showDivider) {
        Spacer(modifier = Modifier.height(12.dp))
        //  HorizontalDivider(modifier = Modifier.customWidth(LocalConfiguration.current, 0.85f))
        //  }
    }



}