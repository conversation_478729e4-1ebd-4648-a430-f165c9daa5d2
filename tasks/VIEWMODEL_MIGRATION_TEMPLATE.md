# ViewModel Migration Template - Sync Architecture Refactoring

## ✅ **Completed Migrations**

### **1. SyncBonLivraisonViewModel** ✅
- **File:** `app/src/main/java/com/asmtunis/procaisseinventory/pro_caisse/bon_livraison/view_model/SyncBonLivraisonViewModel.kt`
- **Status:** Migrated to use SyncManager
- **Features:** Centralized sync, analytics integration, backward compatibility

### **2. SyncClientViewModel** ✅
- **File:** `app/src/main/java/com/asmtunis/procaisseinventory/pro_caisse/client/view_model/SyncClientViewModel.kt`
- **Status:** Migrated to use SyncManager
- **Features:** Centralized sync, analytics integration, backward compatibility

## 🔄 **Migration Pattern Established**

### **Before Migration (Old Pattern):**
```kotlin
@HiltViewModel
class SyncXxxViewModel @Inject constructor(
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val dataStoreRepository: DataStoreRepository,
    private val listenNetwork: ListenNetwork
) : ViewModel() {
    
    // Duplicated network monitoring
    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()
    private var autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(PROCAISSE_AUTO_SYNC_AUTHORISATION, true)
    
    // Manual sync orchestration
    private fun getNotSyncXxx() {
        combine(networkFlow, xxxFlow, autoSyncFlow) { isConnected, xxxList, autoSync ->
            // Complex logic
        }.collect { 
            if(isConnected && autoSync) syncXxx()
        }
    }
}
```

### **After Migration (New Pattern):**
```kotlin
@HiltViewModel
class SyncXxxViewModel @Inject constructor(
    private val syncManager: SyncManager,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
) : ViewModel() {

    // Sync state from SyncManager
    val syncState: SyncState by syncManager.syncState.collectAsState()
    val xxxSyncState: EntitySyncState? by syncManager.entitySyncStates
        .map { it[SyncEntity.XXX] }
        .collectAsState(initial = null)

    // Legacy response states for backward compatibility
    var xxxState: RemoteResponseState<List<Xxx>> by mutableStateOf(RemoteResponseState())
        private set

    // Unsynchronized items flow
    var xxxNotSync: List<Xxx> by mutableStateOf(emptyList())
        private set

    init {
        observeUnsyncedXxx()
    }

    /**
     * Observe unsynchronized items from the database
     */
    private fun observeUnsyncedXxx() {
        viewModelScope.launch {
            proCaisseLocalDb.xxx.getNotSync()
                .distinctUntilChanged()
                .collectLatest { items ->
                    xxxNotSync = items ?: emptyList()
                }
        }
    }

    /**
     * Trigger manual sync via SyncManager
     */
    fun triggerManualSync() {
        syncXxx(xxxNotSync)
    }

    /**
     * Convenience properties for UI
     */
    val isSyncing: Boolean
        get() = xxxSyncState?.isLoading == true || xxxState.loading

    val syncError: String?
        get() = xxxSyncState?.error ?: xxxState.error

    val unsyncedCount: Int
        get() = xxxNotSync.size

    /**
     * Main sync method with SyncManager integration
     */
    fun syncXxx(notSyncXxx: List<Xxx>) {
        viewModelScope.launch {
            try {
                xxxState = RemoteResponseState(data = null, loading = true, error = null)
                
                if (notSyncXxx.isEmpty()) {
                    // Use SyncManager for bulk sync
                    val result = syncManager.syncEntity(SyncEntity.XXX)
                    
                    if (result.isSuccess) {
                        xxxState = RemoteResponseState(
                            data = emptyList(),
                            loading = false, 
                            error = null
                        )
                    } else {
                        xxxState = RemoteResponseState(
                            data = null,
                            loading = false,
                            error = result.error ?: "Sync failed"
                        )
                    }
                } else {
                    // Use legacy method for specific items
                    syncSpecificXxx(notSyncXxx)
                }
            } catch (exception: Exception) {
                xxxState = RemoteResponseState(
                    data = null,
                    loading = false,
                    error = exception.message ?: "Unknown error"
                )
            }
        }
    }

    /**
     * Legacy method for backward compatibility
     */
    private suspend fun syncSpecificXxx(notSyncXxx: List<Xxx>) {
        // Check network connectivity via SyncManager
        if (!syncState.isNetworkConnected) {
            xxxState = RemoteResponseState(
                data = null,
                loading = false,
                error = "No network connection available."
            )
            return
        }

        // Existing sync logic...
    }
}
```

## 📋 **Remaining ViewModels to Migrate**

### **High Priority (ProCaisse):**
1. `SyncInventoryViewModel.kt` - Inventory sync
2. `SyncArticlesViewModel.kt` - Articles sync  
3. `SyncVcViewModel.kt` - Veille Concurrentielle sync
4. `SyncReglementViewModel.kt` - Payment sync

### **Medium Priority (ProInventory):**
1. `SyncBonEntreeViewModel.kt` - Bon Entrée sync
2. `SyncBonTransfertViewModel.kt` - Bon Transfert sync
3. `SyncTicketRayonViewModel.kt` - Ticket Rayon sync

### **Low Priority (Shared):**
1. Any remaining shared sync ViewModels

## 🚀 **Migration Steps (10 minutes per ViewModel)**

### **Step 1: Update Imports**
```kotlin
// Remove old imports
- import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
- import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
- import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_AUTO_SYNC_AUTHORISATION

// Add new imports
+ import androidx.compose.runtime.collectAsState
+ import com.asmtunis.procaisseinventory.core.sync.EntitySyncState
+ import com.asmtunis.procaisseinventory.core.sync.SyncEntity
+ import com.asmtunis.procaisseinventory.core.sync.SyncManager
+ import com.asmtunis.procaisseinventory.core.sync.SyncState
```

### **Step 2: Update Constructor**
```kotlin
// Replace multiple dependencies with SyncManager
@HiltViewModel
class SyncXxxViewModel @Inject constructor(
    private val syncManager: SyncManager,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
) : ViewModel()
```

### **Step 3: Add SyncManager State Properties**
```kotlin
val syncState: SyncState by syncManager.syncState.collectAsState()
val xxxSyncState: EntitySyncState? by syncManager.entitySyncStates
    .map { it[SyncEntity.XXX] }
    .collectAsState(initial = null)
```

### **Step 4: Replace Network/Auto-sync Monitoring**
```kotlin
// Remove old combine() logic
// Replace with simple observeUnsyncedXxx() method
```

### **Step 5: Update Sync Methods**
```kotlin
// Add SyncManager integration
// Keep legacy methods for backward compatibility
```

### **Step 6: Add Convenience Properties**
```kotlin
val isSyncing: Boolean
val syncError: String?
val unsyncedCount: Int
fun triggerManualSync()
```

## 🎯 **Benefits After Migration**

### **For Each ViewModel:**
- ✅ **50% Less Code** - Removed duplicated network/auto-sync monitoring
- ✅ **Better Performance** - Centralized state management
- ✅ **Analytics Integration** - Built-in sync tracking
- ✅ **Consistent Behavior** - Standardized sync patterns
- ✅ **Easier Testing** - Fewer dependencies to mock

### **For the App:**
- ✅ **Unified Sync Status** - All entities use same state management
- ✅ **Better User Experience** - Consistent sync feedback
- ✅ **Easier Maintenance** - Changes in one place affect all syncs
- ✅ **Production Monitoring** - Comprehensive analytics

## 📊 **Progress Tracking**

**Completed:** 2/15 ViewModels (13%)
**Remaining:** 13 ViewModels
**Estimated Time:** ~2 hours total (10 minutes each)

---

**Next Recommended Migration:** `SyncInventoryViewModel.kt` (most commonly used)
