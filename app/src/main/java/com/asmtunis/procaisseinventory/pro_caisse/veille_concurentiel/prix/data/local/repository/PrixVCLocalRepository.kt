package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.local.repository

import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine.PrixVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine.PrixVCWithImages
import kotlinx.coroutines.flow.Flow



interface PrixVCLocalRepository {
    fun upsertAll(value: List<PrixVC>)
    fun upsert(value: PrixVC)
    fun deleteAll()

    fun deleteByCode(codeAutre: String)
    fun getAll(): Flow<List<PrixVC>>
    fun noSyncedToDelete(): Flow<List<PrixVC>?>


    fun updateCloudCode(code: String, codeM: String)
    fun restDeleted(code: String,status : String, isSync:Boolean)
    fun setDeleted(code: String, codeMobile: String)
    fun noSyncedToAddOrUpdate(): Flow<List<PrixVC>>

    fun filterByNum(searchString: String, filterByTypComm: String, sortBy: String, isAsc: Int): Flow<List<PrixVCWithImages>>
    fun filterByArtConcurrent(searchString: String, filterByTypComm: String, sortBy: String?, isAsc: Int?): Flow<List<PrixVCWithImages>>
    fun filterByCodeArtLocal(searchString: String, sortBy: String, filterByTypComm: String, isAsc: Int): Flow<List<PrixVCWithImages>>
    fun getAllFiltred(isAsc: Int, filterByTypComm: String, sortBy: String): Flow<List<PrixVCWithImages>>
}
