<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property string $CodeVCLanP
 * @property string $CodeVCLanPM
 * @property string $ProduitLanP
 * @property string $DateOp
 * @property string $CodeConcur
 * @property string $NoteOp
 * @property float $PrixLanP
 * @property float $TauxPromo
 * @property int $CodeUser
 * @property string $InfoOp1
 * @property string $CodeTypeCom
 */
class VCLancementNP extends Model
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'VCLancementNP';

    /**
     * The primary key for the model.
     * 
     * @var string
     */
    protected $primaryKey = 'CodeVCLanP';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['CodeVCLanPM', 'ProduitLanP', 'DateOp', 'CodeConcur', 'NoteOp', 'PrixLanP', 'TauxPromo', 'CodeUser', 'InfoOp1', 'CodeTypeCom'];

    public function VC_Image()
    {
        $this->hasMany(VCImage::class);
    }
}
