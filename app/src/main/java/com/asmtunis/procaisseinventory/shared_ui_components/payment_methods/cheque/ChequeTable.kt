@file:OptIn(ExperimentalMaterial3Api::class)

package com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.cheque

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.data.banques.domaine.Banque
import com.asmtunis.procaisseinventory.data.cheque_caisse.domaine.ChequeCaisse
import com.simapps.ui_kit.ModifiersUtils.detectTapGestures
import kotlinx.coroutines.launch


@Composable
fun ChequeTable(
    listChequeCaisse : List<ChequeCaisse>,
    onTap: (index : Int, banque : Banque) -> Unit,
    onLongPress: (Int) -> Unit,
    onShowChequeTableChange :(Boolean) -> Unit,
    banque :(Int)-> Banque
) {
    val context = LocalContext.current
    val listState = rememberLazyListState()

    val scope = rememberCoroutineScope()
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

   val rowTitls = context.resources.getStringArray(R.array.chequeTable_array).toList()

    ModalBottomSheet(
        sheetState = sheetState,
        onDismissRequest = {
            scope.launch {
                sheetState.hide()
            }
            onShowChequeTableChange(false)
        },
    ) {
        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            HorizontalDivider(
                thickness = 2.dp,
                color = MaterialTheme.colorScheme.outline
            )
            ChequeTableHeader(rowTitls = rowTitls)
            HorizontalDivider(
                thickness = 2.dp,
                color = MaterialTheme.colorScheme.outline
            )



            LazyColumn(
                //  modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
                state = listState
            ) {
                items(
                    count = listChequeCaisse.size,
                    /* key = {
                         ligneBonEntree[it].id
                     }*/
                ) { index ->

                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        modifier = Modifier
                            .padding(start = 3.dp, end = 3.dp)
                            .heightIn(40.dp, 150.dp)
                            .fillMaxWidth()
                            .detectTapGestures(
                                key1 = banque(index),
                                onPress = {


                                },
                                onDoubleTap = {

                                },
                                onLongPress = {
                                    onLongPress(index)

                                },
                                onTap = {
                                    onTap(index, banque(index))
                                }
                            )

                    ) {

                        Text(
                            text =  (banque(index).bANDes?:banque(index).bANCode) +" (" +listChequeCaisse[index].numCheque+")",
                            textAlign = TextAlign.Center,
                            fontSize = MaterialTheme.typography.bodySmall.fontSize,
                            modifier = Modifier.fillMaxWidth(0.5f)
                        )




                        Text(
                            text = listChequeCaisse[index].echeanceCheque?:"N/A",
                            textAlign = TextAlign.Center,
                            fontSize = MaterialTheme.typography.bodySmall.fontSize,
                            modifier = Modifier.fillMaxWidth(0.25f)
                        )
                        Text(
                            text = StringUtils.convertStringToPriceFormat(listChequeCaisse[index].montant),
                            fontSize = MaterialTheme.typography.bodySmall.fontSize,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth(0.25f)
                        )
                    }



                    HorizontalDivider(color = MaterialTheme.colorScheme.outline)
                }
            }
            Spacer(modifier = Modifier.height(55.dp))
        }
    }





}

@Composable
fun ChequeTableHeader(rowTitls : List<String>) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceEvenly,
        modifier = Modifier
            .heightIn(45.dp)
            .fillMaxWidth()
            //  .background(color = MaterialTheme.colorScheme.scrim)
            .clickable {
            }
    ) {

        Text(
            text = rowTitls[0],
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth(0.5f)
        )
        Text(
            text = rowTitls[1],
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth(0.25f)
        )

        Text(
            text = rowTitls[2],
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth(0.25f)
        )

    }
}