package com.asmtunis.procaisseinventory.pro_caisse.global_data_class

import com.asmtunis.procaisseinventory.articles.data.article.local.repository.article_codebar.ArticleBarCodeLocalRepository
import com.asmtunis.procaisseinventory.articles.data.article.local.repository.articles.ArticlesLocalRepository
import com.asmtunis.procaisseinventory.articles.data.article.local.repository.client_article_prix.ClientArticlePrixLocalRepository
import com.asmtunis.procaisseinventory.articles.data.priceperstation.local.repository.PricePerStationLocalRepository
import com.asmtunis.procaisseinventory.articles.data.unite_article.local.repository.UniteArticleLocalRepository
import com.asmtunis.procaisseinventory.auth.base_config.data.local.repository.BaseConfigLocalRepository
import com.asmtunis.procaisseinventory.auth.login.data.local.repository.AuthorizationLocalRepository
import com.asmtunis.procaisseinventory.auth.login.data.local.repository.UtilisateurRoomRepository
import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.local.repository.LicenceLocalRepository
import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
import com.asmtunis.procaisseinventory.data.banques.local.repository.BanqueLocalRepository
import com.asmtunis.procaisseinventory.data.carte_resto.local.repository.CarteRestoLocalRepository
import com.asmtunis.procaisseinventory.data.cheque_caisse.local.repository.ChequeCaisseLocalRepository
import com.asmtunis.procaisseinventory.data.devise.local.repository.DeviseLocalRepository
import com.asmtunis.procaisseinventory.data.etablisement.local.repository.EtablisementLocalRepository
import com.asmtunis.procaisseinventory.data.exercice.local.repository.ExerciceLocalRepository
import com.asmtunis.procaisseinventory.data.facture.local.repository.FactureLocalRepository
import com.asmtunis.procaisseinventory.data.image_piece_joint.local.repository.InventairePieceJointLocalRepository
import com.asmtunis.procaisseinventory.data.prefixe.local.repository.PrefixeLocalRepository
import com.asmtunis.procaisseinventory.data.sessioncaisse.local.repository.SessionCaisseLocalRepository
import com.asmtunis.procaisseinventory.data.statistiques.local.repository.StatisticsLocalRepository
import com.asmtunis.procaisseinventory.data.ticket_resto.local.repository.TraiteCaisseLocalRepository
import com.asmtunis.procaisseinventory.data.timbre.local.repository.TimbreLocalRepository
import com.asmtunis.procaisseinventory.data.ville.local.repository.VilleLocalRepository
import com.asmtunis.procaisseinventory.network_errors.local.repository.NetworkErrorsLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.bon_commande.repository.BonCommandeLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.ligne_bon_commande.repository.LigneBonCommandeLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.ligne_ticket.repository.LigneTicketLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.ticket.repository.TicketLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.bon_retour.repository.BonRetourLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.ligne_bon_retour.repository.LigneBonRetourLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.client.data.local.repository.ClientRoomRepository
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.famille.repository.FamilleDnLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.superficie.repository.SuperficieLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.typepointvente.repository.TypePointVenteLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.typeservice.repository.TypeServicesLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.visite.repository.VisiteLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.local.repository.DeplacementOutByUserLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.repository.BatimentsByUserLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.repository.ImmobilisationLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.repository.TypeMouvementLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.local.repository.InventaireLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.local.respository.ReglementCaisseLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.etat_ordre_mission.repository.EtatOrdreMissionLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.ligne_ordre_mission.repository.LigneOrdreMissionLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.ordre_mission.repository.OrdreMissionLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.local.repository.AutreVCLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.image.repository.ImageVCLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.liste_concurrent.repository.ListConcurrentVCLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.type_communication.repository.TypeCommunicationVCLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.local.repository.NewProductVCLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.local.repository.PrixVCLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.local.repository.PromoVCLocalRepository
import javax.inject.Inject
import javax.inject.Named

data class ProCaisseLocalDb @Inject constructor(

    val dataStore: DataStoreRepository,
    @Named("NetworkErrors") val networkErrors: NetworkErrorsLocalRepository,

    @Named("BaseConfig") val baseConfig: BaseConfigLocalRepository,
    @Named("Immobilisation") val immobilisation: ImmobilisationLocalRepository,
    @Named("TypeMouvement") val typeMouvement: TypeMouvementLocalRepository,
    @Named("Licence") val licence: LicenceLocalRepository,
    @Named("Clients") val clients: ClientRoomRepository,
    @Named("Utilisateur") val utilisateur: UtilisateurRoomRepository,
    @Named("Authorization") val authorization: AuthorizationLocalRepository,
    @Named("Articles") val articles: ArticlesLocalRepository,
    @Named("UniteArticles") val uniteArticles: UniteArticleLocalRepository,
    @Named("ArticleCodeBar") val articlesBarCode: ArticleBarCodeLocalRepository,
    @Named("ClientArticlePrix") val clientArticlePrix: ClientArticlePrixLocalRepository,
    @Named("Etablissement") val etablisement: EtablisementLocalRepository,
    @Named("Banque") val banque: BanqueLocalRepository,
    @Named("Facture") val facture: FactureLocalRepository,
    @Named("Devise") val devise: DeviseLocalRepository,
    @Named("CarteResto") val carteResto: CarteRestoLocalRepository,
    @Named("TraiteCaisse") val ticketResto: TraiteCaisseLocalRepository,
    @Named("ChequeCaisse") val chequeCaisse: ChequeCaisseLocalRepository,
    @Named("Statistiques") val statistiques: StatisticsLocalRepository,
    @Named("Timbre") val timbre: TimbreLocalRepository,
    @Named("Prefix") val prefix: PrefixeLocalRepository,
    @Named("Exercice") val exercice: ExerciceLocalRepository,
    @Named("PricePerStation") val pricePerStation: PricePerStationLocalRepository,
    @Named("SessionCaisse") val sessionCaisse: SessionCaisseLocalRepository,
    @Named("ReglementCaisse") val reglementCaisse: ReglementCaisseLocalRepository,
    @Named("Ville") val ville: VilleLocalRepository,

    //Bon Livraison
    @Named("Ticket") val bonLivraison: TicketLocalRepository,
    @Named("LigneTicket") val ligneBonLivraison: LigneTicketLocalRepository,

    //Bon Commande
    @Named("BonCommande") val bonCommande: BonCommandeLocalRepository,
    @Named("LigneBonCommande") val ligneBonCommande: LigneBonCommandeLocalRepository,
    @Named("InventairePatrimoine") val invePatrimoine: InventaireLocalRepository,


    //BON RETOUR
    @Named("BonRetour") val bonRetour: BonRetourLocalRepository,
    @Named("BonRetourDetail") val ligneBonRetour: LigneBonRetourLocalRepository,

    //ORDRE MISSION **** TOURNEE
    @Named("OrdreMission") val ordreMission: OrdreMissionLocalRepository,
    @Named("LigneOrdreMission") val ligneOrdreMission: LigneOrdreMissionLocalRepository,
    @Named("EtatOrdreMission") val etatOrdreMission: EtatOrdreMissionLocalRepository,


    //DISTRIBUTION NUMERIQUE
    @Named("TypeService") val typeServicesDn: TypeServicesLocalRepository,
    @Named("FamilleDn") val familleDn: FamilleDnLocalRepository,
    @Named("Superficie") val superficieDn: SuperficieLocalRepository,
    @Named("TypePointVente") val typePointVenteDn: TypePointVenteLocalRepository,
    @Named("Visites") val visitesDn: VisiteLocalRepository,

    //Veille Concurentiel
    @Named("ListeConcurrent") val listConcurrentVC: ListConcurrentVCLocalRepository,
    @Named("NewProduct") val newProductVC: NewProductVCLocalRepository,
    @Named("Promo") val promoVC: PromoVCLocalRepository,
    @Named("Prix") val prixVC: PrixVCLocalRepository,
    @Named("Autre") val autreVC: AutreVCLocalRepository,
    @Named("TypeCommunication") val typeCommunicationVC: TypeCommunicationVCLocalRepository,
    @Named("Image") val imageVC: ImageVCLocalRepository,


    //Inventaire
    @Named("InventairePieceJoint") val inventairePieceJoint: InventairePieceJointLocalRepository,
    @Named("BatimentByUser") val batimentByUser: BatimentsByUserLocalRepository,
    @Named("DeplacementOutByUser") val deplacementOutByUser: DeplacementOutByUserLocalRepository,

    )
