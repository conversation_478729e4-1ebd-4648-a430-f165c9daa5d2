package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.api.bon_commande

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.InvPatBatchResponse
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class BonCommandeApiImpl(private val client: HttpClient) : BonCommandeApi {


    override suspend fun getBonCommandes(
        baseConfig: String
    ): Flow<DataResult<List<BonCommande>>> = flow {
        val result = executePostApiCall<List<BonCommande>>(
            client = client,
            endpoint = Urls.GET_COMMANDE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun addBatchBonCommande(baseConfig: String): Flow<DataResult<List<InvPatBatchResponse>>> = flow {

        val result = executePostApiCall<List<InvPatBatchResponse>>(
            client = client,
            endpoint = Urls.ADD_BATCH_BON_COMMANDE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
    }