<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CouleurController extends Controller
{

    public function getCouleurs(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('couleur')->get());

    }

    public function addCouleurMobile(Request $request)
    {
        $items = $request->json()->all();

        $connection = DatabaseConnection::setConnection($items);

        if (!empty($items)) {
            // $f = $connection->table('famille')->insert($data);
            foreach ($items["object"]["couleurs"] as $data) {
                if($data["COU_DDm"]=="NULL")
			$data["COU_DDm"]=NULL;
			if($data["COU_Station"]=="NULL")
			$data["COU_Station"]=NULL;
			if($data["COU_Designation"]=="NULL")
			$data["COU_Designation"]=NULL;
			if($data["COU_export"]=="NULL")
            $data["COU_export"]=NULL;
            
            $exist=$connection->table('couleur')
				->where('COU_Code', '=', $data['COU_Code'])
				->get();
				if(is_null($exist) || $exist->isEmpty()){
					$f=$connection->table('couleur')->insert($data);
				}else{
				$f=	$connection->table('couleur')
				->where('COU_Code', $data['COU_Code'])
					->update([
						'COU_DDm' => $data['COU_DDm'],
						'COU_Designation' => $data['COU_Designation'],
						'COU_export' => $data['COU_export'],
						'COU_Station' => $data['COU_Station'],
						'COU_User' => $data['COU_User']
					 ]);
				}
             
            }
            return response()->json($f);

        } else {
            return (false);
        }

    }

    public function updateFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        $connection->table('famille')->where('FAM_Code', $data["FAM_Code"])->update($data);

        return ("Data modified");

    }

    public function deleteFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $connection->table('famille')->where('FAM_Code', $data["object"])->delete();
        return ("Data Deleted");
    }

    public function updateOrCreate($connection, $data, $table, $whereClauses)
    {
        $exists = $connection->table($table)->where($whereClauses)->first();

        if ($exists) {
            $connection->table($table)->where($whereClauses)->delete();
        }

        return $connection->table($table)->insert($data);

    }
}
