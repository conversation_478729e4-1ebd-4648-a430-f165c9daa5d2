package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.bon_retour.repository

import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetourWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetourWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.bon_retour.dao.BonRetourDAO
import kotlinx.coroutines.flow.Flow


class BonRetourLocalRepositoryImpl(
    private val bonRetourDAO: BonRetourDAO
) : BonRetourLocalRepository {
    override fun upsertAll(value: List<BonRetour>) = bonRetourDAO.insertAll(value)

    override fun upsert(value: BonRetour) = bonRetourDAO.insert(value)
    override fun notSynced(): Flow<Map<BonRetour, List<LigneBonRetour>>?> = bonRetourDAO.notSynced
    override fun setSynced(bonRetourNum: String, bonRetourNumM: String)  = bonRetourDAO.setSynced(bonRetourNum, bonRetourNumM)

    override fun deleteAll() = bonRetourDAO.deleteAll()
    override fun deleteByCodeM(code: String, exercice: String) = bonRetourDAO.deleteById(codeRetour = code, exercice = exercice)

    override fun getNewCode(prefix: String): Flow<String>  = bonRetourDAO.getNewCode(prefix)

    override fun getAll(station : String): Flow<Map<BonRetour, List<LigneBonRetour>>>  = bonRetourDAO.getByStation(station)



    override fun getByClient(codeClient: String): Flow<Map<BonRetour, List<LigneBonRetourWithArticle>>> =
        bonRetourDAO.getByClient(codeClient)

    override fun filterByCodeFournisseur(
        searchString: String,
        sortBy: String,
        isAsc: Int,
        station : String
    ): Flow<Map<BonRetourWithClient, List<LigneBonRetourWithArticle>>> = bonRetourDAO.filterByCodeFournisseur(
        searchString = searchString,
        sortBy = sortBy,
        isAsc = isAsc,
        station = station
    )

    override fun filterByNomFournisseur(
        searchString: String,
        sortBy: String,
        isAsc: Int,
        station : String
    ): Flow<Map<BonRetourWithClient, List<LigneBonRetourWithArticle>>> = bonRetourDAO.filterByNomFournisseur(
        searchString = searchString,
        sortBy = sortBy,
        isAsc = isAsc,
        station = station
    )

    override fun filterByNumero(
        searchString: String,
        sortBy: String,
        isAsc: Int,
        station : String
    ): Flow<Map<BonRetourWithClient, List<LigneBonRetourWithArticle>>> = bonRetourDAO.filterByNumero(
        searchString = searchString,
        sortBy = sortBy,
        isAsc = isAsc,
        station = station
    )

    override fun getAllFiltred(
        isAsc: Int,
        sortBy: String,
        station : String
    ): Flow<Map<BonRetourWithClient, List<LigneBonRetourWithArticle>>> = bonRetourDAO.getAllFiltred(
        sortBy = sortBy,
        isAsc = isAsc,
        station = station
    )
}