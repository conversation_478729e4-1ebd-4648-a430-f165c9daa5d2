package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.TYPE_SERVICE_TABLE)
@Serializable
data class TypeServicesDn (
    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = "CodeTypeSv")
    @SerialName("CodeTypeSv")
    var codeTypeSv: String = "",

    @ColumnInfo(name = "TypeSv")
    @SerialName("TypeSv")
    var typeSv: String = "",

    @ColumnInfo(name = "NoteSv")
    @SerialName("NoteSv")
    var noteSv: String? = "",

    @ColumnInfo(name = "EtatPv")
    @SerialName("EtatPv")
    var etatSV : Int? = 0
)
