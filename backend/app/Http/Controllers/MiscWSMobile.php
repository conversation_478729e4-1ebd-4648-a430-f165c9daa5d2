<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use App\Http\Requests;
use File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

class MiscWSMobile extends Controller
{


    public function getStatistics(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        /*

        $result;

                 $result1 = collect($connection->select("SELECT   SUM(TIK_MtTTC) AS CA, COUNT(TIK_CodClt) AS Nbr_Client, COUNT(TIK_NumTicket) AS NbreTicket
        FROM                       Ticket
        WHERE               (TIK_user = '".$data["object"][1]."') AND (ISNULL(TIK_Annuler, 'False') <> 'True')"))->first();



        $result2 =  collect($connection->select(" SELECT               SUM(LigneTicket.LT_Qte) AS LT_Qte
        FROM                       LigneTicket INNER JOIN
                                                        Ticket ON LigneTicket.LT_NumTicket = Ticket.TIK_NumTicket AND LigneTicket.LT_Exerc = Ticket.TIK_Exerc AND
                                                        LigneTicket.LT_IdCarnet = Ticket.TIK_IdCarnet
        WHERE               (Ticket.TIK_user = '".$data["object"][1]."') AND (ISNULL(Ticket.TIK_Annuler, 'False') <> 'True')"))->first();



        $result3 =  collect($connection->select("SELECT SUM(Mnt_Espece) AS Mnt_Espece, SUM(Mnt_Carte_Bancaire) AS Mnt_Carte_Bancaire, SUM(Mnt_Cheque) AS Mnt_Cheque,
         SUM(Mnt_Traite) AS Mnt_Traite, SUM(Mnt_Espece) + SUM(Mnt_Cheque) + SUM(Mnt_Traite)
         AS total,
         SUM(ISNULL(Mnt_Bonus, 0)) AS Mnt_Bonus, REGC_IdSCaisse,
          SUM(DISTINCT SC_FondCaisse) AS Fond_Caisse,
           SUM(DISTINCT SC_TotDepense) AS Dep_Caisse,
           SUM(ISNULL(MntCarte_prepayee, 0)) AS MntCarte_prepayee,
             SUM(ISNULL(Mnt_PointMerci, 0)) AS Mnt_PointMerci, SUM(ISNULL(MntBonAchat, 0)) AS MntBonAchat, NomPrenUtil
             FROM View_Journal_Reglement_001_3 WHERE      (REGC_USER = '".$data["object"][1]."')   GROUP BY REGC_IdSCaisse, NomPrenUtil"))->first();
        */
        /*
        if ($result3!=null) {
        $result["Mnt_Espece"]= $result3->Mnt_Espece;
        $result["Mnt_Carte_Bancaire"]= $result3->Mnt_Carte_Bancaire;
        $result["Mnt_Cheque"]= $result3->Mnt_Cheque;
        $result["Mnt_Traite"]= $result3->Mnt_Traite;
        $result["total"]= $result3->total;
        $result["Mnt_Bonus"]= $result3->Mnt_Bonus;
        $result["REGC_IdSCaisse"]= $result3->REGC_IdSCaisse;
        $result["Fond_Caisse"]= $result3->Fond_Caisse;
        $result["Dep_Caisse"]= $result3->Dep_Caisse;
        $result["MntCarte_prepayee"]= $result3->MntCarte_prepayee;
        $result["Mnt_PointMerci"]= $result3->Mnt_PointMerci;
        $result["MntBonAchat"]= $result3->MntBonAchat;
        $result["NomPrenUtil"]= $result3->NomPrenUtil;}


        $result["CA"]= $result1->CA;
        $result["Nbr_Client"]= $result1->Nbr_Client;
        $result["NbreTicket"]= $result1->NbreTicket;
        $result["LT_Qte"]= $result2->LT_Qte;
         //(TIK_IdSCaisse = '".$data["object"][0]."')
        $result["TopClients"]= collect($connection->select("SELECT       top(5)       SUM(TIK_MtTTC) AS CA, TIK_CodClt as CLI_Code,TIK_NomClient as CLI_NomPren
        from dbo.Ticket
        WHERE           (TIK_user = '".$data["object"][1]."')  AND        (ISNULL(TIK_Annuler, 'False') <> 'True')
        group by TIK_CodClt,TIK_NomClient
        order by SUM(TIK_MtTTC) desc"));

        //$result4=collect($connection->select("select sum([dbo].GetSoldeClient (CLI_Code)) as Mnt_Credit from client"))->first();
        $result4=collect($connection->select("select isnull(SUM(TIK_MtTTC)*(-1),0) as Mnt_Credit from Ticket where TIK_Etat='Credit' and TIK_user='".$data["object"][1]."' and (TIK_IdSCaisse <> '".$data["object"][0]."')"))->first();



        $result["Mnt_Credit"]= $result4->Mnt_Credit;
        */


        return response()->json(null);

    }

    public function getReglementStatistics(Request $request)
    {

        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        return response()->json(collect($connection->select("SELECT SUM(Mnt_Espece) AS Mnt_Espece, SUM(Mnt_Carte_Bancaire) AS Mnt_Carte_Bancaire, SUM(Mnt_Cheque) AS Mnt_Cheque,
 SUM(Mnt_Traite) AS Mnt_Traite, SUM(Mnt_Espece) + SUM(Mnt_Carte_Bancaire) + SUM(Mnt_Cheque) + SUM(Mnt_Traite) 
 + SUM(ISNULL(MntCarte_prepayee, 0)) + SUM(ISNULL(Mnt_PointMerci, 0))
 + SUM(ISNULL(MntBonAchat, 0)) + SUM(ISNULL(Mnt_Bonus, 0)) AS total, 
 SUM(ISNULL(Mnt_Bonus, 0)) AS Mnt_Bonus, REGC_IdSCaisse,
  SUM(DISTINCT SC_FondCaisse) AS Fond_Caisse,
   SUM(DISTINCT SC_TotDepense) AS Dep_Caisse,
   SUM(ISNULL(MntCarte_prepayee, 0)) AS MntCarte_prepayee, 
     SUM(ISNULL(Mnt_PointMerci, 0)) AS Mnt_PointMerci, SUM(ISNULL(MntBonAchat, 0)) AS MntBonAchat, NomPrenUtil 
	 FROM View_Journal_Reglement_001_3 WHERE (REGC_IdSCaisse = '" . $data["object"] . "') GROUP BY REGC_IdSCaisse, NomPrenUtil"))->first());
    }


    public function getVenteStatistics(Request $request)
    {
        $data = $request->json()->all();
        $result = null;
        $connection = DatabaseConnection::setConnection($data);

        $result1 = collect($connection->select("SELECT   SUM(TIK_MtTTC) AS CA, COUNT(TIK_CodClt) AS Nbr_Client, COUNT(TIK_NumTicket) AS NbreTicket
FROM                       Ticket
WHERE               (TIK_IdSCaisse = '" . $data["object"] . "') AND (ISNULL(TIK_Annuler, 'False') <> 'True')"))->first();


        $result2 = collect($connection->select(" SELECT               SUM(LigneTicket.LT_Qte) AS LT_Qte
FROM                       LigneTicket INNER JOIN
                                                Ticket ON LigneTicket.LT_NumTicket = Ticket.TIK_NumTicket AND LigneTicket.LT_Exerc = Ticket.TIK_Exerc AND 
                                                LigneTicket.LT_IdCarnet = Ticket.TIK_IdCarnet
WHERE               (Ticket.TIK_IdSCaisse = '" . $data["object"] . "') AND (ISNULL(Ticket.TIK_Annuler, 'False') <> 'True')"))->first();


        $result["CA"] = $result1->CA;
        $result["Nbr_Client"] = $result1->Nbr_Client;
        $result["NbreTicket"] = $result1->NbreTicket;
        $result["LT_Qte"] = $result2->LT_Qte;


        return response()->json($result);


    }


    public function getTopNClients(Request $request)
    {

        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);


        return response()->json(collect($connection->select("SELECT       top(5)       SUM(TIK_MtTTC) AS CA, TIK_CodClt as CLI_Code,TIK_NomClient as CLI_NomPren 
from dbo.Ticket
WHERE               (TIK_IdSCaisse = '" . $data["object"] . "') AND   (ISNULL(TIK_Annuler, 'False') <> 'True')
group by TIK_CodClt,TIK_NomClient
order by SUM(TIK_MtTTC) desc")));


    }

    public function migrate()
    {
        return response()->json([
            'status' => 'success',
            'error' => false,
            'message' => "Migrated successfully",
            'data' => "Migrated successfully"
        ], 200, ['Content-type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);

    }


    public function checkConnectivity()
    {
        try {
            DB::getPdo();
            return response()->json([
                'status' => 'success',
                'error' => false,
                'message' => "Connected",
                'data' => "Connected"
            ], 200, ['Content-type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'error' => true,
            ], 500, ['Content-type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
        }
    }


    public function refresh()
    {
        $exitCode = Artisan::call('optimize');
        $exitCode = Artisan::call('view:clear');
        $exitCode = Artisan::call('cache:clear');
        $exitCode = Artisan::call('config:cache');
        $exitCode = Artisan::call('clear-compiled');
        return response()->json([
            'status' => 'success',
            'error' => false,
            'message' => "Refreshed successfully",
            'data' => "Refreshed successfully",
        ], 200, ['Content-type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }


}
