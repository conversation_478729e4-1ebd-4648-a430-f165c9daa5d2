<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use App\Helpers\Enum;
use App\Http\Requests;
use App\Models\Ticket;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ReglementCaisseWSMobile extends Controller
{


    /*fonction get data from database*/

    public function getReglementCaisse(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('ReglementCaisse')->get());
    }


    public function getReglementCaisseBySession(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $exercice = $request->query('exercice');
        $archive = $request->query('archive');
        if($archive == 'true') {
            $ReglementCaisse = $connection->table('ReglementCaisse')->where('REGC_IdSCaisse', '<>',$data["object"])
                ->where('REGC_Exercice','=',$exercice)
                ->get();
        }
        if($archive == 'false') {
            $ReglementCaisse = $connection->table('ReglementCaisse')->where('REGC_IdSCaisse', $data["object"])->get();
        }
        return response()->json($ReglementCaisse);
    }


    public function getReglementCaisseByTicket(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('ReglementCaisse')
            ->where('REGC_NumTicket', $data["object"]["TIK_NumTicket"])
            ->where('REGC_Exercice', $data["object"]["TIK_Exerc"])
            ->where('REGC_IdSCaisse', $data["object"]["TIK_IdSCaisse"])
            ->get());
    }


    public function getReglementCaisseByTickets(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        $items = [];

        foreach ($data["object"] as &$item) {
            array_push($items, $connection->table('ReglementCaisse')
                ->select('REGC_Code', 'REGC_Exercice', 'REGC_IdCarnet', 'REGC_NumTicket', 'REGC_IdSCaisse', 'REGC_IdCaisse', 'REGC_IdStation', 'REGC_CodeClient', 'REGC_NomPrenom', 'REGC_MatFiscale', 'REGC_NumCIN', 'REGC_ModeReg', 'REGC_DateReg', 'REGC_MntEspece', 'REGC_MntCarteBancaire', 'REGC_NumCarteBancaire', 'REGC_MntChéque as REGC_MntCheque', 'REGC_MntTraite', 'REGC_Remarque', 'REGC_Montant', 'REGC_Etat', 'REGC_Station', 'REGC_User', 'REGC_NumTransactCB', 'REGC_NomClientCB', 'REGC_MntTotalRecue', 'REGC_MntEspeceRecue', 'REGC_export', 'REGC_DDm', 'REGC_CODE_COMMERCIAL', 'REGC_DESIG_COMMERCIAL', 'REGC_Bonus', 'REGC_MntCarte_prepayee', 'REGC_NumCarte_prepayee', 'REGC_Mnt_PointMerci', 'REGC_NumBonAchat', 'REGC_MntBonAchat')
                ->where('REGC_NumTicket', $item["TIK_NumTicket"])
                ->where('REGC_IdCarnet', $item["TIK_IdCarnet"])
                ->where('REGC_Exercice', $item["TIK_Exerc"])
                ->get());
        }
        return response()->json($items, 200, ['Content-type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);

    }


    public function getDeviseByTicket(Request $request)
    {
        return response()->json(DB::table('Devise')->where($request->field, $request->value)->get());
    }

    public function getDeviseByActive(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Devise')->where('Principale', 1)->where('Activite', 1)->get()->first());
    }

    public function theirIsTicketZero($connection, $data)
    {
        return $connection->table('Ticket')->where("TIK_Exerc", $data["exercice"])
            ->where("TIK_IdCarnet", $data["carnet"])
            ->where("TIK_NumTicket", "0")->count();
    }

    public function addBatchPayments(Request $request)
    {
        $items = $request->json()->all();
        $connection = DatabaseConnection::setConnection($items);
        $result = array();
        if (!empty($items)) {
            foreach ($items["object"] as $data) {
                if (!empty($data)) {
                    $connection->beginTransaction();
                    try {
                        $TicketController = new TicketWSMobile();
                        //Reglement
                        $testReglement = $TicketController->addRegelement(null, $data, $connection);
                        if (!$testReglement) {
                            $soldeClient =  Ticket::SoldeClient($connection, $data["reglement"]['REGC_CodeClient']);
                            $result[] = $this->setRegelement((new PrefixWSMobile)->getReglementPrefix($connection,
                                "ReglementCaisse",
                                $data["reglement"]['REGC_Exercice'], "REGC_IdSCaisse",
                                $data["reglement"]['REGC_IdSCaisse']), $data["reglement"]['REGC_IdSCaisse'], $data["reglement"]['REGC_Exercice'],
                                $data['reglement']['REGC_Code_M'],$data["reglement"]['REGC_CodeClient'],$soldeClient['SoldeClient'],$soldeClient['Debit'],$soldeClient['Credit'],
                                "Error dans l'insertion du reglement", Enum::Error_Insert_ReglementCaisse

                            );
                            Log::error("Error dans l'insertion du reglement",$result);
                            throw new \Exception("", Enum::Error_Insert_ReglementCaisse);

                        } else {
                            $AddedReglement = $connection->table('ReglementCaisse')
                                ->where('REGC_Code_M', "=", $data['reglement']['REGC_Code_M'])
                                ->first();
                            $testTraites = $TicketController->addTraites($data, $connection, $AddedReglement);
                            $testcheques = $TicketController->addCheques($data, $connection, $AddedReglement);
                            if (!$testTraites) {
                                $soldeClient =  Ticket::SoldeClient($connection, $data["reglement"]['REGC_CodeClient']);
                                $result[] = $this->setRegelement($AddedReglement->REGC_Code, $AddedReglement->REGC_IdSCaisse,
                                    $AddedReglement->REGC_Exercice,
                                    $AddedReglement->REGC_Code_M,$data["reglement"]['REGC_CodeClient'],$soldeClient['SoldeClient'],$soldeClient['Debit'],$soldeClient['Credit'],
                                    "Error dans l'insertion des traites",
                                    Enum::Error_Insert_TraiteCaisse);
                                Log::error("Error dans l'insertion des traites",$result);
                                throw new \Exception("", Enum::Error_Insert_TraiteCaisse);
                            } else if (!$testcheques) {
                                $soldeClient =  Ticket::SoldeClient($connection, $data["reglement"]['REGC_CodeClient']);
                                $result[] = $this->setRegelement($AddedReglement->REGC_Code, $AddedReglement->REGC_IdSCaisse,
                                    $AddedReglement->REGC_Exercice,
                                    $AddedReglement->REGC_Code_M,$data["reglement"]['REGC_CodeClient'],$soldeClient['SoldeClient'],$soldeClient['Debit'],$soldeClient['Credit']
                                    , "Error dans l'insertion des cheques",
                                    Enum::Error_Insert_ReglementCaisse);
                                Log::error("Error dans l'insertion des cheques",$result);
                                throw new \Exception("", Enum::Error_Insert_Cheque);
                            } else {
                                $soldeClient =  Ticket::SoldeClient($connection, $data["reglement"]['REGC_CodeClient']);
                                $result[] = $this->setRegelement($AddedReglement->REGC_Code, $AddedReglement->REGC_IdSCaisse,
                                    $AddedReglement->REGC_Exercice,
                                    $AddedReglement->REGC_Code_M,$data["reglement"]['REGC_CodeClient'], $soldeClient['SoldeClient'],$soldeClient['Debit'],$soldeClient['Credit'],
                                    "INSERTED Regelement",
                                    Enum::Success_Code);

                            }

                        }

                        $connection->commit();

                    } catch
                    (\Exception $e) {
                        $connection->rollBack();

                    }

                }

            }
            return response()->json($result);
        }else{
            return response()->json("Empty Object");
        }
    }

    public function updateOrCreate($connection, $data, $table, $whereClauses)
    {

        try {
            $query = $connection->table($table);
            foreach ($whereClauses as $key => $value) {
                $query->where($value[0], $value[1], $value[2]);
            }
            $query->delete();

        } catch (\Exception $e) {
        }

        return $connection->table($table)->insert($data);
    }

}
