package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.typepointvente.repository


import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypePointVenteDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.typepointvente.dao.TypePointVenteDAO
import kotlinx.coroutines.flow.Flow


class TypePointVenteLocalRepositoryImpl(
    private val typePointVenteDAO: TypePointVenteDAO
) : TypePointVenteLocalRepository {
    override fun upsert(value: TypePointVenteDn)  = typePointVenteDAO.insert(value)

    override fun upsertAll(value: List<TypePointVenteDn>)  = typePointVenteDAO.insertAll(value)

    override fun deleteAll()  = typePointVenteDAO.deleteAll()

    override fun getAll(): Flow<List<TypePointVenteDn>>  = typePointVenteDAO.all
}