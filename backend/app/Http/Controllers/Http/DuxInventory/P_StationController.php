<?php

namespace App\Http\Controllers\Http\DuxInventory;


use App\Models\DuxInventory\P_Station;


class P_StationController extends BaseController
{
    public function __construct()
    {
        $this->modelClass = P_Station::class;
    }

    public function getAllStationActive()
    {
        try {
            $data = P_Station::from('P_Station as s')
                ->select('id', 'libelle', 'alertQteTransfere')
                ->where('s.active', '=', '1')
                ->get();

            return response()->success($data);
        } catch (\Exception $e) {
            return response()->error($e);
        }
    }
}
