package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.superficie.repository

import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.SuperficieDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.superficie.dao.SuperficieDAO
import kotlinx.coroutines.flow.Flow



class SuperficieLocalRepositoryImpl(
    private val superficieDAO: SuperficieDAO
) : SuperficieLocalRepository {
    override fun upsert(value: SuperficieDn) = superficieDAO.insert(value)

    override fun upsertAll(value: List<SuperficieDn>) = superficieDAO.insertAll(value)


    override fun deleteAll() = superficieDAO.deleteAll()

    override fun getAll(): Flow<List<SuperficieDn>> = superficieDAO.all

}