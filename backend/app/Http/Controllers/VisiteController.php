<?php

namespace App\Http\Controllers;

use App\Helpers\AppHelper;
use App\Helpers\DB;
use App\Helpers\Enum;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;
use Illuminate\Support\Str;
use Exception;

class VisiteController extends Controller
{

    public function getAllFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        return response()->json($connection->table('DN_Famille')->get());

    }

    public function getAllTypePVente(Request $request)
    {
        $data = $request->json()->all();


        $connection = DatabaseConnection::setConnection($data);

        return response()->json($connection->table('DN_TypePVente ')->get());

    }

    public function getAllTypeService(Request $request)

    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        return response()->json($connection->table('DN_TypeService')->get());

    }

    public function getAllSuperficie(Request $request)

    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        return response()->json($connection->table('DN_Superficie')->get());

    }

    public function getAllVisite(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        try {
            $query = $connection->table("DN_Visite");

            if ($request->hasAny('user') && !!$request->query('user')) {
                $query = $query->where('VIS_User', $request->query('user'));
            }
            return response()->json($query->get());
        } catch (\Exception $e) {

        }


        return response()->json($connection->table('DN_Visite')->get());

    }

    public function getVisiteByCode(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $item = $data["object"];
        return response()->json($connection->table('DN_Visite')
            ->where('VIS_Exerc', $item["VIS_Exerc"])
            ->where('VIS_Num', $item["VIS_Num"])->get());

    }

    public function getAllLigneVisite(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $query = $connection->table("DN_LigneVisite");

        if ($request->hasAny('user') && !!$request->query('user')) {

            $query = $query->leftJoin('DN_Visite', 'DN_Visite.VIS_Num', '=', 'DN_LigneVisite.LG_VISNum')
                ->select('DN_LigneVisite.*','DN_Visite.VIS_User')
                ->where('DN_Visite.VIS_User',$request->query('user') );
        }
        return response()->json($query->get());
    }


    public function addBatchVisite(Request $request)
    {
        $items = $request->json()->all();
        $connection = DatabaseConnection::setConnection($items);

        $result = [];
        if (!empty($items)) {

            foreach ($items["object"] as &$data) {
                $connection->beginTransaction();
                try {
                    if (!empty($data)) {
                        $visite = $data["DN_Visite"];

                        $visite['VIS_Date'] = AppHelper::setDateFormat($visite['VIS_Date']);
                        $visite['VIS_DDM'] = AppHelper::setDateFormat($visite['VIS_DDM']);
                        $exist = $connection->table("DN_Visite")
                            ->where('VIS_Num', '=', $visite["VIS_Num"])
                            ->where('VIS_Code_M', '=', $visite["VIS_Code_M"])
                            ->where('VIS_Exerc', '=', $visite["VIS_Exerc"])->first();


                        if (!$exist) {
                            $prefixController = new PrefixWSMobile();
                            $visite["VIS_Num"] = $prefixController->getDNVisitePrefix($connection,
                                "DN_Visite",
                                $visite['VIS_Exerc'],
                                $visite['VIS_User']);

                            $succesInsertvisite = $connection->table('DN_Visite')->insert($visite);

                            if (!$succesInsertvisite) {
                                $result[] = [
                                    "VIS_Num" => $visite["VIS_Num"],
                                    "VIS_Exerc" => $visite["VIS_Exerc"],
                                    "VIS_Code_M" => $visite["VIS_Code_M"],
                                    "message" => "Erreur dans l'insertion visite",
                                    "code" => Enum::Error_Insert_DN_Visite
                                ];
                                throw new Exception("",Enum::Error_Insert_DN_Visite);
                            } else {

                                $ligneVisites = $data["DN_LigneVisite"];
                                $nbLigneVisite = count($ligneVisites);
                                $countLigneVisite = 0;


                                foreach ($ligneVisites as &$ligneVisite) {
                                    $ligneVisiteExist = $connection->table('DN_LigneVisite')
                                        ->where('LG_VISNum', '=', $visite["VIS_Num"])
                                        ->where('LG_VISTier', '=', $ligneVisite["LG_VISTier"])
                                        ->where('LG_VISFamille', '=', $ligneVisite["LG_VISFamille"])
                                        ->where('LG_VISExerc', '=', $ligneVisite["LG_VISExerc"])
                                        ->first();

                                    if (empty($ligneVisiteExist)) {

                                        $ligne["LG_VISNum"] = $visite["VIS_Num"];
                                        $ligne["LG_VISFamille"] = $ligneVisite["LG_VISFamille"];
                                        $ligne["LG_VISTier"] = $ligneVisite["LG_VISTier"];
                                        $ligne["LG_VISExerc"] = $ligneVisite["LG_VISExerc"];
                                        $ligne["LG_VISInfo1"] = $ligneVisite["LG_VISInfo1"];
                                        $ligne["LG_VISInfo2"] = $ligneVisite["LG_VISInfo2"];
                                        $ligne["LG_VISInfo3"] = $ligneVisite["LG_VISInfo3"];
                                        $ligne["LG_VISDDM"] = AppHelper::setDateFormat($ligneVisite["LG_VISDDM"]);
                                        $succesInsertLigneVisite = $connection->table('DN_LigneVisite')->insert($ligne);

                                        if ($succesInsertLigneVisite) {
                                            $countLigneVisite++;
                                        }
                                    } else {
                                        $countLigneVisite++;
                                    }
                                }
                                if ($countLigneVisite < $nbLigneVisite) {

                                    $result[] = [
                                        "VIS_Num" => $visite["VIS_Num"],
                                        "VIS_Exerc" => $visite["VIS_Exerc"],
                                        "VIS_Code_M" => $visite["VIS_Code_M"],
                                        "message" => "Erreur dans l'insertion des lignes du visite" ,
                                        "code" => Enum::Error_Insert_Ligne_Visite
                                    ];
                                    throw new Exception("", Enum::Error_Insert_Ligne_Visite);
                                } else {
                                    $result[] = [
                                        "VIS_Num" => $visite["VIS_Num"],
                                        "VIS_Exerc" => $visite["VIS_Exerc"],
                                        "VIS_Code_M" => $visite["VIS_Code_M"],
                                        "message" => "INSERTED visite",
                                        "code" => Enum::Success_Code
                                    ];

                                }
                            }
                        } else {

                            $succesUpdatevisite = $connection->table('DN_Visite')->where("VIS_Num", $visite["VIS_Num"])
                                ->where("VIS_Code_M", $visite["VIS_Code_M"])
                                ->update([
                                    "VIS_Exerc" => $visite["VIS_Exerc"] ?? null,
                                    "VIS_Date" => $visite["VIS_Date"] ?? null,
                                    "VIS_CodeClient" => $visite["VIS_CodeClient"] ?? null,
                                    "VIS_User" => $visite["VIS_User"] ?? null,
                                    "VIS_NomMagazin" => $visite["VIS_NomMagazin"] ?? null,
                                    "VIS_Gouvernorat" => $visite["VIS_Gouvernorat"] ?? null,
                                    "VIS_Delegations" => $visite["VIS_Delegations"] ?? null,
                                    "VIS_Adresse" => $visite["VIS_Adresse"] ?? null,
                                    "VIS_NomGerant" => $visite["VIS_NomGerant"] ?? null,
                                    "VIS_NumTele" => $visite["VIS_NumTele"] ?? null,
                                    "VIS_TypePV" => $visite["VIS_TypePV"] ?? null,
                                    "VIS_TypeServ" => $visite["VIS_TypeServ"] ?? null,
                                    "VIS_Superf" => $visite["VIS_Superf"] ?? null,
                                    "VIS_DDM" => $visite["VIS_DDM"] ?? null,
                                    "VIS_Info1" => $visite["VIS_Info1"] ?? null,
                                    "VIS_Info2" => $visite["VIS_Info2"] ?? null,
                                    "VIS_Info3" => $visite["VIS_Info3"] ?? null,
                                ]);
                            if (!$succesUpdatevisite) {
                                $result[] = [
                                    "VIS_Num" => $visite["VIS_Num"],
                                    "VIS_Exerc" => $visite["VIS_Exerc"],
                                    "VIS_Code_M" => $visite["VIS_Code_M"],
                                    "message" => "Erreur dans la modification visite N° " . $visite["VIS_Num"],
                                    "code" => Enum::Error_Update_Visite
                                ];
                                throw new Exception("", Enum::Error_Update_Visite);

                            } else {
                                $ligneVisites = $data["DN_LigneVisite"];
                                $deleteLigneVisite = $connection->table('DN_LigneVisite')->where('LG_VISNum', '=', $visite["VIS_Num"])->delete();
                                $nbLigneVisite = count($ligneVisites);
                                $countLigneVisite = 0;
                                foreach ($ligneVisites as $ligneVisite) {

                                    $ligneVisiteExist = $connection->table('DN_LigneVisite')
                                        ->where('LG_VISNum', '=', $visite["VIS_Num"])
                                        ->where('LG_VISTier', '=', $ligneVisite["LG_VISTier"])
                                        ->where('LG_VISFamille', '=', $ligneVisite["LG_VISFamille"])
                                        ->where('LG_VISExerc', '=', $ligneVisite["LG_VISExerc"])
                                        ->first();

                                    if (empty($ligneVisiteExist)) {
                                        $ligne["LG_VISNum"] = $visite["VIS_Num"];
                                        $ligne["LG_VISFamille"] = $ligneVisite["LG_VISFamille"];
                                        $ligne["LG_VISTier"] = $ligneVisite["LG_VISTier"];
                                        $ligne["LG_VISExerc"] = $ligneVisite["LG_VISExerc"];
                                        $ligne["LG_VISInfo1"] = $ligneVisite["LG_VISInfo1"];
                                        $ligne["LG_VISInfo2"] = $ligneVisite["LG_VISInfo2"];
                                        $ligne["LG_VISInfo3"] = $ligneVisite["LG_VISInfo3"];
                                        $ligne["LG_VISDDM"] = AppHelper::setDateFormat($ligneVisite["LG_VISDDM"]);
                                        $succesInsertLigneVisite = $connection->table('DN_LigneVisite')->insert($ligne);
                                        if ($succesInsertLigneVisite) {
                                            $countLigneVisite++;
                                        }
                                    } else {
                                        $countLigneVisite++;
                                    }
                                }
                                if ($countLigneVisite < $nbLigneVisite) {
                                    $result[] = [
                                        "VIS_Num" => $visite["VIS_Num"],
                                        "VIS_Exerc" => $visite["VIS_Exerc"],
                                        "VIS_Code_M" => $visite["VIS_Code_M"],
                                        "message" => "Erreur dans l'insertion des lignes du visite",
                                        "code" => Enum::Error_Insert_Ligne_Visite
                                    ];
                                    throw new Exception("", Enum::Error_Insert_Ligne_Visite);
                                } else {
                                    $result[] = [
                                        "VIS_Num" => $visite["VIS_Num"],
                                        "VIS_Exerc" => $visite["VIS_Exerc"],
                                        "VIS_Code_M" => $visite["VIS_Code_M"],
                                        "message" => "Updated visite N°" . $visite["VIS_Num"],
                                        "code" => Enum::Success_Code
                                    ];

                                }
                            }
                        }


                    }
                    $connection->commit();

                } catch
                (\Exception $e) {

                    $connection->rollBack();

                }

            }
            return response()->json($result);

        } else {
            return response()->json("Empty Object");
        }
    }


    public function deleteVisite(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $item = $data["object"];
        $deleteligneVisite = 0;
        if (!empty($data)) {
            foreach ($item as &$data) {
                DB::beginTransaction();
                try {
                    $visite = $connection->table('DN_Visite')
                        ->where('VIS_Num', $data["VIS_Num"])
                        ->where('VIS_Code_M', $data["VIS_Code_M"])
                        ->where('VIS_Exerc', $data["VIS_Exerc"])
                        ->first();
                    if ($visite) {
                        $ligneVisite = $connection->table('DN_LigneVisite')
                            ->where('LG_VISNum', $data["VIS_Num"])
                            ->where('LG_VISExerc', $data["VIS_Exerc"])
                            ->get()->count();

                        if ($ligneVisite != 0) {
                            $deleteligneVisite = $connection->table('DN_LigneVisite')
                                ->where('LG_VISExerc', $data["VIS_Exerc"])
                                ->where('LG_VISNum', $data["VIS_Num"])->delete();
                        }
                        if ($ligneVisite != $deleteligneVisite) {
                            $result[] = [
                                "VIS_Num" => $data["VIS_Num"],
                                "VIS_Exerc" => $data["VIS_Exerc"],
                                "VIS_Code_M" => $data["VIS_Code_M"],
                                "message" => "Erreur dans la suppression des lignes du visite N° " . $data["VIS_Num"],
                                "code" => Enum::Error_Delete_Ligne_Visite
                            ];
                            throw new Exception("Erreur dans la suppression des lignes du visite N°");
                        } else {
                            $deleteVisite = $connection->table('DN_Visite')
                                ->where('VIS_Num', $data["VIS_Num"])
                                ->where('VIS_Code_M', $data["VIS_Code_M"])
                                ->where('VIS_Exerc', $data["VIS_Exerc"])->delete();

                            if ($deleteVisite != 1) {
                                $result[] = [
                                    "VIS_Num" => $data["VIS_Num"],
                                    "VIS_Exerc" => $data["VIS_Exerc"],
                                    "VIS_Code_M" => $data["VIS_Code_M"],
                                    "message" => "Erreur pendant la suppression du visite N° " . $data["VIS_Num"],
                                    "code" => Enum::Error_Delete_Visite
                                ];
                                throw new Exception("Erreur pendant la suppression du visite");
                            } else {
                                $result[] = [
                                    "VIS_Num" => $data["VIS_Num"],
                                    "VIS_Exerc" => $data["VIS_Exerc"],
                                    "VIS_Code_M" => $data["VIS_Code_M"],
                                    "message" => "Viste deleted",
                                    "code" => Enum::Success_Code
                                ];

                            }
                        }
                    } else {
                        $result[] = [
                            "VIS_Num" => $data["VIS_Num"],
                            "VIS_Exerc" => $data["VIS_Exerc"],
                            "VIS_Code_M" => $data["VIS_Code_M"],
                            "message" => "Visite N° " . $data["VIS_Num"] . " n'existe pas",
                            "code" => Enum::Success_Code
                        ];
                    }
                    DB::commit();
                } catch
                (\Exception $e) {

                    DB::rollBack();
                    $message = $e->getTrace();
                }
            }
            return response()->json($result);

        } else {
            return response()->json("Empty Object");
        }
    }


}