package com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.cash

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.UiText
import com.simapps.ui_kit.edit_text.EditTextField
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CashPaymentInputView (
    cashValue : String,
    onValueChange: (String) -> Unit,
    cashErrorValue : UiText?,
    onDismissRequest: () -> Unit
)  {
    val sheetState = rememberModalBottomSheetState()
    val scope = rememberCoroutineScope()

    Dialog(
        onDismissRequest = {
            // Dismiss the dialog when the user clicks outside the dialog or on the back
            // button. If you want to disable that functionality, simply use an empty
            // onDismissRequest.
            onDismissRequest()

        },
        properties = DialogProperties(
            usePlatformDefaultWidth = true
        ),
        content = {
            Card(
                elevation = CardDefaults.cardElevation(),
                shape = RoundedCornerShape(15.dp),
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth().padding(12.dp)/*.imePadding()*/,
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Top,

                ) {

                    Text(
                        text = stringResource(R.string.espece),
                        color = MaterialTheme.colorScheme.outline,
                        fontSize = MaterialTheme.typography.titleLarge.fontSize
                    )


                    Spacer(modifier = Modifier.height(16.dp))
                    EditTextField(
                        modifier = Modifier.fillMaxWidth(0.80f),
                        text = cashValue,
                        errorValue = cashErrorValue?.asString(),
                        label = stringResource(R.string.espece),
                        onValueChange = {
                            onValueChange(it)
                        },
                        readOnly = false,
                        enabled = true,
                        showTrailingIcon = true,
                        //   enabled = distNumViewModel.modifyVisite,
                        leadingIcon = Icons.Default.Home,
                        keyboardType = KeyboardType.Decimal,
                        imeAction = ImeAction.Done,
                        onKeyboardActions = {
                            scope.launch {
                                sheetState.hide()
                            }
                            onDismissRequest()
                        },
                    )
                    Spacer(modifier = Modifier.height(15.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceAround,
                    verticalAlignment = Alignment.CenterVertically
                    ) {




                        OutlinedButton(
                            onClick = {
                                onDismissRequest()
                           //     onValueChange("")
                            }
                        ) {
                            Text(text = stringResource(R.string.quitter))
                        }

                        Button(
                            enabled = cashValue.isNotEmpty(),
                            onClick = {
                                onDismissRequest()
                            }
                        ) {
                            Text(text = stringResource(R.string.OK))
                        }
                    }



                }

            }

        }
    )
    }

