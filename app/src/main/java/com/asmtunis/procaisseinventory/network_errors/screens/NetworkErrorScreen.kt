package com.asmtunis.procaisseinventory.network_errors.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.twotone.ArrowBack
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.AuthViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.utils.UpdateLoadingStateUtils
import com.asmtunis.procaisseinventory.core.utils.UpdateLoadingStateUtils.isLoadingInventoryData
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.network_errors.utlis.FetchRemoteData.fetchRemoteData
import com.asmtunis.procaisseinventory.network_errors.view_model.NetworkErrorsViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.ListItem
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel


@Composable
fun NetworkErrorScreen(
    popBackStack: () -> Unit,
    navDrawerViewmodel: NavigationDrawerViewModel,
    networkErrorsVM: NetworkErrorsViewModel,
    mainViewModel: MainViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    authViewModel: AuthViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
) {
    val listState = rememberLazyListState()

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val isLoading = isLoadingInventoryData(getProInventoryDataViewModel = getProInventoryDataViewModel)
            || UpdateLoadingStateUtils.isLoadingProCaisseData(getProCaisseDataViewModel = getProCaisseDataViewModel)
            ||    UpdateLoadingStateUtils.isLoadingSharedData(getSharedDataViewModel = getSharedDataViewModel)
            ||    UpdateLoadingStateUtils.isLoadingCommenSharedData(getSharedDataViewModel = getSharedDataViewModel)

    val networkErrorsList = networkErrorsVM.networkErrorsList

    Scaffold(
        topBar = {
            AppBar(
                showNavIcon = true,
                onNavigationClick = { popBackStack() },
                navIcon = Icons.AutoMirrored.TwoTone.ArrowBack,
                title = stringResource(id = R.string.networkError),
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected
            )
        }
    ) { padding ->

        Column(modifier = Modifier.padding(padding)) {
            val density = LocalDensity.current
            LazyColumn(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(12.dp),
                modifier = Modifier.fillMaxSize(),
                state = listState,
            ) {
                item {
                    AnimatedVisibility(
                        // modifier = modifier,
                        visible = isLoading,
                        enter = slideInVertically { with(density) { 40.dp.roundToPx() } } + fadeIn(),
                        exit = fadeOut(animationSpec = keyframes { this.durationMillis = 120 }),
                    ) {
                        LottieAnim(lotti = R.raw.loading, size = 25.dp)
                    }
                }
                items(
                    //   items= filteredList.keys.toList()
                    count = networkErrorsList.size,
                    key = { networkErrorsList[it].url + networkErrorsList[it].extraInfo },
                ) { index ->
                    val networkError = networkErrorsList[index]
                    ListItem(
                        firstText = networkError.url,
                        secondText = networkError.errorMessage/*?.replace("*", "")*/ ?: "Unknow error !",
                        secondTextMaxLine = 4,
                        thirdText = networkError.extraInfo,
                        dateText = networkError.date.substringBefore("."),
                        moreClickIsVisible = false,
                        onItemClick = {
                            fetchRemoteData(
                                url = networkError.url,
                                extraInfo = networkError.extraInfo,
                                networkErrorsList = networkErrorsList,
                                tickets = mainViewModel.listTicket,
                                listInventaire = mainViewModel.inventaireList,
                                listImmobilisation = mainViewModel.immobilisationList,
                                sessionCaisse = navDrawerViewmodel.sessionCaisse,
                                reglementCaisseList = mainViewModel.reglementCaisseList,
                                clientList = mainViewModel.clientList,
                                exerciceCode = mainViewModel.exerciceList.firstOrNull()?.exerciceCode?: "",
                                utilisateur = mainViewModel.utilisateur,
                                reglementsCaisseList = mainViewModel.reglementCaisseList,
                                baseConfig = selectedBaseconfig,
                                networkErrorsVM = networkErrorsVM,
                                getProCaisseDataViewModel = getProCaisseDataViewModel,
                                getSharedDataViewModel = getSharedDataViewModel,
                                getProInventoryDataViewModel = getProInventoryDataViewModel,
                                authViewModel = authViewModel
                            )
                        }
                    )
                }
            }
        }
    }

}



