package com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.remote.di

import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.remote.api.TypePrixApi
import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.remote.api.TypePrixApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object TypePrixRemoteModule {

    @Provides
    @Singleton
    fun provideTypePrixApi(client: HttpClient): TypePrixApi = TypePrixApiImpl(client)


}