package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.visite.repository


import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.LigneVisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.VisitesDn
import kotlinx.coroutines.flow.Flow


interface VisiteLocalRepository {
    fun loadVisiteAndLignesVisite(visNum: String, exercice: String): Flow<Map<VisitesDn, List<LigneVisitesDn>>>
    fun allCount(): Flow<Int>
    fun getNotSyncedVisite(): Flow<Map<VisitesDn, List<LigneVisitesDn>>>
    fun getNoSyncedToDelete(): Flow<Map<VisitesDn, List<LigneVisitesDn>>>

    fun updateLgVisNum(code: String, codeMobile: String)
    fun updateVisite(code: String, codeMobile: String)
    fun setIsDeletedVisite(code: String, exercice: String)
    fun restDeletedVisite(code: String, exercice: String,status : String, isSync:Boolean)




    fun upsertVisite(value: VisitesDn)
    fun upsertLigneVisite(value: LigneVisitesDn)

    fun upsertVisiteAll(value: List<VisitesDn>)
    fun upsertLigneVisiteAll(value: List<LigneVisitesDn>)

    fun deleteVisiteByVisNum(visCode: String, exercice: String)
    fun deleteVisiteAll()
    fun deleteLigneVisiteAll()
    fun deleteVisite(visitesDn: VisitesDn)
    fun deleteLigneVisite(dnLigneVisite: LigneVisitesDn)
    fun deleteLigneVisiteByVisNum(numVis: String, exercice: String)




    fun getLigneVisiteAll(): Flow<List<LigneVisitesDn>>
    fun getVisiteAll(): Flow<List<VisitesDn>>
    fun getVisiteListByClient(codeClient: String): Flow<Map<VisitesDn, List<LigneVisitesDn>>>




    fun getAllFiltred(isAsc: Int?, sortBy: String?, codeClient: String): Flow<Map<VisitesDn, List<LigneVisitesDn>>>
    fun filterByNomGerant(filterString: String, sortBy: String?, isAsc: Int?, codeClient: String): Flow<Map<VisitesDn, List<LigneVisitesDn>>>
    fun filterByNumVisite(filterString: String, sortBy: String?, isAsc: Int?, codeClient: String): Flow<Map<VisitesDn, List<LigneVisitesDn>>>
    fun filterByProspect(filterString: String, sortBy: String?, isAsc: Int?, codeClient: String): Flow<Map<VisitesDn, List<LigneVisitesDn>>>
}
