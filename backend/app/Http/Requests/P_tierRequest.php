<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Symfony\Component\HttpFoundation\Response;

class P_tierRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'object' => 'required|array',
            'object.*.nomPrenom' => 'required|string|max:255',
            'object.*.adresse' => 'nullable|string|max:255',
            'object.*.tel' => 'nullable|string|max:255',
            'object.*.tel1' => 'nullable|string|max:255',
            'object.*.mail' => 'nullable|email|max:255',
            'object.*.idTypeTier' => 'nullable|string|max:255',
            'object.*.idDevise' => 'nullable|string|max:255',
            'object.*.Active' => 'nullable|boolean',
            'object.*.RegCommerce' => 'nullable|string|max:255',
            'object.*.fb_Url' => 'nullable|url|max:255',
            'object.*.idGrp1' => 'nullable|string|max:255',
            'object.*.idGrp2' => 'nullable|string|max:255',
            'object.*.idGrp3' => 'nullable|string|max:255',
            'object.*.idPays' => 'nullable|string|max:255',
            'object.*.idRegime' => 'nullable|string|max:255',
            'object.*.idSecteur' => 'nullable|string|max:255',
            'object.*.idTypeVente' => 'nullable|string|max:255',
            'object.*.idVille' => 'nullable|string|max:255',
            'object.*.id_EtatTier' => 'nullable|string|max:255',
            'object.*.isLocal' => 'nullable|boolean',
            'object.*.j_codeUser' => 'nullable|string|max:255',
            'object.*.matriculeFiscal' => 'nullable|string|max:255',
            'object.*.notation_s10' => 'nullable|string|max:255',
            'object.*.note' => 'nullable|string|max:255',
            'object.*.numArticle' => 'nullable|string|max:255',
            'object.*.responsableContact' => 'nullable|string|max:255',
            'object.*.telResponsableContact' => 'nullable|string|max:255',
            'object.*.idPoprietaire' => 'nullable|string|max:255',
        ];

    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY)
        );
    }
}
