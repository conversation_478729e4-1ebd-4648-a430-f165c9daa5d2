package com.asmtunis.procaisseinventory.shared_ui_components

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.sp

fun extractHtml(input: String): String? {
    // Use a regex in dot-all mode to capture everything from <!DOCTYPE html> to </html>
    val regex = Regex("(?s)(<!DOCTYPE html>.*</html>)")
    return regex.find(input)?.value
}
fun extractErrorDetails(html: String): Pair<String, String> {
    // Extract error code from div with class "code"
    val code = Regex("""<div class="code"[^>]*>(.*?)</div>""", RegexOption.DOT_MATCHES_ALL)
        .find(html)
        ?.groupValues
        ?.get(1)
        ?.trim()
        ?: ""

    // Extract error message from div with class "message"
    val message = Regex("""<div class="message"[^>]*>(.*?)</div>""", RegexOption.DOT_MATCHES_ALL)
        .find(html)
        ?.groupValues
        ?.get(1)
        ?.trim()
        ?: ""

    return code to message
}
@Composable
fun HtmlView(html: String) {
    val (errorCode, errorMessage) = extractErrorDetails(html)

   val isHtml = errorCode.isNotEmpty() && errorMessage.isNotEmpty()
    Text(
        modifier = Modifier.fillMaxWidth(),
        text = if (isHtml) "$errorCode: $errorMessage" else html,
        fontSize = 16.sp,
        fontWeight = FontWeight.Normal,
        color = if (isHtml) MaterialTheme.colorScheme.error else Color.Unspecified,
        textAlign = if (isHtml) TextAlign.Center else TextAlign.Start
        //  maxLines = secondTextMaxLine
    )

//    Box(
//        modifier = Modifier.fillMaxSize(),
//        contentAlignment = Alignment.Center
//    ) {
//        AndroidView(
//            modifier = Modifier
//                .fillMaxWidth() // Adjust sizing as needed
//                .height(150.dp),
//            factory = { context ->
//                WebView(context).apply {
//                     settings.apply {
//                         // Basic settings (customize as needed)
//                         javaScriptEnabled = true // Enable JavaScript if required
//                         domStorageEnabled = true // For HTML5 local storage
//                         loadWithOverviewMode = true
//                         useWideViewPort = true
//                     }
//                }
//            },
//            update = { webView ->
//                // Load HTML content
//                webView.loadDataWithBaseURL(
//                    null, // Base URL (null for local data)
//                    extractHtml(html)?.trimIndent()?: "",
//                    "text/html",
//                    "UTF-8",
//                    null // History URL (null for no history)
//                )
//            }
//        )
//    }


//    var webViewClient = remember {
//        object : WebViewClient() {
//            // Optional: You can override methods here for more control
//            // For example, to handle link clicks:
//
//            override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
//                // Handle the URL, e.g., open in a browser or navigate within the app
//                // Return true to indicate that you've handled the URL
//                // Return false to let the WebView handle the URL
//                return true // Example: Handle all links
//            }
//
//        }
//    }
//    AndroidView(
//        modifier = Modifier.fillMaxSize(),
//        factory = { context ->
//            WebView(context).apply {
//                settings.javaScriptEnabled = true // Optional: Enable JavaScript if needed
//                webViewClient = webViewClient // Set the custom WebViewClient
//                loadData(html, "text/html", "UTF-8")
//            }
//        },
//        update = { webView ->
//            // Optionally update the WebView if 'html' changes.  Crucially important.
//            // Without this, if the html variable changes, the WebView won't update.
//            webView.loadData(extractHtml(html)?.trimIndent()?: "", "text/html", "UTF-8")
//
//        }
//    )

}