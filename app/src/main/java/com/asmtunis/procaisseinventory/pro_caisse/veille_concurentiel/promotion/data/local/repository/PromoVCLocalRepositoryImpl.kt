package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.local.repository


import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.local.dao.PromoVCDAO
import kotlinx.coroutines.flow.Flow



class PromoVCLocalRepositoryImpl(
    private val promoVCDAO: PromoVCDAO
) : PromoVCLocalRepository {


    override fun upsertAll(value: List<PromoVC>) = promoVCDAO.insertAll(value)
    override fun upsert(value: PromoVC) = promoVCDAO.insert(value)
    override fun noSyncedToAddOrUpdate(): Flow<List<PromoVC>> = promoVCDAO.noSyncedToAddOrUpdate
    override fun noSyncedToDelete(): Flow<List<PromoVC>>  = promoVCDAO.noSyncedToDelete

    override fun updateCloudCode(code: String, codeMobile: String) =
        promoVCDAO.updateCloudCode(code = code, codeMobile= codeMobile)

    override fun restDeleted(code: String,status : String, isSync:Boolean) =
        promoVCDAO.restDeleted(code,status, isSync)

    override fun setDeleted(code: String, codeMobile: String) =
        promoVCDAO.setDeleted(code, codeMobile)


    override fun deleteAll() = promoVCDAO.deleteAll()
    override fun deleteByCode(codeAutre: String)  = promoVCDAO.deleteByCode(codeAutre)

    override fun getAll(): Flow<List<PromoVC>> = promoVCDAO.all
    override fun filterByNum(
        searchString: String,
        filterByTypComm: String,
        sortBy: String,
        isAsc: Int
    ): Flow<List<PromoVCWithImages>> = promoVCDAO.filterByNum(
        searchString = searchString,
        filterByTypComm = filterByTypComm,
        sortBy = sortBy,
        isAsc = isAsc
    )

    override fun filterByArtConcurrent(
        searchString: String,
        filterByTypComm: String,
        sortBy: String?,
        isAsc: Int?
    ): Flow<List<PromoVCWithImages>> = promoVCDAO.filterByArtConcurrent(
    searchString = searchString,
    filterByTypComm = filterByTypComm,
    sortBy = sortBy,
    isAsc = isAsc
    )

    override fun filterByCodeArtLocal(
        searchString: String,
        sortBy: String,
        filterByTypComm: String,
        isAsc: Int
    ): Flow<List<PromoVCWithImages>> = promoVCDAO.filterByCodeArtLocal(
        searchString = searchString,
        filterByTypComm = filterByTypComm,
        sortBy = sortBy,
        isAsc = isAsc
     )

    override fun getAllFiltred(
        isAsc: Int,
        filterByTypComm: String,
        sortBy: String
    ): Flow<List<PromoVCWithImages>> = promoVCDAO.getAllFiltred(
        filterByTypComm = filterByTypComm,
        sortBy = sortBy,
        isAsc = isAsc)
}