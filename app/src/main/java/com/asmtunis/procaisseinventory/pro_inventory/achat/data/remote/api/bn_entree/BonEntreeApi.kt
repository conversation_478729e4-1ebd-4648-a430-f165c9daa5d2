package com.asmtunis.procaisseinventory.pro_inventory.achat.data.remote.api.bn_entree

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.BonEntree
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.BonEntreeResponse
import kotlinx.coroutines.flow.Flow


interface BonEntreeApi {

        suspend fun getBonEntrees(baseConfig: String, mois: String): Flow<DataResult<List<BonEntree>>>
        suspend fun addBatchBonEntreesWithLines(baseConfig: String): Flow<DataResult<List<BonEntreeResponse>>>
}