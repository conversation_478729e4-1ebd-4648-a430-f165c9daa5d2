package com.asmtunis.honeywellbarcodereader

import com.asmtunis.honeywellbarcodereader.listener.IBareCodeReader

class BarCodeReaderManager {
    private var bareCodeReaders: MutableList<IBareCodeReader> = ArrayList()

    fun addReader(iBareCodeReader: IBareCodeReader): BarCodeReaderManager {
        bareCodeReaders.add(iBareCodeReader)
        return this
    }

    fun removeReader(iBareCodeReader: IBareCodeReader) {
        bareCodeReaders.remove(iBareCodeReader)
    }

    fun startListener() {
        for (bareCodeReader in bareCodeReaders) bareCodeReader.startListening()
    }

    fun resume() {
        for (bareCodeReader in bareCodeReaders) bareCodeReader.resume()
    }

    fun destroy() {
        for (bareCodeReader in bareCodeReaders) bareCodeReader.destroy()
    }
}