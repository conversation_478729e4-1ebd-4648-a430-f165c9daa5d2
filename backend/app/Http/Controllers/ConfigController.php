<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ConfigController extends Controller
{

    public function checkDB(Request $request)
    {
        try {
            DB::connection()->getPdo();
            if (DB::connection()->getDatabaseName()) {
                echo "<h1 style='color: green'>Yes! Successfully connected to the DB: " . DB::connection()->getDatabaseName() . "</h1>";
            } else {
                die("<h1 style='color: orange'>Could not find the database. Please check your configuration.</h1>");
            }
        } catch (\Exception $e) {
            die("<h1 style='color: red'>Could not open connection to database server.  Please check your configuration.</h1>");
        }
    }

}
