package com.asmtunis.procaisseinventory.nav_components.nav_drawer.ui

import android.graphics.Bitmap
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ExitToApp
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Sync
import androidx.compose.material.icons.filled.SyncDisabled
import androidx.compose.material3.Badge
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import coil3.compose.SubcomposeAsyncImage
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.InventoryGraph
import com.asmtunis.procaisseinventory.core.navigation.ProcaisseGraph
import com.asmtunis.procaisseinventory.core.navigation.SettingRoute
import com.asmtunis.procaisseinventory.core.utils.Sync.getProCaisseTotalNoSyncCount
import com.asmtunis.procaisseinventory.core.utils.Sync.getProInventoryTotalNoSyncCount
import com.asmtunis.procaisseinventory.core.utils.Sync.getSharedTotalNoSyncCount
import com.asmtunis.procaisseinventory.core.utils.Sync.isSyncInProCaisse
import com.asmtunis.procaisseinventory.core.utils.Sync.isSyncInProInventory
import com.asmtunis.procaisseinventory.core.utils.Sync.syncAllProCaisse
import com.asmtunis.procaisseinventory.core.utils.Sync.syncAllProInventory
import com.asmtunis.procaisseinventory.core.utils.UpdateLoadingStateUtils
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import com.dokar.sonner.ToasterState
import com.simapps.ui_kit.dialogues.CustomAlertDialogue
import com.simapps.ui_kit.utils.remainingDays

@Composable
fun DrawerHeader(
    toaster: ToasterState,
    navigateTo: (route: Any) -> Unit,
    dataViewModel: DataViewModel,
    exerciceCode: String,
    utilisateur: Utilisateur,
    stationList: List<Station>,
    logo: Bitmap?,
    navDrawerViewModel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncInventoryViewModel: SyncInventoryViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    isConnected: Boolean,

    ) {
    val context = LocalContext.current

    val switchDrawer = navDrawerViewModel.isProInventory
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val licenceProCaisse = selectedBaseconfig.licences.firstOrNull { it.produit == Globals.PRO_CAISSE_MOBILITY }
    val licenceProInventory = selectedBaseconfig.licences.firstOrNull { it.produit == Globals.PRO_INVENTORY }

    val station = stationList.firstOrNull { it.sTATCode == utilisateur.Station }

    val sessionCaisse = navDrawerViewModel.sessionCaisse


    val noSyncCountMobility = getProCaisseTotalNoSyncCount(
        syncProcaisseViewModels = syncProcaisseViewModels,
    )

    val noSyncCountInvetory =  getProInventoryTotalNoSyncCount(
        syncInventoryViewModel = syncInventoryViewModel,
    )
    val noSyncCountShared = getSharedTotalNoSyncCount(syncSharedViewModels = syncSharedViewModels)


    val proCaisseAuthorization = getProCaisseDataViewModel.authorizationList

    val haveClotureSessionAutoAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.CLOT_SESSION_AUTO}
    
    val haveAutoFactureAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.AUTO_FACTURE}

    val nbrJoursRestant = if (switchDrawer) remainingDays(
        date = licenceProInventory?.datef,
        defaultRemainingDays =  licenceProInventory?.dater?.toInt()?: 0
    )

    else remainingDays(
        date = licenceProCaisse?.datef,
        defaultRemainingDays = licenceProCaisse?.dater?.toInt()?: 0)


    val isLoadingProCaisseData = UpdateLoadingStateUtils.isLoadingProCaisseData(getProCaisseDataViewModel = getProCaisseDataViewModel)
    val isLoadingSharedData = UpdateLoadingStateUtils.isLoadingSharedData(getSharedDataViewModel = getSharedDataViewModel)
    val isLoadingCommenSharedData = UpdateLoadingStateUtils.isLoadingCommenSharedData(getSharedDataViewModel = getSharedDataViewModel)
    val isLoadingInventoryData = UpdateLoadingStateUtils.isLoadingInventoryData(getProInventoryDataViewModel = getProInventoryDataViewModel)


    CustomAlertDialogue(
        canDismiss = true,
        confirmText = stringResource(id = R.string.sync_title),
        cancelText = stringResource(id = R.string.Deconnecter),
        title = stringResource(id = R.string.sync_first),
        msg = stringResource(id = R.string.sync_before_q),
        openDialog = navDrawerViewModel.showNotSyncDialogue,
        setDialogueVisibility = {
            navDrawerViewModel.onShowNotSyncDialogueChange(it)
        },
        customAction = {
            if(noSyncCountMobility + noSyncCountShared==0) return@CustomAlertDialogue

            syncAllProCaisse(
                syncProcaisseViewModels = syncProcaisseViewModels,
            )

            if(noSyncCountInvetory + noSyncCountShared!=0)
                syncAllProInventory(
                    syncSharedViewModels = syncSharedViewModels,
                    syncInventoryViewModel = syncInventoryViewModel
                )
        },
        negatifAction = {
            navDrawerViewModel.onLogoutBtnClick(
                selectedBaseconfig= selectedBaseconfig,
                resetDataStore = {
                    dataViewModel.resetDataStore()
                },
                navigate = {
                    navigateTo(it)
                }
              )
        }
    )


        Row(
            horizontalArrangement = Arrangement.Start,
            verticalAlignment = Alignment.CenterVertically,
             modifier = Modifier
                 .padding(end = 9.dp, start = 9.dp)
                 .clip(RoundedCornerShape(9.dp))
                 .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.4f))
                 .fillMaxWidth()
        ) {

            SubcomposeAsyncImage(
                    model = logo,
                    contentDescription = "logo",
                    modifier = Modifier
                        // .clip(RoundedCornerShape(10.dp))
                        .padding(start = 3.dp, bottom = 3.dp, top = 3.dp)
                        .clip(CircleShape)
                        .size(80.dp)
                        .border(2.dp, Color.Black, CircleShape),
                    loading = { LottieAnim(lotti = R.raw.loading, size = 25.dp) },
                    error =  {
                        Image(
                            //modifier = modifier.width(34.dp),
                            painter =  painterResource(id = R.drawable.ic_asm),
                            contentDescription = null
                        )

                       }

                )



            Spacer(modifier = Modifier.width(9.dp))

            Column {
                Spacer(modifier = Modifier.height(9.dp))
                Text(
                    text = utilisateur.Nom + " " + utilisateur.Prenom,
                     fontWeight = MaterialTheme.typography.titleMedium.fontWeight,
                    fontSize = MaterialTheme.typography.titleMedium.fontSize,
                   // color = MaterialTheme.colorScheme.outline
                )

                Spacer(modifier = Modifier.height(6.dp))
                Text(text =if(station!=null) station.sTATDesg + " (${utilisateur.Station})"  else stringResource(R.string.station_value, utilisateur.Station))
                Spacer(modifier = Modifier.height(6.dp))

                if(sessionCaisse.sCIdSCaisse!="" && haveClotureSessionAutoAuthorisation)
                    Text(
                        maxLines = 2,
                        text = sessionCaisse.sCIdSCaisse + " (" + exerciceCode + ")"
                    )

                if(nbrJoursRestant< 30) {
                    Text(
                        text = stringResource(id = R.string.jours_restant, nbrJoursRestant.toString()),
                        color =   MaterialTheme.colorScheme.error,
                       style = MaterialTheme.typography.titleMedium
                    )
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement =  Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Spacer(modifier = Modifier.width(12.dp))

                    if(dataViewModel.getProcaisseActivationState() &&
                        dataViewModel.getInventoryActivationState() &&
                        dataViewModel.getIsProInventoryLicenseSelected() &&
                        dataViewModel.getIsProcaisseLicenseSelected() &&
                        selectedBaseconfig != BaseConfig()
                    ){
                        Switch(
                            checked = switchDrawer,
                            thumbContent =  {
                                Image(
                                    painter = painterResource(
                                        id = if(switchDrawer)
                                            R.drawable.inventory_logo
                                        else
                                            R.drawable.procaisse_logo),
                                    contentDescription ="Logo",
                                    modifier = Modifier
                                    // .clip(RoundedCornerShape(10.dp))
                                    //    .padding(start = 3.dp, bottom = 3.dp, top = 3.dp)
                                    // .clip(CircleShape)
                                    //    .size(80.dp)
                                    // .border(2.dp, MaterialTheme.colorScheme.outline, CircleShape)

                                )
                            },
                            onCheckedChange = {
                                navDrawerViewModel.onSwitchDrawerChange(it)
                                val destination = if(it) InventoryGraph else ProcaisseGraph
                                navigateTo(destination)

                            }
                        )
                    }
                    else if(dataViewModel.getProcaisseActivationState() &&
                        dataViewModel.getIsProcaisseLicenseSelected() &&
                       selectedBaseconfig != BaseConfig() )
                        navDrawerViewModel.onSwitchDrawerChange(false)
                    else if(dataViewModel.getInventoryActivationState() &&
                        dataViewModel.getIsProInventoryLicenseSelected() &&
                        selectedBaseconfig != BaseConfig())
                        navDrawerViewModel.onSwitchDrawerChange(true)

                    Spacer(modifier = Modifier.width(12.dp))

                    if(noSyncCountInvetory+ noSyncCountMobility + noSyncCountShared>0)
                        BadgedBox(badge = { Badge { Text((noSyncCountInvetory+ noSyncCountMobility + noSyncCountShared).toString()) } }) {


                           if(isSyncInProCaisse(
                                   syncProcaisseViewModels = syncProcaisseViewModels,
                                   syncSharedViewModels = syncSharedViewModels
                               )
                               ||
                               isSyncInProInventory(
                                   syncSharedViewModels = syncSharedViewModels,
                                   syncInventoryViewModel = syncInventoryViewModel,
                               )
                           )
                               LottieAnim(lotti = R.raw.loading, size = 25.dp)
                        else
                            IconButton(
                                enabled = isConnected,
                                onClick = {
                                    if (noSyncCountMobility + noSyncCountShared > 0) {
                                        syncAllProCaisse(
                                            syncProcaisseViewModels = syncProcaisseViewModels,
                                        )
                                    }

                                    if (noSyncCountInvetory + noSyncCountShared > 0) {
                                        syncAllProInventory(
                                            syncSharedViewModels = syncSharedViewModels,
                                            syncInventoryViewModel = syncInventoryViewModel,
                                        )
                                    }
                                }
                            ) {
                                Icon(
                                   // modifier = Modifier.size(AssistChipDefaults.IconSize),
                                    imageVector = if(isConnected) Icons.Default.Sync else Icons.Default.SyncDisabled,
                                    contentDescription = stringResource(id = R.string.sync_title),

                                    )
                            }


                    }






                        if (isLoadingProCaisseData || isLoadingSharedData || isLoadingCommenSharedData || isLoadingInventoryData) {
                            LottieAnim(lotti = R.raw.loading, size = 25.dp)
                        }
                    Spacer(modifier = Modifier.width(12.dp))
                    IconButton(onClick = {
                        navigateTo(SettingRoute)
                    }) {
                        Icon(
                            imageVector = Icons.Default.Settings,
                            contentDescription = stringResource(id = R.string.cd_favorite_button)
                        )
                    }

                    Spacer(modifier = Modifier.width(12.dp))




                    IconButton(
                        enabled = isConnected,
                        onClick = {
                        if(noSyncCountMobility + noSyncCountInvetory + noSyncCountShared!=0){
                            navDrawerViewModel.onShowNotSyncDialogueChange(true)

                            return@IconButton
                        }
                            navDrawerViewModel.onLogoutBtnClick(
                                selectedBaseconfig= selectedBaseconfig,

                                resetDataStore = {
                                    dataViewModel.resetDataStore()
                                },
                                navigate = {
                                    navigateTo(it)
                                }
                            )
                    }) {
                       /* if(authViewModel.setTokenState.loading) {
                            LottieAnim(lotti = R.raw.loading, size = 25.dp)
                        }
                        else */Icon(
                            imageVector = Icons.AutoMirrored.Filled.ExitToApp,
                            contentDescription = stringResource(id = R.string.cd_favorite_button)
                        )

                    }
                }

            }
        }


}
