package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.text_validation

import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ConcurrentVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.TypeCommunicationVC


data class VcFormState(

    val produit: String = "",
    val produitError: UiText? = null,

    val autreNote: String = "",
    val autreNoteError: UiText? = null,


    val prix: String =  "",
    val prixError: UiText? = null,

    val taux: String = "",
    val tauxError: UiText? = null,

    val note: String = "",
    val noteError: UiText? = null,


    val concurrent: ConcurrentVC = ConcurrentVC(),
    val concurrentError: UiText? = null,

    val typeCommunication: TypeCommunicationVC = TypeCommunicationVC(),
    val typeCommunicationError: UiText? = null,

    val localProduct: Article = Article(),
    val localProductError: UiText? = null,
    )