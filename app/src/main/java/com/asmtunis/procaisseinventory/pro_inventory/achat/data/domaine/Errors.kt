package com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Errors(
    @SerialName("code")
    val code: Int,
    @SerialName("message")
    val message: String,
    @SerialName("ORDER_LIG")
    val oRDERLIG: Int,
    @SerialName("amount_calculated")
    val amountCalculated: Double

)