package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.ligne_bon_retour.repository

import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetour

interface LigneBonRetourLocalRepository {

    fun upsertAll(value: List<LigneBonRetour>)
    fun upsert(value: LigneBonRetour)
    fun setSynced(newNum: String, oldNum: String)

    fun deleteByCodeM(
        code: String,
        exercice: String,
    )
    fun deleteAll()
}