package com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.local.dao.TicketRayonDAO
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.local.repository.TicketRayonLocalRepository
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.local.repository.TicketRayonLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton




    @Module
    @InstallIn(SingletonComponent::class)
    class TicketRayonLocalModule {

        @Provides
        @Singleton
        fun provideTicketRayonDao(
            proInventoryDataBase: ProCaisseDataBase
        ) = proInventoryDataBase.ticketRayonDAO()

        @Provides
        @Singleton
        @Named("TicketRayon")
        fun provideTicketRayonRepository(
            ticketRayonDAO: TicketRayonDAO
        ): TicketRayonLocalRepository = TicketRayonLocalRepositoryImpl(
            ticketRayonDAO = ticketRayonDAO
        )

    }
