package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.PRIX_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine.PrixVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine.PrixVCWithImages
import kotlinx.coroutines.flow.Flow


@Dao
interface PrixVCDAO {
    @get:Query("SELECT * FROM $PRIX_TABLE where Status !='DELETED' order by strftime('%Y-%m-%d %H-%M-%S',DateOp) desc")
    val all: Flow<List<PrixVC>>

    @Query("SELECT * FROM $PRIX_TABLE WHERE CodeVCPrixM = :code")
    fun getByCodeM(code: String?): Flow<PrixVC>

    @Query("SELECT * FROM $PRIX_TABLE WHERE CodeVCPrix = :code")
    fun getByCode(code: String?): Flow<PrixVC>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(vcPrixList: List<PrixVC>)


    @Query("UPDATE $PRIX_TABLE SET CodeVCPrix = :code, Status = 'SELECTED' , IsSync = 1 where CodeVCPrixM = :codeM")
    fun updateCloudCode(code: String, codeM: String)

    @Query("UPDATE $PRIX_TABLE SET Status = 'DELETED' , IsSync = 0 where CodeVCPrixM = :codeMobile  or CodeVCPrix=:code")
    fun setDeleted(code: String, codeMobile: String)

    @Query("UPDATE $PRIX_TABLE SET Status = :status , IsSync = :isSync where CodeVCPrixM = :code  or CodeVCPrix=:code")
    fun restDeleted(code: String,status : String, isSync:Boolean)


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(vcPrixe: PrixVC)

    @get:Query("SELECT * FROM $PRIX_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ")
    val noSyncedToAddOrUpdate: Flow<List<PrixVC>>

    @get:Query("SELECT * FROM $PRIX_TABLE where isSync=0 and Status='DELETED' ")
    val noSyncedToDelete: Flow<List<PrixVC>?>

    @get:Query("SELECT count(*) FROM $PRIX_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val countNonSync: Int

    @get:Query("SELECT count(*) FROM $PRIX_TABLE where isSync=0 and Status='DELETED' ")
    val countNoSyncedToDelete: Int

    @get:Query("SELECT count(*) FROM $PRIX_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val noSyncCountMubtale: Flow<Int>

    @get:Query("SELECT count(*) FROM $PRIX_TABLE where isSync=0 and Status='DELETED' ")
    val countNoSyncedToDeleteMubtale: Flow<Int>

    @Query("delete from $PRIX_TABLE")
    fun deleteAll()

    @Query("DELETE FROM $PRIX_TABLE where CodeVCPrix=:codeAutre")
    fun deleteByCode(codeAutre: String)

    @Query("DELETE FROM $PRIX_TABLE where CodeVCPrix=:codeAutre or CodeVCPrixM = :codeMobile")
    fun deleteByIdAndCodeM(codeAutre: String?, codeMobile: String?)








    @Transaction
    @Query(
        "SELECT * FROM $PRIX_TABLE " +
                "WHERE CodeVCPrix LIKE '%' || :searchString || '%' " +
                "and  CASE WHEN :filterByTypComm !=  '' THEN CodeTypeCom =:filterByTypComm ELSE CodeTypeCom !=:filterByTypComm END " +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'CodeVCPromo'  AND :isAsc = 1 THEN CodeVCPrix END ASC, " +
                "CASE WHEN :sortBy = 'CodeVCPromo'  AND :isAsc = 2 THEN CodeVCPrix END DESC, " +

                "CASE WHEN :sortBy = 'PrixConcur'  AND :isAsc = 1 THEN PrixConcur END ASC, " +
                "CASE WHEN :sortBy = 'PrixConcur'  AND :isAsc = 2 THEN PrixConcur END DESC, " +
                "CASE WHEN :sortBy = 'DateOp'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END ASC, " +
                "CASE WHEN :sortBy = 'DateOp'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END DESC "
    )
    fun filterByNum(searchString: String, filterByTypComm : String, sortBy: String, isAsc: Int): Flow<List<PrixVCWithImages>>


    @Transaction
    @Query(
        "SELECT * FROM $PRIX_TABLE " +
                "WHERE ArticleConcur LIKE '%' || :searchString || '%' " +
                "and CASE WHEN :filterByTypComm !=  '' THEN CodeTypeCom =:filterByTypComm ELSE CodeTypeCom !=:filterByTypComm END " +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'CodeVCPromo'  AND :isAsc = 1 THEN CodeVCPrix END ASC, " +
                "CASE WHEN :sortBy = 'CodeVCPromo'  AND :isAsc = 2 THEN CodeVCPrix END DESC, " +

                "CASE WHEN :sortBy = 'PrixConcur'  AND :isAsc = 1 THEN PrixConcur END ASC, " +
                "CASE WHEN :sortBy = 'PrixConcur'  AND :isAsc = 2 THEN PrixConcur END DESC, " +
                "CASE WHEN :sortBy = 'DateOp'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END ASC, " +
                "CASE WHEN :sortBy = 'DateOp'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END DESC "
    )
    fun filterByArtConcurrent(searchString: String, filterByTypComm : String, sortBy: String?, isAsc: Int?): Flow<List<PrixVCWithImages>>

    @Transaction
    @Query(
        "SELECT * FROM $PRIX_TABLE " +
                "WHERE CodeArtLocal LIKE '%' ||  :searchString || '%' " +
                "and  CASE WHEN :filterByTypComm !=  '' THEN CodeTypeCom =:filterByTypComm ELSE CodeTypeCom !=:filterByTypComm END " +




                " ORDER BY " +
                "CASE WHEN :sortBy = 'CodeVCPromo'  AND :isAsc = 1 THEN CodeVCPrix END ASC, " +
                "CASE WHEN :sortBy = 'CodeVCPromo'  AND :isAsc = 2 THEN CodeVCPrix END DESC, " +

                "CASE WHEN :sortBy = 'PrixConcur'  AND :isAsc = 1 THEN PrixConcur END ASC, " +
                "CASE WHEN :sortBy = 'PrixConcur'  AND :isAsc = 2 THEN PrixConcur END DESC, " +
                "CASE WHEN :sortBy = 'DateOp'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END ASC, " +
                "CASE WHEN :sortBy = 'DateOp'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END DESC "
    )
    fun filterByCodeArtLocal(searchString: String, sortBy: String, filterByTypComm : String, isAsc: Int): Flow<List<PrixVCWithImages>>

    @Transaction
    @Query(
        "SELECT * FROM $PRIX_TABLE " +
                "WHERE  CASE WHEN :filterByTypComm !=  '' THEN CodeTypeCom =:filterByTypComm ELSE CodeTypeCom !=:filterByTypComm END   " +


                " ORDER BY " +
                "CASE WHEN :sortBy = 'CodeVCPromo'  AND :isAsc = 1 THEN CodeVCPrix END ASC, " +
                "CASE WHEN :sortBy = 'CodeVCPromo'  AND :isAsc = 2 THEN CodeVCPrix END DESC, " +
                "CASE WHEN :sortBy = 'PrixConcur'  AND :isAsc = 1 THEN PrixConcur END ASC, " +
                "CASE WHEN :sortBy = 'PrixConcur'  AND :isAsc = 2 THEN PrixConcur END DESC, " +
                "CASE WHEN :sortBy = 'DateOp'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END ASC, " +
                "CASE WHEN :sortBy = 'DateOp'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END DESC "
    )
    fun getAllFiltred(isAsc: Int, filterByTypComm : String, sortBy: String): Flow<List<PrixVCWithImages>>

}
