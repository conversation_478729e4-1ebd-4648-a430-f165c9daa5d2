package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product

import android.annotation.SuppressLint
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.filter.NewProductListState
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.text_validation.ValidationAddNewVcEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class NewProductViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb
) : ViewModel() {


    var selectedNewProduct: NewProductVCWithImages by mutableStateOf(NewProductVCWithImages())
        private set
    fun onselectedNewProdChange(value: NewProductVCWithImages) {
        selectedNewProduct = value
    }


    var modify: Boolean by mutableStateOf(false)
        private set
    fun onModifyChange(value: Boolean) {
        modify = value
    }


    var newProductSearchTextState: TextFieldValue by mutableStateOf(TextFieldValue(""))
        private set
    fun onNewProductSearchValueChange(value: TextFieldValue) {
        newProductSearchTextState = value
    }


    var newProductFilterListstate: NewProductListState by mutableStateOf(NewProductListState())
        private set
    fun onEventNewProductVC(event: ListEvent) {
        when (event) {
            is ListEvent.Order -> {
                if (newProductFilterListstate.listOrder::class == event.listOrder::class &&
                    newProductFilterListstate.listOrder.orderType == event.listOrder.orderType
                ) {
                    return
                }
                newProductFilterListstate = newProductFilterListstate.copy(
                    listOrder = event.listOrder
                )
                filterNewProductVC(newProductFilterListstate)
            }
            is ListEvent.Delete -> TODO()
            is ListEvent.Restore -> TODO()



            is ListEvent.ListSearch -> {
                newProductFilterListstate = newProductFilterListstate.copy(
                    search = event.listSearch
                )

                filterNewProductVC(newProductFilterListstate)
            }

            is ListEvent.FirstCustomFilter -> {
                newProductFilterListstate = newProductFilterListstate.copy(
                    filterByConcurent = event.firstFilter
                )

                filterNewProductVC(newProductFilterListstate)
            }

            is ListEvent.SecondCustomFilter -> {
                newProductFilterListstate = newProductFilterListstate.copy(
                    filterByTypeCommunication = event.secondFiter
                )

                filterNewProductVC(newProductFilterListstate)
            }
            is ListEvent.ThirdCustomFilter -> TODO()
        }

    }
   // var getNewProductJob: Job = Job()
    fun filterNewProductVC(newProductFilterListState: NewProductListState) {



        val searchedText = newProductSearchTextState.text
        val searchValue = newProductFilterListState.search
        val filterByConcurent = newProductFilterListState.filterByConcurent
        val filterByTypeComm = newProductFilterListState.filterByTypeCommunication

    //    getNewProductJob.cancel()

        if (searchedText.isEmpty()) {
           /* getNewProductJob =*/ when (newProductFilterListState.listOrder.orderType) {
                is OrderType.Ascending -> {
                    when (newProductFilterListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proCaisseLocalDb.newProductVC.getAllFiltred(
                                isAsc = 1,
                                sortBy = "CodeVCLanP",
                                filterByConcurrent = filterByConcurent,
                                filterByTypComm = filterByTypeComm
                            ).collect {

                                setNewProdList(it)

                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proCaisseLocalDb.newProductVC.getAllFiltred(
                                isAsc = 1,
                                sortBy = "dateOp",
                                filterByConcurrent = filterByConcurent,
                                filterByTypComm = filterByTypeComm).collect {

                                setNewProdList(it)
                                }
                        }

                        is ListOrder.Third -> viewModelScope.launch {
                            proCaisseLocalDb.newProductVC.getAllFiltred(
                                isAsc = 1,
                                sortBy = "ProduitLanP",
                                filterByConcurrent = filterByConcurent,
                                filterByTypComm = filterByTypeComm
                            ).collect {
                                setNewProdList(it)

                            }
                        }
                    }
                }

                is OrderType.Descending -> {
                    when (newProductFilterListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proCaisseLocalDb.newProductVC.getAllFiltred(
                                isAsc = 2,
                                sortBy = "CodeVCLanP",
                                filterByConcurrent = filterByConcurent,
                                filterByTypComm = filterByTypeComm).collect {
                                setNewProdList(it)

                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {

                            proCaisseLocalDb.newProductVC.getAllFiltred(
                                isAsc = 2,
                                sortBy = "dateOp",
                                filterByConcurrent = filterByConcurent,
                                filterByTypComm = filterByTypeComm
                            ).collect {
                                setNewProdList(it)
                            }
                        }

                        is ListOrder.Third -> viewModelScope.launch {
                            proCaisseLocalDb.newProductVC.getAllFiltred(
                                isAsc = 2,
                                sortBy = "ProduitLanP",
                                filterByConcurrent = filterByConcurent,
                                filterByTypComm = filterByTypeComm
                            ).collect {
                                setNewProdList(it)
                            }
                        }
                    }
                }
            }
        } else {
            if (searchedText.isNotEmpty()) {
                if (searchValue is ListSearch.FirstSearch) {
                     /*getNewProductJob = */when (newProductFilterListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (newProductFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.newProductVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "CodeVCLanP",
                                        isAsc = 1,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setNewProdList(it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.newProductVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "dateOp",
                                        isAsc = 1,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setNewProdList(it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.newProductVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "ProduitLanP",
                                        isAsc = 1,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setNewProdList(it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (newProductFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.newProductVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "CodeVCLanP",
                                        isAsc = 2,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setNewProdList(it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.newProductVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "dateOp",
                                        isAsc = 2,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setNewProdList(it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.newProductVC.filterByNum(
                                        searchString = searchedText,
                                        sortBy = "ProduitLanP",
                                        isAsc = 2,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setNewProdList(it)
                                    }
                                }
                            }
                        }
                    }
                }

                if (searchValue is ListSearch.SecondSearch) {
                    /*getNewProductJob = */ when (newProductFilterListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (newProductFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.newProductVC.filterByNewProduct(
                                        searchString = searchedText,
                                        sortBy = "CodeVCLanP",
                                        isAsc = 1,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setNewProdList(it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.newProductVC.filterByNewProduct(
                                        searchString = searchedText,
                                        sortBy = "dateOp",
                                        isAsc = 1,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setNewProdList(it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.newProductVC.filterByNewProduct(
                                        searchString = searchedText,
                                        sortBy = "ProduitLanP",
                                        isAsc = 1,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setNewProdList(it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (newProductFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.newProductVC.filterByNewProduct(
                                        searchString = searchedText,
                                        sortBy = "CodeVCLanP",
                                        isAsc = 2,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setNewProdList(it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.newProductVC.filterByNewProduct(
                                        searchString = searchedText,
                                        sortBy = "dateOp",
                                        isAsc = 2,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setNewProdList(it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.newProductVC.filterByNewProduct(
                                        searchString = searchedText,
                                        sortBy = "ProduitLanP",
                                        isAsc = 2,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setNewProdList(it)
                                    }
                                }
                            }
                        }
                    }
                }

                if (searchValue is ListSearch.ThirdSearch) {
                    when (newProductFilterListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (newProductFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.newProductVC.filterByPrixProduct(
                                        searchString = searchedText,
                                        sortBy = "CodeVCLanP",
                                        isAsc = 1,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setNewProdList(it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.newProductVC.filterByPrixProduct(
                                        searchString = searchedText,
                                        sortBy = "dateOp",
                                        isAsc = 1,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setNewProdList(it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.newProductVC.filterByPrixProduct(
                                        searchString = searchedText,
                                        sortBy = "ProduitLanP",
                                        isAsc = 1,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setNewProdList(it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (newProductFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.newProductVC.filterByPrixProduct(
                                        searchString = searchedText,
                                        sortBy = "CodeVCLanP",
                                        isAsc = 2,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm).collect {
                                        setNewProdList(it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.newProductVC.filterByPrixProduct(
                                        searchString = searchedText,
                                        sortBy = "dateOp",
                                        isAsc = 2,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setNewProdList(it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.newProductVC.filterByPrixProduct(
                                        searchString = searchedText,
                                        sortBy = "ProduitLanP",
                                        isAsc = 2,
                                        filterByConcurrent = filterByConcurent,
                                        filterByTypComm = filterByTypeComm
                                    ).collect {
                                        setNewProdList(it)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    fun saveNewProduct(newProduct: NewProductVC) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.newProductVC.upsert(newProduct)
        }
    }


    fun deleteNewProduct(newProduct: NewProductVC) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.newProductVC.delete(newProduct)
        }
    }


    private fun setNewProdList(newProductVCWithImages:  List<NewProductVCWithImages>){
        //  if(it.any { it.status == ITEMSTATUS.DELETED.status })
        newProductFilterListstate = newProductFilterListstate.copy(
            lists = emptyList()
        )
        newProductFilterListstate = newProductFilterListstate.copy(
            lists = newProductVCWithImages
        )


      //  getNewProductJob.cancel()
    }

    @SuppressLint("SuspiciousIndentation")
    fun setDeletedNewProduct(newProduct: NewProductVC) {
        viewModelScope.launch(dispatcher) {

            if(!newProduct.isSync && newProduct.status == ItemStatus.INSERTED.status){
                proCaisseLocalDb.newProductVC.delete(newProduct)
                proCaisseLocalDb.imageVC.deleteByCodeTypeVc(newProduct.codeVCLanPM)

            }

                else
            proCaisseLocalDb.newProductVC.setDeleted(
                code = newProduct.codeVCLanP,
                codeMobile = newProduct.codeVCLanPM
            )
           filterNewProductVC(newProductFilterListstate)
         }



    }




    fun restDeletedNewProd(newProduct: NewProductVCWithImages) {
        viewModelScope.launch(dispatcher) {



            val status :String
            val isSync :Boolean
            if(newProduct.newProductVC!!.codeVCLanP == newProduct.newProductVC!!.codeVCLanPM){
                status = ItemStatus.DELETED.status
                isSync = false
            }
            else {
                status = ItemStatus.SELECTED.status
                isSync = true

            }

            proCaisseLocalDb.newProductVC.restDeleted(
                code = newProduct.newProductVC!!.codeVCLanP,
                status =status,
                isSync =isSync
            )
            filterNewProductVC(newProductFilterListstate)
        }
    }










    fun handleAddVisiteEvents(
        validationAddVcEvents: ValidationAddNewVcEvent,
        popBackStack: () -> Unit,
        codeM: String,
        saveImageList: () -> Unit,
        utilisateur: Utilisateur,

        ) {

                when (validationAddVcEvents) {
                    is ValidationAddNewVcEvent.AddNewVc -> {
                        val userID = utilisateur.codeUt


                        saveImageList()

                        saveNewProduct(
                            codeM = codeM,
                            userID = userID,
                            event = validationAddVcEvents
                        )


                        onModifyChange(false)
                        // navController.navigate(Screen.Clients.Route)
                        popBackStack()
                    }
                }

    }


    private fun saveNewProduct(
                        codeM: String,
                       userID: String,
                       event: ValidationAddNewVcEvent.AddNewVc){
        val newProductVC = selectedNewProduct.newProductVC?: NewProductVC()
        val newProduct =
            NewProductVC(
                id = if(newProductVC.id!=0L) newProductVC.id else 0,

                codeVCLanP = if (newProductVC.codeVCLanP != "") newProductVC.codeVCLanP
                else codeM,
                codeVCLanPM = codeM,
                produitLanP = event.addNewtVc.produit,
                dateOp = getCurrentDateTime(),
                codeConcur = event.addNewtVc.concurrent.codeconcurrent,
                noteOp = event.addNewtVc.note,
                prixLanP = StringUtils.stringToDouble(event.addNewtVc.prix),
                tauxPromo = StringUtils.stringToDouble(event.addNewtVc.taux),
                codeUser = userID.toInt(),
                infoOp1 = "",
                codeTypeCom = event.addNewtVc.typeCommunication.codeTypeCom
            )
        newProduct.status = ItemStatus.INSERTED.status
        newProduct.isSync = false

        saveNewProduct(newProduct =newProduct )
    }
}