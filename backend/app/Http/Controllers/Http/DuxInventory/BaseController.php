<?php

namespace App\Http\Controllers\Http\DuxInventory;


use App\Http\Controllers\Controller;
use App\Http\Requests\BaseRequest;
use App\Models\DuxInventory\M_Document;
use App\Services\ImageService;

use Illuminate\Support\Facades\Log;


class BaseController extends Controller
{

    public function index()
    {
        try {
            $models = $this->modelClass::all();
            return response()->success($models);
        } catch (\Exception $e) {
            return response()->error($e);
        }
    }

    public function add()
    {
        $validatedData = app($this->requestClass)->validated()['object'];
        $response = $this->createOrUpdateBatch($this->modelClass, $validatedData);
        return $this->handleJsonResponse($response['updatedAndCreatedObjects'], $response['errors']);
    }

    public function addParentAndChildren()
    {
        $validatedData = app($this->requestClass)->validated()['object'];
        $response = $this->createOrUpdateBatch($this->modelClass, $validatedData, true);
        return $this->handleJsonResponse($response['updatedAndCreatedObjects'], $response['errors']);
    }

    public function handleJsonResponse($updatedAndCreatedObjects, $errors)
    {
        if (!empty($errors) && !empty($updatedAndCreatedObjects)) {
            return response()->partialSuccess($updatedAndCreatedObjects, $errors);
        } elseif (!empty($errors) && empty($updatedAndCreatedObjects)) {
            return response()->errors($errors);
        }
        return response()->success($updatedAndCreatedObjects);
    }


    public function createOrUpdateBatch($modelClass, array $validatedData, $parent = false, $child = false, $modelInstance = null)
    {
        $updatedAndCreatedObjects = [];
        $errors = [];
        if ($child && $modelInstance && $modelInstance->exists) {
            $foreignKey = $modelInstance['id'];
        }
        foreach ($validatedData as $object) {
           try {
                if ($modelClass == M_Document::class && isset($this->idClasseDocument) && !is_null($this->idClasseDocument)) {
                    $object['idClasseDocument'] = $this->idClasseDocument;
                }
                if ($parent) {
                    $lignes = $object['lignes'] ?? [];
                    unset($object['lignes']);
                }
                $modelInstance = $modelClass::findOrNew($object['id'] ?? null);
                if ($child) {
                    $object[$this->parentIdField] = $foreignKey;
                }
                $modelInstance->fill($object);
                $modelInstance->saveOrFail();
                if ($parent) {
                    $lignesResponse = $this->createOrUpdateBatch($this->ligneModelClass, $lignes, false, true, $modelInstance);
                    $modelInstance['lignes'] = $lignesResponse;
                }
                $updatedAndCreatedObjects[] = $modelInstance;
            } catch (\Exception $e) {
                Log::error('Error creating or updating model: ' . $e->getMessage(), ['object' => $object]);
                $errors[] = [
                    'idMobile' => $object['idMobile'] ?? null,
                    'id' => $object['id'] ?? null,
                    'error' => $e->getMessage()
                ];
            }
        }
        if ($child) {
            return $this->handleChildrenResponse($updatedAndCreatedObjects, $errors);
        } else {
            return compact('updatedAndCreatedObjects', 'errors');
        }
    }


    public function handleChildrenResponse($updatedAndCreatedObjects, $errors)
    {
        if (!empty($errors) && !empty($updatedAndCreatedObjects)) {
            return [
                'status' => 'partial_success',
                'data' => $updatedAndCreatedObjects,
                'errors' => $errors,
            ];
        } elseif (!empty($errors) && empty($updatedAndCreatedObjects)) {
            return [
                'status' => 'error',
                'errors' => $errors,
            ];
        }
        return [
            'status' => 'success',
            'data' => $updatedAndCreatedObjects,
        ];
    }


    public function uploadImage(ImageService $imageService, BaseRequest $request)
    {
        $errors = [];
        $updatedObjects = [];
        $validatedData = $request->validated()['object'];
        foreach ($validatedData as $object) {
            try {
                if (!isset($object['file'], $object['name'], $object['table'])) {
                    throw new \Exception('Invalid inputs');
                }
                $modelClass = "\\App\\Models\\DuxInventory\\" . $object['table'];
                $modelInstance = $modelClass::where('idMobile', $object['idMobile'])->firstOrFail();
                if ($modelInstance->photo) {
                    $imageService->deleteImage($modelInstance->photo, $object['table']);
                }
                $fileName = $imageService->saveImage($object);
                if ($fileName) {
                    $modelInstance->photo = $fileName;
                    $modelInstance->saveOrFail();
                    $updatedObjects[] = $modelInstance;
                }
            } catch (\Exception $e) {
                Log::error('Error creating or updating model: ' . $e->getMessage(), ['object' => $object]);
                $errors[] = [
                    'id' => $object['id'] ?? null,
                    'error' => $e->getTrace()
                ];
            }
        }
        if (!empty($errors) && !empty($updatedObjects)) {
            return response()->partialSuccess($updatedObjects, $errors);
        } elseif (!empty($errors) && empty($updatedObjects)) {
            return response()->errors($errors);
        }
        return response()->success($updatedObjects);
    }


}
