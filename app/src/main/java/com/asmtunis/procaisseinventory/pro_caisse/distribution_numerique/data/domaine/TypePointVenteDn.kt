package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.TYPE_POINT_VENTE_TABLE)
@Serializable
data class TypePointVenteDn(
    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = "CodeTypePV")
    @SerialName("CodeTypePV")
    var codeTypePV: String = "",

    @ColumnInfo(name = "TypePV")
    @SerialName("TypePV")
    var typePV: String = "",

    @ColumnInfo(name = "NotePV")
    @SerialName("NotePV")
    var notePV: String? = "",

    @ColumnInfo(name = "EtatPv")
    @SerialName("EtatPv")
    var etatPv : Int? = 0
)