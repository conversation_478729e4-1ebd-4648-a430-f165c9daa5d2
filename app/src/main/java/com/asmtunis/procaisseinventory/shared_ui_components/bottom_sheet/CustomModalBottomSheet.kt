@file:OptIn(ExperimentalMaterial3Api::class, ExperimentalMaterial3Api::class)

package com.asmtunis.procaisseinventory.shared_ui_components.bottom_sheet

import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.ConfirmationNumber
import androidx.compose.material.icons.twotone.Delete
import androidx.compose.material.icons.twotone.Print
import androidx.compose.material.icons.twotone.Sync
import androidx.compose.material.icons.twotone.Transform
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.shared_ui_components.HtmlView
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import kotlinx.coroutines.launch

@Composable
fun <T> CustomModalBottomSheet(
    remoteResponseState: RemoteResponseState<T>,
    title: String,
    showDeleteIcon: Boolean = true,
    showPrintIcon: Boolean = true,
    canTransfromBCToBL: Boolean = false,
    canInvoice: Boolean = false,
    status: String,
    onDismissRequest: () -> Unit,
    onDeleteRequest: () -> Unit,
    onValidate: () -> Unit = {},
    onBCtoBL: () -> Unit = {},
    onFacturer: () -> Unit = {},
    onPrintRequest: () -> Unit = {},
    onSyncRequest: () -> Unit = {},

    ) {
    val density = LocalDensity.current
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val scope = rememberCoroutineScope()
    val isLoading = remoteResponseState.loading
    val error = remoteResponseState.error
    val message = remoteResponseState.message

    ModalBottomSheet(
        sheetState = sheetState,
        onDismissRequest = {
            scope.launch {
                sheetState.hide()
            }
            onDismissRequest()
        },
    ) {
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = title,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.titleLarge
        )
        Spacer(
            modifier = Modifier.height(20.dp)
        )

        if (showPrintIcon) {
            IconButton(
                modifier = Modifier.fillMaxWidth(),
                onClick = {
                    onPrintRequest()
                }) {


                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start
                ) {
                    Icon(
                        modifier = Modifier.size(36.dp),
                        imageVector = Icons.TwoTone.Print,
                        contentDescription = stringResource(id = R.string.print)
                    )
                    Spacer(modifier = Modifier.width(width = 8.dp))
                    Text(text = stringResource(id = R.string.print))
                }
            }
        }



        if (status == ItemStatus.WAITING.status) {
            IconButton(
                modifier = Modifier.fillMaxWidth(),
                onClick = {
                    onValidate()

                    scope.launch {
                        sheetState.hide()
                    }
                    onDismissRequest()
                }) {


                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start
                ) {
                    Icon(
                        modifier = Modifier.size(36.dp),
                        imageVector = Icons.TwoTone.Sync,
                        contentDescription = stringResource(id = R.string.valide)
                    )
                    Spacer(modifier = Modifier.width(width = 8.dp))
                    Text(stringResource(id = R.string.valide))
                }
            }


        }


        if (canInvoice) {
            IconButton(
                modifier = Modifier.fillMaxWidth(),
                onClick = {
                    onFacturer()

                    scope.launch {
                        sheetState.hide()
                    }
                    onDismissRequest()
                }) {


                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start
                ) {
                    Icon(
                        modifier = Modifier.size(36.dp),
                        imageVector = Icons.TwoTone.ConfirmationNumber,
                        contentDescription = stringResource(id = R.string.facturer)
                    )
                    Spacer(modifier = Modifier.width(width = 8.dp))
                    Text(text = stringResource(id = R.string.facturer))
                }
            }

        }


        if (canTransfromBCToBL) {
            IconButton(
                modifier = Modifier.fillMaxWidth(),
                onClick = {


                    scope.launch {
                        sheetState.hide()
                        onDismissRequest()
                        onBCtoBL()

                    }

                }) {


                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start
                ) {
                    Icon(
                        modifier = Modifier.size(36.dp),
                        imageVector = Icons.TwoTone.Transform,
                        contentDescription = stringResource(id = R.string.TransformeEnBL)
                    )
                    Spacer(modifier = Modifier.width(width = 8.dp))
                    Text(text = stringResource(id = R.string.TransformeEnBL))
                }
            }
        }

        if (showDeleteIcon && status != ItemStatus.DELETED.status) {
            IconButton(
                modifier = Modifier.fillMaxWidth(),
                onClick = {
                    onDeleteRequest()

                    scope.launch {
                        sheetState.hide()
                    }
                    onDismissRequest()
                }
            ) {


                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start
                ) {
                    Icon(
                        modifier = Modifier.size(36.dp),
                        imageVector = Icons.TwoTone.Delete,
                        contentDescription = stringResource(id = R.string.delete)
                    )
                    Spacer(modifier = Modifier.width(width = 8.dp))
                    Text(text = stringResource(id = R.string.delete))
                }
            }

        }

       if (status == ItemStatus.INSERTED.status || status == ItemStatus.INSERTED_REG_FROM_REGLEMENT.status) {
            IconButton(
                modifier = Modifier.fillMaxWidth(),
                enabled = !isLoading,
                onClick = {
                    onSyncRequest()
                 //   onDismissRequest()
                }) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start
                ) {
                    if(isLoading) { LottieAnim(lotti = R.raw.loading, size = 100.dp) }
                   else Icon(
                        modifier = Modifier.size(36.dp),
                        imageVector = Icons.TwoTone.Sync,
                        contentDescription = stringResource(id = R.string.sync_title)
                    )
                    Spacer(modifier = Modifier.width(width = 8.dp))
                    Text(text = stringResource(id = R.string.sync_title))
                }
            }

            androidx.compose.animation.AnimatedVisibility(
                visible = error != null && title == message,
                enter = slideInVertically { with(density) { 40.dp.roundToPx() } } + fadeIn(),
                exit = fadeOut(animationSpec = keyframes { this.durationMillis = 120 })
            ) {
                if (error != null) {
                    HtmlView(error)
                //    Text(text = error, color = MaterialTheme.colorScheme.error)
                }
            }

            }
        Spacer(modifier = Modifier.height(50.dp))

    }
}

