package com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.ligne_bn_entree.repository

import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.LigneBonEntree
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.ligne_bn_entree.dao.LigneBonEntreeDAO
import kotlinx.coroutines.flow.Flow


class LigneBonEntreeLocalRepositoryImpl(
        private val ligneBonEntreeDAO: LigneBonEntreeDAO
    ) : LigneBonEntreeLocalRepository {
    override fun upsertAll(value: List<LigneBonEntree>) =
        ligneBonEntreeDAO.insertAll(value)

    override fun upsert(value: LigneBonEntree) =
        ligneBonEntreeDAO.insert(value)

    override fun deleteAll() =
        ligneBonEntreeDAO.deleteAll()

    override fun deleteAllList(listLigneBE: List<LigneBonEntree>) = ligneBonEntreeDAO.deleteAllList(listLigneBE)

    override fun deleteByBeCode(code: String, exercice: String)  =
        ligneBonEntreeDAO.deleteByBeCode(code, exercice)

    override fun deleteByBeCodeAndCodeBar(code: String, exercice: String, codeBare: String) =
        ligneBonEntreeDAO.deleteByBeCodeAndCodeBar(code, exercice, codeBare)

    override fun getAll(): Flow<List<LigneBonEntree>> =
        ligneBonEntreeDAO.all

    override fun updateLigneBonEntreeStatus(
        bonEntNum: String,
        bonEntNumM: String,
        exercice: String
    ) =
        ligneBonEntreeDAO.updateLigneBonEntreeStatus(
            bonEntNum = bonEntNum,
            bonEntNumM = bonEntNumM,
            exercice = exercice
        )
}