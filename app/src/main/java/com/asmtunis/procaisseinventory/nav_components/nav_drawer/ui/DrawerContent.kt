package com.asmtunis.procaisseinventory.nav_components.nav_drawer.ui

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.Globals.DEVICE_ID
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.utils.PackageUtils
import com.asmtunis.procaisseinventory.nav_components.ID
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.nav_components.data.model.MenuModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.view_model.*
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState

@Composable
fun DrawerContent(
    navigateTo: (route: Any) -> Unit,
    onMenuClick: (menu: MenuModel) -> Unit,
    menus: List<MenuModel>,
    mainViewModel: MainViewModel,
    navDrawerVM: NavigationDrawerViewModel,
    networkViewModel: NetworkViewModel,
    dataViewModel: DataViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    syncInventoryViewModel: SyncInventoryViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    settingViewModel: SettingViewModel
) {
    val context = LocalContext.current
    val clipboardManager = LocalClipboardManager.current
    val androidId = remember { DEVICE_ID }
    val selectedItemIndex = if (menus.first().id == ID.INVENTORY_HOME_ID)
        menus.indexOf(navDrawerVM.proInventorySelectedMenu)
    else menus.indexOf(navDrawerVM.proCaisseSelectedMenu)

    val utilisateur = mainViewModel.utilisateur
    val toaster = rememberToasterState()
    val exerciceCode = mainViewModel.exerciceList.firstOrNull()?.exerciceCode?: ""
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    ModalDrawerSheet(
        drawerShape = MaterialTheme.shapes.small,
        drawerContainerColor = MaterialTheme.colorScheme.primaryContainer,
        drawerContentColor = MaterialTheme.colorScheme.onPrimaryContainer,
        drawerTonalElevation = 4.dp
    ) {
        Spacer(modifier = Modifier.height(16.dp))
        DrawerHeader(
            toaster = toaster,
            navigateTo = { navigateTo(it) },
            dataViewModel = dataViewModel,
            navDrawerViewModel = navDrawerVM,
            getProCaisseDataViewModel = getProCaisseDataViewModel,
            getSharedDataViewModel = getSharedDataViewModel,
            getProInventoryDataViewModel = getProInventoryDataViewModel,
            syncInventoryViewModel = syncInventoryViewModel,
            syncSharedViewModels = syncSharedViewModels,
            isConnected = networkViewModel.isConnected,
            syncProcaisseViewModels = syncProcaisseViewModels,
            utilisateur = utilisateur,
            stationList = mainViewModel.stationList,
            logo = mainViewModel.logo,
            exerciceCode = exerciceCode
        )





        Spacer(modifier = Modifier.height(16.dp))
        LazyColumn(
            state = rememberLazyListState(initialFirstVisibleItemIndex = selectedItemIndex),
            modifier = Modifier.wrapContentSize()
        ) {
            items(
                menus.size,
                key = {
                    menus[it].id
                }
            ) { index ->


                if (navDrawerVM.addMenu(
                        menus = menus,
                        index = index,
                        clientList = mainViewModel.clientList,
                        authorizationList = utilisateur.autorisationUser.filter { it.AutEtat == "1" }
                    )
                ) {
                    NavigationDrawerItem(
                        selected = menus[index] == if (menus.first().id == ID.INVENTORY_HOME_ID)
                            navDrawerVM.proInventorySelectedMenu
                        else navDrawerVM.proCaisseSelectedMenu,
                        onClick = {
                            onMenuClick(menus[index])

                        },
                        icon = {
                            Icon(
                                imageVector = menus[index].icon,
                                contentDescription = stringResource(id = menus[index].contentDescription)
                            )
                        },
                        label = {
                            Text(text = stringResource(id = menus[index].title))
                        },
                        modifier = Modifier.padding(6.dp),
                        badge = {

                            if (navDrawerVM.isLoading(
                                    getProCaisseDataViewModel = getProCaisseDataViewModel,
                                    getProInventoryDataViewModel = getProInventoryDataViewModel,
                                    getSharedDataViewModel = getSharedDataViewModel,
                                    menus = menus,
                                    index = index
                                )
                            )
                                LottieAnim(lotti = R.raw.loading, size = 25.dp)
                            else if (navDrawerVM.errorGetData(
                                    getProCaisseDataViewModel = getProCaisseDataViewModel,
                                    getProInventoryDataViewModel = getProInventoryDataViewModel,
                                    getSharedDataViewModel = getSharedDataViewModel,
                                    menus = menus,
                                    index = index
                                ) == ""
                            )
                                Text(text = menus[index].badge)
                            else if (navDrawerVM.errorGetData(
                                    getProCaisseDataViewModel = getProCaisseDataViewModel,
                                    getProInventoryDataViewModel = getProInventoryDataViewModel,
                                    getSharedDataViewModel = getSharedDataViewModel,
                                    menus = menus,
                                    index = index
                                ) != ""
                            ) LottieAnim(lotti = R.raw.network_error, size = 25.dp)


                        }
                    )
                }
            }

            item {

                Text(
                    modifier = Modifier.fillMaxWidth().wrapContentWidth(Alignment.End)
                        .padding(end = 16.dp, bottom = 9.dp)
                        .clickable {

                            showToast(
                                context = context,
                                toaster = toaster,
                                message = AnnotatedString(androidId).text + "\n" + " Copied",
                                type = ToastType.Success,
                            )
                            clipboardManager.setText(AnnotatedString(androidId))
                        },
                    text = AnnotatedString(androidId),
                    fontSize = MaterialTheme.typography.bodySmall.fontSize
                )
            }
            item {
                Text(
                    text =  PackageUtils.getVersionName(context = context),
                    modifier = Modifier.fillMaxWidth().wrapContentWidth(Alignment.End)
                        .padding(end = 16.dp, bottom = 20.dp),
                    textAlign = TextAlign.End,
                    fontSize = MaterialTheme.typography.bodySmall.fontSize
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))


    }

}




