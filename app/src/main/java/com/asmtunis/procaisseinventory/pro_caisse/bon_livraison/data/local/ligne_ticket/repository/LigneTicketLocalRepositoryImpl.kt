package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.ligne_ticket.repository

import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.ligne_ticket.dao.LigneTicketDAO
import kotlinx.coroutines.flow.Flow



class LigneTicketLocalRepositoryImpl(
    private val ligneTicketDAO: LigneTicketDAO
) : LigneTicketLocalRepository {

    override fun upsertAll(value: List<LigneTicket>) = ligneTicketDAO.insertAll(value)
    override fun upsert(value: LigneTicket)  = ligneTicketDAO.insert(value)
    override fun updateNumTicket(codeM: String, newCode: Int, exercice: String, carnet: String) =
        ligneTicketDAO.updateNumTicket(
            codeM = codeM,
            newCode = newCode,
            exercice = exercice,
            carnet = carnet
        )

    override fun deleteAll() = ligneTicketDAO.deleteAll()


    override fun deleteByCodeM(code: String, exercice: String) = ligneTicketDAO.deleteByTicketM(
        numTicket = code,
        ltExercice = exercice
    )

    override fun deleteByNumTicket(numTicket: String, ltExercice: String) =
        ligneTicketDAO.deleteByNumTicket(numTicket = numTicket, ltExercice = ltExercice)

    override fun getAll(): Flow<List<LigneTicket>> = ligneTicketDAO.all
}