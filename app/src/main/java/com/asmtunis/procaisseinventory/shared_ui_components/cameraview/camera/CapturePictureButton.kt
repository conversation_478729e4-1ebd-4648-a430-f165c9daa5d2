package com.asmtunis.procaisseinventory.shared_ui_components.cameraview.camera

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun CapturePictureButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit = { },
) {
    val coroutineScope = rememberCoroutineScope()
    var isButtonEnabled by remember { mutableStateOf(true) }

    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    val color = if (isPressed) Color.Red else Color.DarkGray
    val contentPadding = PaddingValues(if (isPressed) 8.dp else 12.dp)

    OutlinedButton(
        modifier = modifier,
        shape = CircleShape,
        border = BorderStroke(2.dp, MaterialTheme.colorScheme.errorContainer),
        contentPadding = contentPadding,
        colors = ButtonDefaults.outlinedButtonColors(/*contentColor = Color.Black*/),
        enabled = isButtonEnabled,
        onClick = {

        }
    ) {
        Button(
            modifier = Modifier.fillMaxSize(),
            shape = CircleShape,
            colors = ButtonDefaults.buttonColors(containerColor = color),
            interactionSource = interactionSource,
            enabled = isButtonEnabled, // Disable the inner button
            onClick = {

                isButtonEnabled = false
                onClick()
                // Re-enable button after a delay
                coroutineScope.launch {
                    delay(2500) // Adjust delay as needed
                    isButtonEnabled = true
                }




            }
            ) {
            // No content
        }
    }
}



