<?php

namespace App\Http\Controllers\Http\DuxInventory;

use App\Http\Requests\FilterRequest;
use App\Models\DuxInventory\P_Article_Unite;
use App\Http\Controllers\Controller;


class P_Article_UniteController extends Controller
{
    public function getUniteArticles(FilterRequest $request)
    {
        try {
            $validated = $request->validated();
            $limit = $validated['object']['limit'];
            $station = $validated['object']['station'];
            $articlesUnites = P_Article_Unite::where('j_codeStation', $station)->paginate($limit);
            return response()->success($articlesUnites);
        } catch (\Exception $e) {
            return response()->error($e);
        }
    }




}
