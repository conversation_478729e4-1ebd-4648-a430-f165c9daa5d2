package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.ui

import com.asmtunis.procaisseinventory.nav_components.NavDrawer
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.adaptive.ExperimentalMaterial3AdaptiveApi
import androidx.compose.material3.adaptive.layout.AnimatedPane
import androidx.compose.material3.adaptive.layout.ListDetailPaneScaffoldRole
import androidx.compose.material3.adaptive.navigation.NavigableListDetailPaneScaffold
import androidx.compose.material3.adaptive.navigation.rememberListDetailPaneScaffoldNavigator
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.view_model.BonTransfertPrintViewModel
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_no_calcul.SelectArticleNoCalculViewModel
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.InventoryBonTransfertDetailRoute
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.BonTransfertViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import kotlinx.coroutines.launch


@OptIn(ExperimentalMaterial3AdaptiveApi::class)
@Composable
fun BonTransfertScreen(
    navigate: (route: Any) -> Unit,
    navDrawerViewmodel: NavigationDrawerViewModel,
    bonTransfertViewModel: BonTransfertViewModel,
    barCodeViewModel: BarCodeViewModel,
    mainViewModel: MainViewModel,
    selectArtInventoryVM: SelectArticleNoCalculViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    printViewModel: PrintViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    bluetoothVM: BluetoothViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    settingViewModel: SettingViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncInventoryViewModel: SyncInventoryViewModel,
    sunmiPrintManager: com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager,
    bonTransfertPrintViewModel: BonTransfertPrintViewModel
) {


    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)
    val navigator = rememberListDetailPaneScaffoldNavigator<Any>()
    val scope = rememberCoroutineScope()
    NavDrawer(
        navigate = { navigate(it) },
        drawer = drawer,
        navDrawerViewmodel = navDrawerViewmodel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        mainViewModel = mainViewModel,
        syncSharedViewModels = syncSharedViewModels,
        syncInventoryViewModel = syncInventoryViewModel,
       syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingViewModel
    ) {
        NavigableListDetailPaneScaffold(
            // modifier = Modifier.fillMaxSize().padding(padding),
            navigator = navigator,
            listPane = {
                BonTranfertPane(
                    navigate = {


                        scope.launch {
                            if(it is InventoryBonTransfertDetailRoute)
                                navigator.navigateTo(
                                    pane = ListDetailPaneScaffoldRole.Detail
                                )
                            else navigate(it)
                        }

                    },
                    drawer = drawer,
                    networkViewModel = networkViewModel,
                    dataViewModel = dataViewModel,
                    barCodeViewModel =barCodeViewModel,
                    navDrawerViewmodel = navDrawerViewmodel,
                    mainViewModel = mainViewModel,
                    printViewModel = printViewModel,
                    bluetoothVM = bluetoothVM,
                    getProInventoryDataViewModel = getProInventoryDataViewModel,
                    settingViewModel = settingViewModel,
                    bonTransfertViewModel = bonTransfertViewModel,
                    selectArtInventoryVM = selectArtInventoryVM,
                    syncInventoryViewModel = syncInventoryViewModel,
                    sunmiPrintManager = sunmiPrintManager
                )

            },
            detailPane = {

                //     val content = navigator.currentDestination?.content as Client?



                /*      if(uiWindowState.navigationType != ReplyNavigationType.PERMANENT_NAVIGATION_DRAWER) {
                          bonCommandeVM.restBonCommande()
                      }
   */

                AnimatedPane {
                    if (bonTransfertViewModel.selectedListLgBonTransfert.isEmpty())
                        LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
                    else
                        BonTranfertDetailPane(
                            navigate = { navigate(it) },
                            popBackStack = {
                                //   popBackStack()
                                scope.launch {
                                    navigator.navigateBack()
                                }
                            },
                            networkViewModel = networkViewModel,
                            bluetoothVM = bluetoothVM,
                            printViewModel = printViewModel,
                            dataViewModel = dataViewModel,
                            mainViewModel = mainViewModel,
                            navigationDrawerViewModel = navDrawerViewmodel,
                            settingViewModel = settingViewModel,
                            selectArtInventoryVM = selectArtInventoryVM,
                            bonTransfertViewModel = bonTransfertViewModel,
                            barCodeViewModel = barCodeViewModel,
                            sunmiPrintManager = sunmiPrintManager,
                            bonTransfertPrintViewModel = bonTransfertPrintViewModel
                        )
                }
            },
            /*  extraPane = {

                    }*/
        )
    }
}



