package com.asmtunis.procaisseinventory.core.sync

import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.model.DataResult
import kotlinx.coroutines.flow.Flow

/**
 * Generic interface for repositories that support synchronization operations.
 * This interface standardizes how repositories interact with the sync system.
 *
 * @param T The type of entity this repository manages
 * @param R The type of response expected from remote sync operations
 */
interface SyncableRepository<T, R> {

    /**
     * Retrieves all unsynchronized items from the local database.
     * These are items that have been created or modified locally but not yet synced to the server.
     *
     * @return Flow of list of unsynchronized items
     */
    suspend fun getUnsynchronizedItems(): Flow<List<T>>

    /**
     * Retrieves the count of unsynchronized items.
     * Useful for displaying sync status without loading all items.
     *
     * @return Flow of count of unsynchronized items
     */
    suspend fun getUnsynchronizedCount(): Flow<Int>

    /**
     * Marks items as synchronized in the local database.
     * This should be called after successful remote synchronization.
     *
     * @param items List of items to mark as synchronized
     */
    suspend fun markAsSynchronized(items: List<T>)

    /**
     * Marks a single item as synchronized in the local database.
     *
     * @param item Item to mark as synchronized
     */
    suspend fun markAsSynchronized(item: T)

    /**
     * Marks items as having sync errors.
     * This should be called when remote synchronization fails.
     *
     * @param items List of items that failed to sync
     * @param errorMessage Error message to store
     */
    suspend fun markSyncError(items: List<T>, errorMessage: String)

    /**
     * Performs the actual remote synchronization operation.
     * This method should handle the network call to sync items with the server.
     *
     * @param items List of items to synchronize
     * @param baseConfig Base configuration for the API call
     * @return Flow of DataResult containing the sync response
     */
    suspend fun performRemoteSync(items: List<T>, baseConfig: BaseConfig): Flow<DataResult<R>>

    /**
     * Processes the sync response and updates local data accordingly.
     * This method should handle the server response and update local database.
     *
     * @param response The response from the remote sync operation
     * @param originalItems The original items that were synced
     */
    suspend fun processSyncResponse(response: R, originalItems: List<T>)

    /**
     * Gets the sync configuration for this repository.
     * This allows different entities to have different sync behaviors.
     *
     * @return SyncConfig for this repository
     */
    fun getSyncConfig(): SyncConfig

    /**
     * Gets the sync entity type for this repository.
     *
     * @return SyncEntity type
     */
    fun getSyncEntity(): SyncEntity

    /**
     * Validates items before synchronization.
     * This method should check if items are valid for sync.
     *
     * @param items List of items to validate
     * @return List of validation errors, empty if all items are valid
     */
    suspend fun validateForSync(items: List<T>): List<String>

    /**
     * Prepares items for synchronization.
     * This method can be used to transform or enrich items before sync.
     *
     * @param items List of items to prepare
     * @return List of prepared items
     */
    suspend fun prepareForSync(items: List<T>): List<T>

    /**
     * Handles sync conflicts when they occur.
     * This method should resolve conflicts between local and remote data.
     *
     * @param localItem Local version of the item
     * @param remoteItem Remote version of the item
     * @return Resolved item
     */
    suspend fun handleSyncConflict(localItem: T, remoteItem: T): T

    /**
     * Cleans up after sync operations.
     * This method can be used to perform cleanup tasks after sync.
     */
    suspend fun cleanupAfterSync()

    /**
     * Gets sync statistics for this repository.
     *
     * @return SyncStats for this repository
     */
    suspend fun getSyncStats(): SyncStats
}

/**
 * Base implementation of SyncableRepository that provides common functionality.
 * Repositories can extend this class to inherit default behavior.
 */
abstract class BaseSyncableRepository<T, R> : SyncableRepository<T, R> {

    override fun getSyncConfig(): SyncConfig {
        return SyncConfig() // Default configuration
    }

    override suspend fun validateForSync(items: List<T>): List<String> {
        return emptyList() // No validation by default
    }

    override suspend fun prepareForSync(items: List<T>): List<T> {
        return items // No preparation by default
    }

    override suspend fun handleSyncConflict(localItem: T, remoteItem: T): T {
        return remoteItem // Prefer remote version by default
    }

    override suspend fun cleanupAfterSync() {
        // No cleanup by default
    }

    override suspend fun getSyncStats(): SyncStats {
        return SyncStats() // Default empty stats
    }

    override suspend fun markAsSynchronized(item: T) {
        markAsSynchronized(listOf(item))
    }
}

/**
 * Interface for repositories that support batch synchronization operations.
 * This extends the basic SyncableRepository with batch-specific functionality.
 */
interface BatchSyncableRepository<T, R> : SyncableRepository<T, R> {

    /**
     * Gets the optimal batch size for this repository.
     *
     * @return Optimal batch size
     */
    fun getBatchSize(): Int

    /**
     * Splits items into batches for synchronization.
     *
     * @param items List of items to batch
     * @return List of batches
     */
    fun createBatches(items: List<T>): List<List<T>> {
        val batchSize = getBatchSize()
        return items.chunked(batchSize)
    }

    /**
     * Performs batch synchronization.
     *
     * @param batches List of batches to synchronize
     * @param baseConfig Base configuration for the API call
     * @return Flow of DataResult containing batch sync responses
     */
    suspend fun performBatchSync(batches: List<List<T>>, baseConfig: BaseConfig): Flow<DataResult<List<R>>>
}

/**
 * Interface for repositories that support priority-based synchronization.
 */
interface PrioritySyncableRepository<T, R> : SyncableRepository<T, R> {

    /**
     * Gets the priority of items for synchronization.
     *
     * @param items List of items to prioritize
     * @return Map of items to their priorities
     */
    suspend fun getItemPriorities(items: List<T>): Map<T, SyncPriority>

    /**
     * Sorts items by sync priority.
     *
     * @param items List of items to sort
     * @return List of items sorted by priority
     */
    suspend fun sortByPriority(items: List<T>): List<T> {
        val priorities = getItemPriorities(items)
        return items.sortedBy { priorities[it]?.level ?: SyncPriority.MEDIUM.level }
    }
}
