package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


@Entity(tableName = ProCaisseConstants.AUTRE_TABLE)
@Serializable
data class AutreVC(
    @PrimaryKey(autoGenerate = true)
    @Transient
    val id: Long = 0,

    @ColumnInfo(name = "CodeAutre")
    @SerialName("CodeAutre")
    var codeAutre: String = "",

    @ColumnInfo(name = "Autre")
    @SerialName("Autre")
    var autre: String = "",

    @ColumnInfo(name = "AutreNote")
    @SerialName("AutreNote")
    var autreNote: String = "",

    @ColumnInfo(name = "DateOp")
    @SerialName("DateOp")
    var dateOp: String = "",

    @ColumnInfo(name = "CodeConcur")
    @SerialName("CodeConcur")
    var codeConcur: String = "",

    @ColumnInfo(name = "NoteOp")
    @SerialName("NoteOp")
    var noteOp: String = "",

    @ColumnInfo(name = "CodeUser")
    @SerialName("CodeUser")
    var codeUser: Int? = 0,

    @ColumnInfo(name = "InfoOp1")
    @SerialName("InfoOp1")
    var infoOp1: String? = "",

    @ColumnInfo(name = "CodeTypeCom")
    @SerialName("CodeTypeCom")
    var codeTypeCom: String? = "",


    @ColumnInfo(name = "Code_Mob")
    @SerialName("Code_Mob")
    var codeMob: String = ""

) : BaseModel()
