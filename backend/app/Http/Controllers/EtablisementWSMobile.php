<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class EtablisementWSMobile extends Controller
{

    /*fonction get data from database*/

    public function getEtablisements(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Etablisement')->first());
    }

   

}
