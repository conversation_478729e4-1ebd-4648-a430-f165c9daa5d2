package com.asmtunis.countrycodepicker.presentation

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.Menu
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.asmtunis.asm.countrycodepicker.R


//todo this is in shared ui compoenets
// maybe make shared ui compoenets as a local ui lib for all project
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppBar(
    titleVisibilty : Boolean = true,
    title: String = stringResource(id = R.string.select_country),
    showNavIcon: Boolean = true,
    navIcon: ImageVector = Icons.TwoTone.Menu,
    onNavigationClick: () -> Unit = {},
    actions: @Composable (RowScope.() -> Unit) = {}
) {

    TopAppBar(
        title = {
            AnimatedVisibility(
                visible = titleVisibilty,
                enter = fadeIn() + slideInVertically(),
                exit = fadeOut() + slideOutVertically()
            ) {
                Row(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(text = title, textAlign = TextAlign.Center, fontSize = MaterialTheme.typography.titleSmall.fontSize)
                }
            }

        },
        navigationIcon = {
            if (showNavIcon){
                IconButton(
                    onClick = onNavigationClick
                ) {
                    Icon(
                        imageVector = navIcon,
                        contentDescription = stringResource(
                            id = R.string.select_country

                        )
                    )
                }
            }
        },
        actions = {
            actions()
        }
    )
}