package com.asmtunis.procaisseinventory.shared_ui_components.payment_methods

import android.content.Context
import com.asmtunis.procaisseinventory.R

object PaymentUtils {
     fun getPaymentModeDetailed(
         context: Context,
         rEGCMntEspece: Double,
         rEGCMntCarteBancaire: Double,
         rEGCMntTraite: Double,
         rEGCMntCheque: Double,
     ): String {
         var typePayment = ""
         typePayment = if (rEGCMntEspece > 0) context.resources.getString(R.string.espece)
         else typePayment

         typePayment =
             if (rEGCMntCarteBancaire > 0) "$typePayment " + context.resources.getString(R.string.carte_banquaire)
             else typePayment

         typePayment = if (rEGCMntTraite > 0) "$typePayment " + context.resources.getString(R.string.traite)
         else typePayment

         typePayment = if (rEGCMntCheque > 0) "$typePayment "+ context.resources.getString(R.string.checK)
         else typePayment

         return typePayment
     }
    fun getPaymentMode(
        montantTotalCheques: Double,
        montantTotalTicketResto: Double,
        cashValue: Double
    ): String {
        var count = 0
        if (cashValue > 0) count++
        if (montantTotalCheques > 0) count++
        if (montantTotalTicketResto > 0) count++

        return when (count) {
            0 -> ""
            1 -> when {
                cashValue > 0 -> PaymentMode.CASH.value
                montantTotalCheques > 0 -> PaymentMode.CHECK.value
                montantTotalTicketResto > 0 -> PaymentMode.TICKET.value
                else -> ""
            }
            else -> PaymentMode.VARIOUS.value
        }
    }
}