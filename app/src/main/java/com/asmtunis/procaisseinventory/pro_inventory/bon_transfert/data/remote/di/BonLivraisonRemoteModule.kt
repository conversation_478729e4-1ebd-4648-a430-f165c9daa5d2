package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.remote.di

import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.remote.api.bn_livraison.BonLivraisonApi
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.remote.api.bn_livraison.BonLivraisonApiImpl
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.remote.api.ligne_bn_livraison.LigneBonLivraisonApi
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.remote.api.ligne_bn_livraison.LigneBonLivraisonApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
    @InstallIn(SingletonComponent::class)
    object BonLivraisonRemoteModule {

        @Provides
        @Singleton
        fun provideBonLivraisonApi(client: HttpClient): BonLivraisonApi = BonLivraisonApiImpl(client)




        @Provides
        @Singleton
        fun provideLigneBonLivraisonApi(client: HttpClient): LigneBonLivraisonApi = LigneBonLivraisonApiImpl(client)


}