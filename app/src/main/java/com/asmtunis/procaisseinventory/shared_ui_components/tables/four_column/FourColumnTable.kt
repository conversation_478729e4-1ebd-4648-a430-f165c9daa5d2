package com.asmtunis.procaisseinventory.shared_ui_components.tables.four_column

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.shared_ui_components.CustomModifiers.customWidth
import com.asmtunis.procaisseinventory.shared_ui_components.swipe_actions.swipe_to_delete.SwipeToDeleteContainer
import com.asmtunis.procaisseinventory.shared_ui_components.swipe_actions.swipe_to_delete.rememberDeleteState
import com.simapps.ui_kit.ModifiersUtils.detectTapGestures

@Composable
fun FourColumnTable(
    showFilterLine: Boolean = false,
    onShowFilterLineChange: (Boolean) -> Unit = {},
    fiterValue: String = "",
    onFilterValueChange: (String) -> Unit = {},
    selectedListArticle: List<SelectedArticle>,
    onSwipeToDelete: (SelectedArticle) -> Unit = {},
    canModify: Boolean,
    onTap: (SelectedArticle) -> Unit,
    onLongPress: (SelectedArticle) -> Unit = {},
    rowTitls : List<String> = listOf("N°","Désignation","Qté dans toutes les stations","Qté"),
    firstColumn: (SelectedArticle) -> String,
    secondColumn: (SelectedArticle) -> String,
    thirdColumn: (SelectedArticle) -> String,
    infoText: (SelectedArticle) -> String = { "" }
) {

    val density = LocalDensity.current
    val listState = rememberLazyListState()
    HorizontalDivider(color = MaterialTheme.colorScheme.outline)
    FourTableHeaderTitles(rowTitls = rowTitls,
        showFilterLine = showFilterLine,
    onShowFilterLineChange = { onShowFilterLineChange(it) }
    )
    AnimatedVisibility(
        visible = showFilterLine,
        enter = slideInVertically {
            with(density) { 40.dp.roundToPx() }
        } + fadeIn(),
        exit = fadeOut(
            animationSpec = keyframes {
                this.durationMillis = 120
            }
        )
    ) {
        OutlinedTextField(
            value = fiterValue,
            onValueChange = onFilterValueChange,
            label = { Text("Filter ") },
            modifier = Modifier.fillMaxWidth().padding(18.dp),
            trailingIcon = {
                Row(
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    AnimatedVisibility(
                        visible = fiterValue.isNotEmpty(),
                        enter = fadeIn(),
                        exit = fadeOut(),
                    ) {
                        IconButton(onClick = { onFilterValueChange("") }) {
                            Icon(
                                imageVector = Icons.Filled.Clear,
                                contentDescription = stringResource(id = R.string.your_divice_id),
                            )
                        }
                    }


                }
            },
        )
    }

    HorizontalDivider(color = MaterialTheme.colorScheme.outline)


    LazyColumn(
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
        state = listState
    ) {
        items(
            items = selectedListArticle,
            key = { item -> "${item.article.aRTCode}_${item.prixCaisse}_${item.discount}_${item.quantity}_${selectedListArticle.indexOf(item)}" } // Unique key with index to prevent duplicates
            //count = selectedArticle.size,
            /* key = {
                 ligneBonEntree[it].id
             }*/
        ) { item ->

            val currentItem by rememberUpdatedState(item)
            SwipeToDeleteContainer (
                canModify = canModify,
                state = rememberDeleteState(
                    onDelete = {
                        onSwipeToDelete(currentItem)
                    }
                ),
                content = {

                    Column (
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier//.clickable { onTap(item) }

                            .detectTapGestures(
                                key1 = item,
                                onPress = {  },/* Called when the gesture starts */
                                onDoubleTap = {},
                                onLongPress = {  },
                                onTap = { onTap(item)  } /* Called on Tap */
                            )


                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.SpaceEvenly,
                            modifier = Modifier
                                .padding(start = 3.dp, end = 3.dp)
                                .heightIn(40.dp, 90.dp)
                                .fillMaxWidth()


                        ) {
                            Text(
                                text = (selectedListArticle.indexOf(item)+1).toString(),
                                textAlign = TextAlign.Center,
                                fontSize = MaterialTheme.typography.bodySmall.fontSize,
                                modifier = Modifier.customWidth(0.08f),
                                color = MaterialTheme.colorScheme.outline
                            )


                            Text(
                                text = firstColumn(item),
                                fontSize = MaterialTheme.typography.bodySmall.fontSize,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.customWidth(0.52f)
                            )
                            Text(
                                text = secondColumn(item),
                                textAlign = TextAlign.Center,
                                fontSize = MaterialTheme.typography.bodySmall.fontSize,
                                modifier = Modifier.customWidth(0.2f)
                            )
                            Text(
                                text = thirdColumn(item),
                                fontSize = MaterialTheme.typography.bodySmall.fontSize,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.customWidth(0.2f)
                            )
                        }


                       if (infoText(item).isNotEmpty())
                            Text(
                                text = infoText(item),
                                style = MaterialTheme.typography.bodySmall,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth(),
                                color = MaterialTheme.colorScheme.secondary
                            )

                        HorizontalDivider(color = MaterialTheme.colorScheme.outline)
                    }
                }
            )

        }
    }


}
