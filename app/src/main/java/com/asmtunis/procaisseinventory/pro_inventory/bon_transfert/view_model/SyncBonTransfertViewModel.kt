package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROINVENTORY_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.sync.SyncEntity
import com.asmtunis.procaisseinventory.core.sync.SyncManager
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.AddBatchBonLivraisonResponse
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.BonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.data.NestedItem
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryRemote
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject

/**
 * ViewModel for synchronizing BonTransfert (Inventory Transfer) data
 * Handles sync operations for inventory transfers between locations
 */
@HiltViewModel
class SyncBonTransfertViewModel @Inject constructor(
    private val syncManager: SyncManager,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    private val proInventoryRemote: ProInventoryRemote,
    private val proInventoryLocalDb: ProInventoryLocalDb,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val listenNetwork: ListenNetwork,
) : ViewModel() {

    // Sync state from SyncManager
    val syncState = syncManager.syncState

    // Auto-sync configuration
    private var autoSyncState by mutableStateOf(false)
    private val autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(
        key = PROINVENTORY_AUTO_SYNC_AUTHORISATION,
        default = true
    ).distinctUntilChanged()
    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()
    private var connected by mutableStateOf(false)

    // Sync response state
    var responseAddBonTransfertState: RemoteResponseState<List<AddBatchBonLivraisonResponse>> by mutableStateOf(RemoteResponseState())
        private set

    // Unsynced data
    var bonTransfertNotSync: Map<BonLivraison, List<LigneBonLivraison>> by mutableStateOf(emptyMap())
        private set
    var notSyncBonTransfertObj: String by mutableStateOf("")
        private set

    init {
        observeUnsyncedBonTransfert()
    }

    /**
     * Observe unsynced BonTransfert data and trigger auto-sync when conditions are met
     */
    private fun observeUnsyncedBonTransfert() {
        viewModelScope.launch {
            val bonTransfertNotSyncFlow = proInventoryLocalDb.bonLivraison.noSynced().distinctUntilChanged()

            combine(networkFlow, bonTransfertNotSyncFlow, autoSyncFlow) { isConnected, bonTransfertNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                bonTransfertNotSyncList?.ifEmpty { emptyMap() } ?: emptyMap()
            }.collectLatest { bonTransfertMap ->
                if (bonTransfertMap.isEmpty()) {
                    bonTransfertNotSync = emptyMap()
                    return@collectLatest
                }
                bonTransfertNotSync = bonTransfertMap
                if (connected && autoSyncState) {
                    syncBonTransfert()
                }
            }
        }
    }

    /**
     * Synchronize BonTransfert data with the server
     * @param selectedBonTransfert Optional specific BonTransfert to sync, if null syncs all unsynced items
     */
    fun syncBonTransfert(selectedBonTransfert: BonLivraison = BonLivraison()) {
        viewModelScope.launch(dispatcherIO) {
            try {
                responseAddBonTransfertState = RemoteResponseState(data = null, loading = true, error = null)

                // Try SyncManager first
                if (selectedBonTransfert == BonLivraison() && bonTransfertNotSync.isNotEmpty()) {
                    val result = syncManager.syncEntity(SyncEntity.BON_TRANSFERT)
                    if (result.isSuccess) {
                        responseAddBonTransfertState = RemoteResponseState(
                            data = emptyList(),
                            loading = false,
                            error = null
                        )
                        return@launch
                    }
                }

                // Fallback to legacy sync method
                syncBonTransfertLegacy(selectedBonTransfert)

            } catch (exception: Exception) {
                responseAddBonTransfertState = RemoteResponseState(
                    data = null,
                    loading = false,
                    error = exception.message ?: "Unknown error"
                )
            }
        }
    }

    /**
     * Legacy sync method for BonTransfert
     */
    private suspend fun syncBonTransfertLegacy(selectedBonTransfert: BonLivraison) {
        val nestedItems: ArrayList<NestedItem<BonLivraison, List<LigneBonLivraison>>> = ArrayList()

        bonTransfertNotSync.forEach { (key, value) ->
            nestedItems.add(
                NestedItem(
                    parent = key,
                    children = value
                )
            )
        }

        if (selectedBonTransfert != BonLivraison()) {
            nestedItems.removeIf { it.parent != selectedBonTransfert }
        }

        val baseConfigObj = GenericObject(
            proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                ?.let { Json.decodeFromString(it) } ?: BaseConfig(),
            Json.encodeToJsonElement(nestedItems),
        )

        notSyncBonTransfertObj = Json.encodeToString(baseConfigObj)

        proInventoryRemote.bonLivraison.addBatchBonLivraisonWithLines(notSyncBonTransfertObj).onEach { result ->
            when (result) {
                is DataResult.Success -> {
                    result.data?.let { responses ->
                        for (response in responses) {
                            if (response.code == 10200) {
                                proInventoryLocalDb.bonLivraison.updateState(
                                    code = response.bonTransNum,
                                    codeM = response.bonTransNumM,
                                    exercice = response.bonTransExerc,
                                )
                                proInventoryLocalDb.ligneBonLivraison.updateState(
                                    code = response.bonTransNum,
                                    codeM = response.bonTransNumM,
                                    exercice = response.bonTransExerc,
                                )
                            }
                        }
                    }
                    responseAddBonTransfertState = RemoteResponseState(
                        data = result.data,
                        loading = false,
                        error = null
                    )
                }

                is DataResult.Loading -> {
                    responseAddBonTransfertState = RemoteResponseState(
                        data = null,
                        loading = true,
                        error = null
                    )
                }

                is DataResult.Error -> {
                    responseAddBonTransfertState = RemoteResponseState(
                        data = null,
                        loading = false,
                        error = result.message,
                        message = selectedBonTransfert.bONTransNum
                    )
                }
            }
        }.flowOn(dispatcherIO).launchIn(viewModelScope)
    }

    // ========================================
    // SYNCMANAGER INTEGRATION & CONVENIENCE METHODS
    // ========================================

    /**
     * Trigger manual sync for all BonTransfert entities using SyncManager
     */
    fun triggerManualSyncAll() {
        syncBonTransfert()
    }

    /**
     * Check if sync is currently in progress
     */
    val isSyncing: Boolean
        get() = responseAddBonTransfertState.loading

    /**
     * Get the current sync error if any
     */
    val syncError: String?
        get() = responseAddBonTransfertState.error

    /**
     * Get the count of unsynchronized items
     */
    val unsyncedCount: Int
        get() = bonTransfertNotSync.size

    /**
     * Reset all sync states to initial state
     */
    fun resetAllSyncStates() {
        responseAddBonTransfertState = RemoteResponseState()
    }

    /**
     * Get sync status summary for UI display
     */
    val syncStatusSummary: String
        get() = when {
            isSyncing -> "Synchronisation bons de transfert en cours..."
            syncError != null -> "Erreur de synchronisation: $syncError"
            unsyncedCount > 0 -> "$unsyncedCount bons de transfert non synchronisés"
            else -> "Tous les bons de transfert sont synchronisés"
        }

    /**
     * Get detailed sync information
     */
    val detailedSyncInfo: Map<String, Any>
        get() = mapOf(
            "totalItems" to bonTransfertNotSync.size,
            "isLoading" to isSyncing,
            "error" to (syncError ?: ""),
            "lastSyncObject" to notSyncBonTransfertObj,
            "networkConnected" to connected,
            "autoSyncEnabled" to autoSyncState
        )

    /**
     * Get transfer statistics
     */
    val transferStatistics: Map<String, Int>
        get() {
            val totalLines = bonTransfertNotSync.values.sumOf { it.size }
            return mapOf(
                "totalTransfers" to bonTransfertNotSync.size,
                "totalLines" to totalLines,
                "averageLinesPerTransfer" to if (bonTransfertNotSync.isNotEmpty()) {
                    totalLines / bonTransfertNotSync.size
                } else 0
            )
        }
}
