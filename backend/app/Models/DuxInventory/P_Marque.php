<?php

namespace App\Models\DuxInventory;



class P_Marque extends BaseModel
{

    protected $table = 'P_Marque';
    protected $connection = 'onthefly';
    public $incrementing = false;
    public $timestamps = false;
    protected $guarded = [];


    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->idClasse = 'Marque';
        $this->useCode = true;

    }




}
