package com.asmtunis.procaisseinventory.shared_ui_components.tables.three_column

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp

@Composable
fun ThreeTableHeaderTitles(
    rowTitls: List<String>,
    showFilterLine: Boolean = false,
    onShowFilterLineChange: (Boolean) -> Unit = {},) {

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceEvenly,
            modifier = Modifier
                .heightIn(40.dp)
                .fillMaxWidth()
                //  .background(color = MaterialTheme.colorScheme.scrim)
                .clickable {
                    onShowFilterLineChange(!showFilterLine)
                },
        ) {
            Text(
                text = rowTitls[0],
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth(0.4f),
                color = MaterialTheme.colorScheme.outline,
                fontSize = MaterialTheme.typography.titleLarge.fontSize

            )
            Text(
                text = rowTitls[1],
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth(0.6f),
                color = MaterialTheme.colorScheme.outline,
                fontSize = MaterialTheme.typography.titleLarge.fontSize
            )
        }




    }

}