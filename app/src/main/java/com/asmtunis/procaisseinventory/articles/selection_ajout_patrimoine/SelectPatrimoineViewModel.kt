package com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.domaine.SelectedPatrimoine
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals.NOT_FOUND_LOCALLY
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePat
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePatrimoine
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.Constant.CODE_VERIFICATION_INVENTAIRE_EXIST
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.ControlInventaireResponse
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.ControleInventaire
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.io.IOException
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import java.util.*
import javax.inject.Inject

@HiltViewModel
class SelectPatrimoineViewModel
@Inject
constructor(
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val proInventoryLocalDb: ProInventoryLocalDb,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val proCaisseRemote: ProCaisseRemote,
    private val listenNetwork: ListenNetwork,
) : ViewModel() {

    // Network state tracking
    private var isConnected by mutableStateOf(false)
    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()

    init {
        // Monitor network state
        viewModelScope.launch {
            networkFlow.collect { connected ->
                isConnected = connected
                Log.d("SelectPatrimoineVM", "Network state changed: $connected")
            }
        }
    }

    var backUpSelectedPatrimoine by mutableStateOf(SelectedPatrimoine())
        private set
    fun onBackUpSelectedPatrimoineChange(value: SelectedPatrimoine) {
        backUpSelectedPatrimoine = value
    }


    var showFilterLine by mutableStateOf(false)
        private set
    fun onShowFilterLineChange(value: Boolean) {
        showFilterLine = value
    }


    var fiterValue by mutableStateOf("")
        private set

    fun onFilterValueChange(value: String) {
        fiterValue = value
    }


    var marqueFilter: String by mutableStateOf("")
        private set

    fun onMarqueFilterChange(value: String) {
        marqueFilter = value
    }

    var marqueTxt: String by mutableStateOf("")
        private set

    fun onMarqueTextChange(value: String) {
        marqueTxt = value
    }


    var invPatByNumSerie: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
        private set



    var keepTypedNumSerie: Boolean by mutableStateOf(false)
        private set

    fun onKeepTypedNumSerieChange(value: Boolean) {
        keepTypedNumSerie = value
    }

    var showSetNumeSerie: Boolean by mutableStateOf(false)
        private set

    fun onShowSetNumeSerieChange(value: Boolean) {
        if(value) resetPatrimoineVerificationState()

        showSetNumeSerie = value
    }

    var selectedPatrimoine by mutableStateOf(SelectedPatrimoine())
        private set

    fun setSelectedPat(value: SelectedPatrimoine) {
        Log.d("dfdsfsfsffs", "setSelectedPat")
        selectedPatrimoine = value
    }


    var typePiece: String by mutableStateOf("")
        private set

    fun onTypePieceChange(value: String) {
        typePiece = value
    }

    var selectedPatrimoineList = mutableStateListOf<SelectedPatrimoine>()
        private set

    fun addItemToSelectedPatrimoineList(selectedPatrimoine: SelectedPatrimoine, deleteSelectedItem: Boolean = false) {
//TODO SEE if directly replace pat if exist (like current case) or show a message, ...

        val lg = selectedPatrimoineList.filter { it.numSerie == selectedPatrimoine.numSerie }
        if (lg.isNotEmpty()) {
            selectedPatrimoineList.replaceAll { if (it.numSerie == lg.first().numSerie) selectedPatrimoine else it }
        } else {
            selectedPatrimoineList.add(selectedPatrimoine)
        }

    }
    fun clearSelectedPatrimoineList() {
        selectedPatrimoineList.clear()
    }

    fun resetSelectedPatrimoineArticles() {
        selectedPatrimoineList.clear()
        selectedPatrimoine = SelectedPatrimoine()
    }

    fun setConsultationSelectedPatrimoineList(
        article: Article?,
        numSerie: String = "",
        quantity: Double = 0.0,
        imageList: List<ImagePieceJoint> = emptyList(),
        note: String,
        marque: Marque = Marque()
    ) {
        selectedPatrimoineList.add(
            SelectedPatrimoine(
                articleCode = article?.aRTCode?: "N/A",
                numSerie = numSerie,
                quantity = quantity,
                imageList = imageList,
                note = note,
                marqueCode = marque.mARCode
            ),
        )
    }

    fun deleteSelectedPatrimoine(selectedPat: SelectedPatrimoine) {
        Log.d("dfdsfsfsffs", "delete "+selectedPat.numSerie)
        selectedPatrimoineList.removeIf { it.numSerie == selectedPat.numSerie }
    }
    fun deleteItemToSelectedPatrimoineList(
        value: SelectedPatrimoine,
        isUpdate: Boolean = false,
        selectedInvPatrimoine: BonCommande = BonCommande()
    ) {
        deleteSelectedPatrimoine(selectedPat = value)

        setSelectedPat(SelectedPatrimoine())

        if(isUpdate) {
            viewModelScope.launch(dispatcherIO) {
                proCaisseLocalDb.ligneBonCommande.deleteByLgDevNumBon(code = selectedInvPatrimoine.devCodeM)
                proCaisseLocalDb.inventairePieceJoint.deleteByDevNumNotSync(devNum = selectedInvPatrimoine.devCodeM)
            }

        }
    }

    fun deleteImage(images: ImagePieceJoint) {
        // selectedPatrimoineList.removeIf { it.imageList. }

        val initialImageList = selectedPatrimoine.imageList

        val imagesList = initialImageList.filter { it.imgUrl != images.imgUrl }

        selectedPatrimoine =
            selectedPatrimoine.copy(
                imageList = imagesList,
            )

        addItemToSelectedPatrimoineList(selectedPatrimoine)
    }

    var patrimoineVerificationState: RemoteResponseState<ControlInventaireResponse> by mutableStateOf(RemoteResponseState())
        private set

    fun resetPatrimoineVerificationState() {
        Log.d("eeedddddcxxcxc", "resetPatrimoineVerificationState")
        patrimoineVerificationState = RemoteResponseState()
    }

    /**
     * Expose network state for UI components
     */
    fun getNetworkState(): Boolean = isConnected

    fun patrimoineVerification(
        baseConfig: BaseConfig,
        controlPatrimoine: ControleInventaire
    ) {
        Log.d("SelectPatrimoineVM", "patrimoineVerification called - Network connected: $isConnected")

        try {
            viewModelScope.launch {
                // Check network state first - prioritize offline verification when offline
                if (!isConnected) {
                    Log.d("SelectPatrimoineVM", "Network offline - using local verification only")
                    verifyLocaly(controlPatrimoine = controlPatrimoine)
                    return@launch
                }

                // Network is available - try remote verification first
                Log.d("SelectPatrimoineVM", "Network online - attempting remote verification")
                patrimoineVerificationState = RemoteResponseState(data = null, loading = true, error = null)

                val verificationPatrimoine =
                    async {
                        val baseConfigObj =
                            GenericObject(
                                baseConfig,
                                Json.encodeToJsonElement(controlPatrimoine),
                            )
                        proCaisseRemote.inventairePatrimoine.controlInventaire(Json.encodeToString(baseConfigObj))
                    }
                verificationPatrimoine.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            Log.d("SelectPatrimoineVM", "Remote verification success: ${result.data?.code}")
                            // {"message":"derni\u00e8re type enregistrement different de (affectation\/entree)","code":10704}

                            if (result.data!!.code == "10200" || result.data.code == CODE_VERIFICATION_INVENTAIRE_EXIST) {
                                if (controlPatrimoine.DEV_info3 != TypePatrimoine.AFFECTATION.typePat) {
                                    verifyLocaly(controlPatrimoine = controlPatrimoine)
                                    proCaisseLocalDb.invePatrimoine.getByNumSerie(controlPatrimoine.LG_DEV_NumSerie).collect { resultData ->

                                        invPatByNumSerie = resultData ?: emptyMap()

                                        patrimoineVerificationState =
                                            if (invPatByNumSerie.isEmpty()) {
                                                RemoteResponseState(
                                                    data = null,
                                                    loading = false,
                                                    error = NOT_FOUND_LOCALLY,
                                                    message = TypePatrimoine.INVENTAIRE.typePat,
                                                )
                                            } else {
                                                RemoteResponseState(
                                                    data = result.data,
                                                    loading = false,
                                                    error = null,
                                                )
                                            }
                                    }
                                } else {
                                    patrimoineVerificationState =
                                        RemoteResponseState(
                                            data = result.data,
                                            loading = false,
                                            error = null,
                                        )
                                }
                            }
                            else {
                                patrimoineVerificationState =
                                    RemoteResponseState(
                                        data = result.data,
                                        loading = false,
                                        message = result.data.code,
                                        error = /*result.data.code + ": " +*/ result.data.message,
                                    )
                            }
                        }

                        is DataResult.Loading -> patrimoineVerificationState = RemoteResponseState(data = null, loading = true, error = null)

                        is DataResult.Error -> {
                            Log.e("SelectPatrimoineVM", "Remote verification failed - falling back to local: ${result.message}")
                            verifyLocaly(controlPatrimoine = controlPatrimoine)
                        }

                        else -> {}
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }
        } catch (e: IOException) {
            Log.e("SelectPatrimoineVM", "patrimoineVerification error: ${e.message}")
            // Fallback to local verification on any exception
            viewModelScope.launch {
                verifyLocaly(controlPatrimoine = controlPatrimoine)
            }
        }
    }
    private fun verifyLocaly(controlPatrimoine: ControleInventaire) {
        viewModelScope.launch {
            var shouldVerifyPat = true // Flag to control execution of verifyPatLocaly

            if (controlPatrimoine.DEV_info3 == TypePatrimoine.AFFECTATION.typePat) {
                shouldVerifyPat = verifyCodeBareBatimentLocaly(controlPatrimoine = controlPatrimoine)
            }

            if (shouldVerifyPat) { // Only execute if verifyCodeBareBatimentLocaly returned true (empty list)
                verifyPatLocaly(controlPatrimoine = controlPatrimoine)
            }
        }
    }

    private suspend fun verifyCodeBareBatimentLocaly(controlPatrimoine: ControleInventaire): Boolean {
        var isEmpty = true // Assume initially the list is empty
        proCaisseLocalDb.immobilisation.getAllZoneConsomationByImoCB(controlPatrimoine.LG_DEV_NumSerie)
            .collect { listImmobilisation ->

                if (listImmobilisation.isNullOrEmpty()) {
                    return@collect // Continue to the next emission, isEmpty stays true
                } else {
                    isEmpty = false // Mark the list as not empty
                    patrimoineVerificationState =
                        RemoteResponseState(
                            data = null,
                            loading = false,
                            error = "Déjà affecté au bâtiment '" + listImmobilisation.first().cLINomPren + "' (Local vérification)"
                        )
                    return@collect // Stop collecting (optimization, assuming first emission is representative)
                }
            }
        return isEmpty // Return true if the list was empty, false otherwise
    }

    private suspend fun verifyPatLocaly(controlPatrimoine: ControleInventaire) {
        proCaisseLocalDb.invePatrimoine.getByNumSerie(controlPatrimoine.LG_DEV_NumSerie).collect {
            val cltCode = controlPatrimoine.DEV_CodeClient

            val devInfo3 = controlPatrimoine.DEV_info3.lowercase(Locale.ROOT)

            if (it.isNullOrEmpty() && devInfo3 != TypePatrimoine.AFFECTATION.typePat) {
                patrimoineVerificationState =
                    RemoteResponseState(
                        data = null,
                        loading = false,
                        message = devInfo3,
                        error = NOT_FOUND_LOCALLY,
                    )
                return@collect
            }

            invPatByNumSerie = it ?: emptyMap()

            if (devInfo3 == TypePatrimoine.AFFECTATION.typePat) {
                verifyAffectationLoccaly(invPatByNumSeri = invPatByNumSerie)
            }

            if (devInfo3 == TypePatrimoine.SORTIE.typePat) {
                verifyDepOutLoccaly(
                    invPatByNumSeri = invPatByNumSerie,
                    cLICode = cltCode,
                )
            }

            if (devInfo3 == TypePatrimoine.ENTREE.typePat) {
                verifyDepInLoccaly(
                    invPatByNumSeri = invPatByNumSerie,
                    cLICode = cltCode,
                )
            }

            if (devInfo3 == TypePatrimoine.INVENTAIRE.typePat) {
                verifyInventaireLoccaly(
                    invPatByNumSeri = invPatByNumSerie,
                    cLICode = cltCode,
                )
            }
        }
    }

    private fun verifyAffectationLoccaly(invPatByNumSeri: Map<BonCommande, List<LigneBonCommande>>) {
        Log.d("SelectPatrimoineVM", "verifyAffectationLoccaly - entries count: ${invPatByNumSeri.size}")

        if (invPatByNumSeri.isEmpty()) {
            Log.d("SelectPatrimoineVM", "No existing affectation found locally - allowing new affectation")
            patrimoineVerificationState =
                RemoteResponseState(
                    data =
                        ControlInventaireResponse(
                            message = if (isConnected) "Success Local vérification" else "Success Offline vérification",
                            code = "10200",
                        ),
                    loading = false,
                    error = null,
                )
            return
        }

        val existingClient = invPatByNumSeri.entries.first().key.dEVClientName
        Log.d("SelectPatrimoineVM", "Existing affectation found for client: $existingClient")

        patrimoineVerificationState =
            RemoteResponseState(
                data = null,
                loading = false,
                error = "Déjà affecter à '$existingClient' (${if (isConnected) "Local" else "Offline"} vérification)",
            )
    }

    private fun verifyDepOutLoccaly(
        invPatByNumSeri: Map<BonCommande, List<LigneBonCommande>>,
        cLICode: String,
    ) {
        val invPat = invPatByNumSeri.entries.first().key

        if (invPat.dEV_info3 == null) {
            patrimoineVerificationState =
                RemoteResponseState(
                    data = null,
                    loading = false,
                    error = NOT_FOUND_LOCALLY, // result.message
                )
            return
        }

        if (invPat.dEV_info3 == TypePatrimoine.SORTIE.typePat) {
            patrimoineVerificationState =
                RemoteResponseState(
                    data = null,
                    loading = false,
                    error = "Dernier opération: " + TypePat.DEP_OUT.typePat + " (Local vérification)"// UiText.StringResource(resId = R.string.last_operation, typePat.DEP_OUT.typePat),
                )
            return
        }

        if (invPat.dEVCodeClient != cLICode) {
            patrimoineVerificationState = RemoteResponseState(
                data = null,
                loading = false,
                error = "Déjà affecté à '" +invPat.dEVClientName+"' (Local vérification)"//UiText.StringResource(resId = R.string.deja_affecter_a_un_autre_client)
            )
        } else {
            patrimoineVerificationState =
                RemoteResponseState(
                    data =
                        ControlInventaireResponse(
                            message = "Success Local vérification",
                            code = "10200",
                        ),
                    loading = false,
                    error = null,
                )
        }
    }

    private fun verifyDepInLoccaly(
        invPatByNumSeri: Map<BonCommande, List<LigneBonCommande>>,
        cLICode: String,
    ) {
        val invPat = invPatByNumSeri.entries.first().key

        if (invPat.dEV_info3 == null) {
            patrimoineVerificationState =
                RemoteResponseState(
                    data = null,
                    loading = false,
                    error = NOT_FOUND_LOCALLY,
                )
            return
        }

        if (invPat.dEV_info3 != TypePatrimoine.SORTIE.typePat) {
            patrimoineVerificationState =
                RemoteResponseState(
                    data = null,
                    loading = false,
                    error = "Dernier opération: " + invPat.dEV_info3 + ", Zone consomation '"+ invPat.dEVClientName+"' (Local vérification)"//UiText.StringResource(resId = R.string.last_operation, typePat.DEP_OUT.typePat),
                )
            return
        }

        patrimoineVerificationState =
                /*   if (invPat.dEVCodeClient != cLICode) {
                       VerificationInvPatState(
                           data = null, loading = false, error = "Déja affecté à un autre client " + (invPat.dEVClientName ?: ""),
                       )
                   } else {*/
            RemoteResponseState(
                data =
                    ControlInventaireResponse(
                        message = "Success Local vérification",
                        code = "10200",
                    ),
                loading = false,
                error = null,
            )
        // }
    }

    private fun verifyInventaireLoccaly(
        invPatByNumSeri: Map<BonCommande, List<LigneBonCommande>>,
        cLICode: String,
    ) {
        val invPat = invPatByNumSeri.entries.first().key

        if (invPat.dEV_info3 == null) {
            patrimoineVerificationState =
                RemoteResponseState(
                    data = null,
                    loading = false,
                    error = NOT_FOUND_LOCALLY, // result.message
                )
            return
        }
        if (invPat.dEV_info3 == TypePatrimoine.SORTIE.typePat) {
            patrimoineVerificationState =
                RemoteResponseState(
                    data = null,
                    loading = false,
                    error = "Dernier opération: "+ TypePat.DEP_OUT.typePat+ " (Local vérification)"// UiText.StringResource(resId = R.string.last_operation, typePat.DEP_OUT.typePat),
                )
            return
        }
        if (invPat.dEVCodeClient != cLICode) {

            patrimoineVerificationState =
                RemoteResponseState(
                    data = null,
//                        data =
//                            ControlInventaireResponse(
//                                message = "Déja affecté ",
//                                code = "10200",
//                            ),
                    loading = false,
                    message = CODE_VERIFICATION_INVENTAIRE_EXIST,
                    error = "Affecté à '"+ invPat.dEVClientName + "' (Local vérification)",
                )
        } else {
            patrimoineVerificationState =
                RemoteResponseState(
                    data =
                        ControlInventaireResponse(
                            message = "Success Local vérification",
                            code = "10200",
                        ),
                    loading = false,
                    error = null,
                )
        }
    }
}
