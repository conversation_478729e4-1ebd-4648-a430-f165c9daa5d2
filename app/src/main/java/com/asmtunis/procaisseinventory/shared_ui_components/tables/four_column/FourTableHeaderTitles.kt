package com.asmtunis.procaisseinventory.shared_ui_components.tables.four_column

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.PlainTooltip
import androidx.compose.material3.Text
import androidx.compose.material3.TooltipBox
import androidx.compose.material3.TooltipDefaults
import androidx.compose.material3.rememberTooltipState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.shared_ui_components.CustomModifiers.customWidth


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FourTableHeaderTitles (
    rowTitls : List<String>,
    showFilterLine: Boolean = false,
    onShowFilterLineChange: (Boolean) -> Unit = {},
    ) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceEvenly,
        modifier = Modifier
            .heightIn(45.dp)
            .fillMaxWidth()
            .clickable {
                onShowFilterLineChange(!showFilterLine)
            }
    ) {

        Text(
            text = rowTitls[0],
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.labelMedium,
            softWrap = false,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.customWidth(0.1f)
        )
        Text(
            text = rowTitls[1],
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.labelMedium,
            softWrap = false,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.customWidth(0.4f)
        )
        TooltipBox(
            positionProvider = TooltipDefaults.rememberPlainTooltipPositionProvider(),
            tooltip = { PlainTooltip { Text(rowTitls[2]) } },
            state = rememberTooltipState()
        ) {
            Text(
                text = rowTitls[2],
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.labelMedium,
                softWrap = false,
                overflow = TextOverflow.Clip,
                modifier = Modifier.customWidth(0.2f)
            )
        }
        TooltipBox(
            positionProvider = TooltipDefaults.rememberPlainTooltipPositionProvider(),
            tooltip = { PlainTooltip { Text(rowTitls[3]) } },
            state = rememberTooltipState()
        ) {
            Text(
                text = rowTitls[3],
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.labelMedium,
                softWrap = false,
                overflow = TextOverflow.Clip,
                modifier = Modifier.customWidth(0.1f)
            )
        }

    }



}

