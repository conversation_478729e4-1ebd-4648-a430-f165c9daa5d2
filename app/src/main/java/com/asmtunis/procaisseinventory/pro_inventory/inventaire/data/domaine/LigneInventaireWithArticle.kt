package com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class LigneInventaireWithArticleS (
    @Embedded
    @SerialName("ligneInventaire")
    var ligneInventaire: LigneInventaire? = null,

    @Relation(
        parentColumn = "LG_INV_Code_Article",
        entityColumn = "ART_Code"
    )
    @SerialName("article")
    var article: Article? = null,


    )
