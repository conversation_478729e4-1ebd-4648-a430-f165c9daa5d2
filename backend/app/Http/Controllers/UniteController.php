<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class UniteController extends Controller
{

    public function getUnites(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('unite')->get());

    }

    public function addUniteMobile(Request $request)
    {
        $items = $request->json()->all();

        $connection = DatabaseConnection::setConnection($items);

        if (!empty($items)) {
            // $f = $connection->table('famille')->insert($data);
            foreach ($items["object"]['unites'] as $data) {
                if($data["UNI_DDm"]=="NULL")
                $data["UNI_DDm"]=NULL;
                if($data["UNI_export"]=="NULL")
                $data["UNI_export"]=NULL;
                if($data["UNI_Station"]=="NULL")
                $data["UNI_Station"]=NULL;
            
                $exist=$connection->table('unite')
				->where('UNI_Code', '=', $data['UNI_Code'])
				->get();    
				if(is_null($exist) || $exist->isEmpty()){
					$f=$connection->table('unite')->insert($data);
				}else{
				$f=	$connection->table('unite')
				->where('UNI_Code', $data['UNI_Code'])
					->update([
						
						'UNI_Designation' => $data['UNI_Designation'],
						'UNI_export' => $data['UNI_export'],
						'UNI_Station' => $data['UNI_Station'],
						'UNI_User' => $data['UNI_User']
					 ]);
				}
            }

            return response()->json($f);

        } else {
            return (false);
        }

    }

    public function updateFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        $connection->table('famille')->where('FAM_Code', $data["FAM_Code"])->update($data);

        return ("Data modified");

    }

    public function deleteFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $connection->table('famille')->where('FAM_Code', $data["object"])->delete();
        return ("Data Deleted");
    }

    public function updateOrCreate($connection, $data, $table, $whereClauses)
    {
        $exists = $connection->table($table)->where($whereClauses)->first();

        if ($exists) {
            $connection->table($table)->where($whereClauses)->delete();
        }

        return $connection->table($table)->insert($data);

    }
}
