package com.asmtunis.procaisseinventory.shared_ui_components.swipe_actions.swipe_to_reveal

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier

private enum class HorizontalDragValue { Settled, StartToEnd, EndToStart }

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun SwipeToReveal(
    modifier: Modifier,
    canModify: Boolean,
    onDelete:() -> Unit,
    content: @Composable () -> Unit
) {

         /*   var boxSize by remember { mutableFloatStateOf(0F) }
            val scope = rememberCoroutineScope()
            val anchors = DraggableAnchors {
                HorizontalDragValue.Settled at 0f
               // HorizontalDragValue.StartToEnd at boxSize / 3
                HorizontalDragValue.EndToStart at -boxSize / 3
            }

    val density = LocalDensity.current
          val state = remember {
                AnchoredDraggableState(
                     anchors = anchors,
                    initialValue = HorizontalDragValue.Settled,
                   // positionalThreshold = { distance: Float -> distance * 0.3f },
                    positionalThreshold = { with(density) { 56.dp.toPx() } },
                  //  velocityThreshold = { 0.3f },
                    velocityThreshold = { with(density) { 125.dp.toPx() } },
                    animationSpec = tween(),
                    confirmValueChange = { true }
                )
         }
            SideEffect { state.updateAnchors(anchors) }
    val progress = state.progress
    val scale by animateFloatAsState(
        //if (swipeDismissState.targetValue == DismissValue.Default) 0.75f else 2f, label = ""
        if (progress == 1f) 0.75f else 1f + progress, label = ""
    )
            val iconsBackgroundColor by animateColorAsState(
                when (state.targetValue) {
                    HorizontalDragValue.Settled -> MaterialTheme.colorScheme.background
                    HorizontalDragValue.StartToEnd -> Color.Green
                    HorizontalDragValue.EndToStart -> MaterialTheme.colorScheme.error.copy(alpha = progress)
                }, label = "change color"
            )

    if(canModify) {
        Box(modifier = modifier) {
            /*  Box(modifier = Modifier
                    .fillMaxWidth(0.4f)
                  //  .height(70.dp)
                    .background(iconsBackgroundColor)
                 //   .border(2.dp, Color.White)
                    .align(Alignment.CenterStart),
                    contentAlignment = Alignment.Center
                ) {
                    IconButton(onClick = { scope.launch { state.animateTo(HorizontalDragValue.Settled) } }) {
                        Icon(Icons.TwoTone.Favorite, contentDescription = "Favorite")
                    }
                }*/
            Box(
                modifier = Modifier
                    .fillMaxWidth(0.4f)
                    .background(iconsBackgroundColor)
                    .align(Alignment.CenterEnd),
                contentAlignment = Alignment.Center
            ) {
                IconButton(onClick = { onDelete() }) {
                    Icon(
                        Icons.TwoTone.Delete, contentDescription = "Delete",
                        modifier = Modifier.scale(scale),
                        tint = MaterialTheme.colorScheme.errorContainer.copy(alpha = if (progress == 1f) 1f else 1f - progress)
                    )
                }
            }
            Box(modifier = Modifier
                .graphicsLayer { boxSize = size.width }
                .offset {
                    IntOffset(
                        x = state
                            .requireOffset()
                            .roundToInt(), y = 0
                    )
                }
                .fillMaxWidth()
                .anchoredDraggable(state, Orientation.Horizontal)
                .background(MaterialTheme.colorScheme.background)
            ) {
                content()

            }
        }

    }
    else content()*/
}