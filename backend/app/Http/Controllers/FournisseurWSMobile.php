<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;

use App\Http\Controllers\PrefixWSMobile;

class FournisseurWSMobile extends Controller
{
    

	public function getFournisseurs(Request $request)
	 {

	  $data = $request->json()->all();
	   $connection = DatabaseConnection::setConnection($data);
           return response()->json($connection->table('fournisseur')->where('FRS_Nomf', '<>', null)->get());
	 }




	
	 

	
}

