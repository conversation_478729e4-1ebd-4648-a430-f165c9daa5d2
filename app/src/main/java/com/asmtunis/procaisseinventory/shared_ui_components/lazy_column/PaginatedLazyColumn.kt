package com.asmtunis.procaisseinventory.shared_ui_components.lazy_column

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.paging.LoadState
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.itemKey
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim

/**
 * A reusable paginated lazy column component that handles:
 * - Pagination with Paging 3 library
 * - Pull-to-refresh functionality
 * - Loading states (initial, append, refresh)
 * - Error states with retry functionality
 * - Empty state display
 * - Consistent UI patterns across the app
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun <T : Any> PaginatedLazyColumn(
    lazyPagingItems: LazyPagingItems<T>,
    modifier: Modifier = Modifier,
    lazyListState: LazyListState = rememberLazyListState(),
    pullToRefreshEnabled: Boolean = true,
    onRefresh: (() -> Unit)? = null,
    key: ((item: T) -> Any)? = null,
    emptyStateContent: @Composable (() -> Unit)? = null,
    loadingStateContent: @Composable (() -> Unit)? = null,
    errorStateContent: @Composable ((error: Throwable, retry: () -> Unit) -> Unit)? = null,
    content: @Composable (item: T) -> Unit,
) {
    val pullToRefreshState = rememberPullToRefreshState()

    // Handle refresh state
    val isRefreshing = lazyPagingItems.loadState.refresh is LoadState.Loading

    PullToRefreshBox(
        state = pullToRefreshState,
        isRefreshing = isRefreshing,
        onRefresh = {
            if (pullToRefreshEnabled) {
                onRefresh?.invoke() ?: lazyPagingItems.refresh()
            }
        },
        modifier = modifier
    ) {
        when (val refreshState = lazyPagingItems.loadState.refresh) {
            is LoadState.Loading -> {
                // Show initial loading state
                if (loadingStateContent != null) {
                    loadingStateContent()
                } else {
                    DefaultLoadingContent()
                }
            }
            is LoadState.Error -> {
                // Show error state with retry
                if (errorStateContent != null) {
                    errorStateContent(refreshState.error) { lazyPagingItems.retry() }
                } else {
                    DefaultErrorContent(refreshState.error) { lazyPagingItems.retry() }
                }
            }
            is LoadState.NotLoading -> {
                if (lazyPagingItems.itemCount == 0) {
                    // Show empty state
                    if (emptyStateContent != null) {
                        emptyStateContent()
                    } else {
                        DefaultEmptyContent()
                    }
                } else {
                    // Show paginated list
                    LazyColumn(
                        state = lazyListState,
                        modifier = Modifier.fillMaxSize(),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(
                            count = lazyPagingItems.itemCount,
                            key = if (key != null) lazyPagingItems.itemKey(key) else null
                        ) { index ->
                            val item = lazyPagingItems[index]
                            if (item != null) {
                                content(item)
                            }
                        }

                        // Handle append loading state
                        when (val appendState = lazyPagingItems.loadState.append) {
                            is LoadState.Loading -> {
                                item {
                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(16.dp),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        CircularProgressIndicator()
                                    }
                                }
                            }
                            is LoadState.Error -> {
                                item {
                                    AppendErrorContent(appendState.error) {
                                        lazyPagingItems.retry()
                                    }
                                }
                            }
                            is LoadState.NotLoading -> {
                                // Add bottom spacing
                                item {
                                    Spacer(modifier = Modifier.height(80.dp))
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun DefaultLoadingContent() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        LottieAnim(lotti = R.raw.loading, size = 250.dp)
    }
}

@Composable
private fun DefaultEmptyContent() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
    }
}

@Composable
private fun DefaultErrorContent(
    error: Throwable,
    onRetry: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "Error occurred",
            style = MaterialTheme.typography.headlineSmall,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = error.localizedMessage ?: "Unknown error",
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(16.dp))

        androidx.compose.material3.Button(
            onClick = onRetry
        ) {
            Text("Retry")
        }
    }
}

@Composable
private fun AppendErrorContent(
    error: Throwable,
    onRetry: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = error.localizedMessage ?: "Unknown error",
            style = MaterialTheme.typography.bodySmall,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.error
        )

        Spacer(modifier = Modifier.height(8.dp))

        androidx.compose.material3.TextButton(
            onClick = onRetry
        ) {
            Text("Retry")
        }
    }
}
