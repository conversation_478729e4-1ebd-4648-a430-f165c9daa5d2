<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\File;

class LigneInventaireWSMobile extends Controller
{

    public function getLigneInventaires(Request $request,$LG_INV_Code_Inv)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $query =  $connection->table('Ligne_Inventaire')
            ->where('LG_INV_Code_Inv','=',$LG_INV_Code_Inv)->get();


        return response()->json($query);
    }

    public function deleteBatchLigneInventaires(Request $request)
    {


        $items = $request->json()->all();

        if (!empty ($items)) {
            //	$data['object']['CLI_Code'] =(new PrefixWSMobile)->getPrefix($data,"Client");
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {
                $c = $connection->table('Ligne_Inventaire')
                    ->where('LG_INV_Code_Inv', $data["LG_INV_Code_Inv"])->where('LG_INV_Code_Article', $data["LG_INV_Code_Article"])->delete();


                if ($c) {
                    $result[] = $data;
                } else {
                    $result[] = null;
                }


            }


        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }


    public function updateBatchLigneInventaires(Request $request)
    {


        $items = $request->json()->all();

        if (!empty ($items)) {
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {

                $c = $connection->table('Ligne_Inventaire')->where('LG_INV_Code_Inv', $data["LG_INV_Code_Inv"])->where('LG_INV_Code_Article', $data["LG_INV_Code_Article"])->update($data);


                if ($c) {
                    $result[] = $data;
                } else {
                    $result[] = null;
                }


            }


        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }


    public function addBatchLigneInventaires(Request $request)
    {

        $items = $request->json()->all();

        if (!empty ($items)) {
            //	$data['object']['CLI_Code'] =(new PrefixWSMobile)->getPrefix($data,"Client");
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {
                $data["LG_INV_Qte_Stock"] = floatval($data["LG_INV_Qte_Stock"]) . "";
                $data["LG_INV_Qte_Reel"] = floatval($data["LG_INV_Qte_Reel"]) . "";
                $data["LG_INV_Prix_CMP"] = floatval($data["LG_INV_Prix_CMP"]) . "";
                $codeone = " " . $data['LG_INV_Code_Article'];
                $codetwo = "  " . $data['LG_INV_Code_Article'];
                $codethree = "   " . $data['LG_INV_Code_Article'];
                $oneSpace = $connection->select("select ART_Code from article where ART_Code = '" . $codeone . "'");
                $twoSpace = $connection->select("select ART_Code from article where ART_Code = '" . $codetwo . "'");
                $threeSpace = $connection->select("select ART_Code from article where ART_Code = '" . $codethree . "'");
                if ($oneSpace) {
                    $data['LG_INV_Code_Article'] = " " . $data['LG_INV_Code_Article'];
                    $insert1 = $this->updateOrCreate($connection, $data, 'Ligne_Inventaire', [['LG_INV_Code_Article', '=', $data['LG_INV_Code_Article']], ['LG_INV_Code_Inv', '=', $data['LG_INV_Code_Inv']]]);

                    if ($insert1) {
                        $result[] = $data;
                    } else {
                        $result[] = null;
                    }

                } elseif ($twoSpace) {
                    $data['LG_INV_Code_Article'] = "  " . $data['LG_INV_Code_Article'];
                    $insert1 = $this->updateOrCreate($connection, $data, 'Ligne_Inventaire', [['LG_INV_Code_Article', '=', $data['LG_INV_Code_Article']], ['LG_INV_Code_Inv', '=', $data['LG_INV_Code_Inv']]]);

                    if ($insert1) {
                        $result[] = $data;
                    } else {
                        $result[] = null;
                    }


                } elseif ($threeSpace) {
                    $data['LG_INV_Code_Article'] = "   " . $data['LG_INV_Code_Article'];
                    $insert1 = $this->updateOrCreate($connection, $data, 'Ligne_Inventaire', [['LG_INV_Code_Article', '=', $data['LG_INV_Code_Article']], ['LG_INV_Code_Inv', '=', $data['LG_INV_Code_Inv']]]);

                    if ($insert1) {
                        $result[] = $data;
                    } else {
                        $result[] = null;
                    }


                } else {
                    $test = $connection->select("select ART_Code from article where ART_Code = '" . trim($codeone) . "'");
                    if ($test) {

                        $insert2 = $this->updateOrCreate($connection, $data, 'Ligne_Inventaire', [['LG_INV_Code_Article', '=', $data['LG_INV_Code_Article']], ['LG_INV_Code_Inv', '=', $data['LG_INV_Code_Inv']]]);

                        if ($insert2) {
                            $result[] = $data;
                        } else {
                            $result[] = null;
                        }

                    }
                }


            }


        } else {
            return response()->json(null);
        }

        return response()->json($result);


    }

    public function addBatchLigneInventairesData($connection, $items)
    {
        if (!empty ($items)) {
            //	$data['object']['CLI_Code'] =(new PrefixWSMobile)->getPrefix($data,"Client");
            $result = null;
            foreach ($items as $data) {
                $data["LG_INV_Qte_Stock"] = floatval($data["LG_INV_Qte_Stock"]) . "";
                $data["LG_INV_Qte_Reel"] = floatval($data["LG_INV_Qte_Reel"]) . "";
                $data["LG_INV_Prix_CMP"] = floatval($data["LG_INV_Prix_CMP"]) . "";
                $codeone = " " . $data['LG_INV_Code_Article'];
                $codetwo = "  " . $data['LG_INV_Code_Article'];
                $codethree = "   " . $data['LG_INV_Code_Article'];
                $oneSpace = $connection->select("select ART_Code from article where ART_Code = '" . $codeone . "'");
                $twoSpace = $connection->select("select ART_Code from article where ART_Code = '" . $codetwo . "'");
                $threeSpace = $connection->select("select ART_Code from article where ART_Code = '" . $codethree . "'");
                if ($oneSpace) {
                    $data['LG_INV_Code_Article'] = " " . $data['LG_INV_Code_Article'];
                    $insert1 = $this->updateOrCreate($connection, $data, 'Ligne_Inventaire', [['LG_INV_Code_Article', '=', $data['LG_INV_Code_Article']], ['LG_INV_Code_Inv', '=', $data['LG_INV_Code_Inv']]]);

                    if ($insert1) {
                        $result[] = $data;
                    } else {
                        $result[] = null;
                    }

                } elseif ($twoSpace) {
                    $data['LG_INV_Code_Article'] = "  " . $data['LG_INV_Code_Article'];
                    $insert1 = $this->updateOrCreate($connection, $data, 'Ligne_Inventaire', [['LG_INV_Code_Article', '=', $data['LG_INV_Code_Article']], ['LG_INV_Code_Inv', '=', $data['LG_INV_Code_Inv']]]);

                    if ($insert1) {
                        $result[] = $data;
                    } else {
                        $result[] = null;
                    }


                } elseif ($threeSpace) {
                    $data['LG_INV_Code_Article'] = "   " . $data['LG_INV_Code_Article'];
                    $insert1 = $this->updateOrCreate($connection, $data, 'Ligne_Inventaire', [['LG_INV_Code_Article', '=', $data['LG_INV_Code_Article']], ['LG_INV_Code_Inv', '=', $data['LG_INV_Code_Inv']]]);

                    if ($insert1) {
                        $result[] = $data;
                    } else {
                        $result[] = null;
                    }


                } else {
                    $test = $connection->select("select ART_Code from article where ART_Code = '" . trim($codeone) . "'");
                    if ($test) {

                        $insert2 = $this->updateOrCreate($connection, $data, 'Ligne_Inventaire', [['LG_INV_Code_Article', '=', $data['LG_INV_Code_Article']], ['LG_INV_Code_Inv', '=', $data['LG_INV_Code_Inv']]]);

                        if ($insert2) {
                            $result[] = $data;
                        } else {
                            $result[] = null;
                        }

                    }
                }


            }


        } else {
            return response()->json(null);
        }

        return $result;


    }


    public function updateOrCreate($connection, $data, $table, $whereClauses)
    {
        $exists = $connection->table($table)->where($whereClauses)->first();

        if ($exists) {
            $connection->table($table)->where($whereClauses)->delete();
        }


        return $connection->table($table)->insert($data);


    }

}





