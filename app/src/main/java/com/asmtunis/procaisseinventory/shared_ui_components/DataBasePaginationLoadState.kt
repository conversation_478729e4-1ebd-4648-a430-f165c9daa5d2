package com.asmtunis.procaisseinventory.shared_ui_components

import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.paging.LoadState
import androidx.paging.compose.LazyPagingItems


@Composable
fun <T : Any> DataBasePaginationLoadState (lazyPagingItems: LazyPagingItems<T>?) {

    lazyPagingItems?.apply {
        when {
            loadState.refresh is LoadState.Loading -> {
                // Show a loading indicator
                CircularProgressIndicator()
            }
            loadState.append is LoadState.Loading -> {
                //Show loading next page indicator
                // CircularProgressIndicator()
            }
            loadState.refresh is LoadState.Error -> {
                //Show a Error
                val e = loadState.refresh as LoadState.Error

                Text(
                    text = e.error.localizedMessage!!,
                    modifier = Modifier.padding(8.dp)
                )


            }
            loadState.append is LoadState.Error -> {
                //Show a Error
                val e = loadState.append as LoadState.Error

                Text(
                    text = e.error.localizedMessage!!,
                    modifier = Modifier.padding(8.dp)
                )

            }
        }
    }
}