package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.screens

import android.Manifest
import android.app.Activity
import android.util.Log
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.outlined.Add
import androidx.compose.material.icons.twotone.Edit
import androidx.compose.material.icons.twotone.Save
import androidx.compose.material3.Button
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.asmtunis.countrycodepicker.data.utils.getNumberHint
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.connectivity.location.CheckLocationSetting
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationViewModule
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.DigitalDistributionRoute
import com.asmtunis.procaisseinventory.core.navigation.FamilleProduitDetailRoute
import com.asmtunis.procaisseinventory.nav_components.ID
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.SuperficieDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypePointVenteDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypeServicesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.text_validation.AddVisiteFormEvent
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.text_validation.ValidationAddVisiteEvent
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.text_validation.VisiteTextValidationViewModel
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.view_model.DistributionNumeriqueViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.AskPermission
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.PhoneCountryPicker
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.simapps.ui_kit.dialogues.CustomAlertDialogue
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu
import com.simapps.ui_kit.edit_text.EditTextField

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun AddModifyVisiteSreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    clientId: String,
    settingViewModel: SettingViewModel,
    distNumViewModel: DistributionNumeriqueViewModel,
    mainViewModel: MainViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    visiteTextValidationViewModel: VisiteTextValidationViewModel = hiltViewModel(),
    locationViewModule: LocationViewModule
) {
    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val modifyVisite = distNumViewModel.modifyVisite
    val validationAddVisiteEvents = visiteTextValidationViewModel.validationAddVisiteEvents

    val stateAddVisite = visiteTextValidationViewModel.stateAddVisite
    val location = locationViewModule.locationState
    val context = LocalContext.current

    val clientList = mainViewModel.clientList
    val clientByCode = clientList.firstOrNull { it.cLICode == clientId }?: Client()//mainViewModel.clientByCode
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val settingResultRequest = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartIntentSenderForResult()
    ) { activityResult ->
        if (activityResult.resultCode == Activity.RESULT_OK) {
            Log.d("appDebug", "Accepted")
        } else {
            Log.d("appDebug", "Denied")
        }
    }
    // val state = rememberScrollState()
    // LaunchedEffect(Unit) { state.animateScrollTo(100) } // Smoothly scroll 100px on first composition

    val scrollState = rememberScrollState()


    val visitesDn = distNumViewModel.selectedVisite
    val ligneVisitesDn = distNumViewModel.selectedVisiteLines



   val utilisateur = mainViewModel.utilisateur


    val superficieList = distNumViewModel.superficieList
    val typePtVenteList = distNumViewModel.typePtVenteList
    val typeServiceList = distNumViewModel.typeServiceList
    val typePtVenteExpanded = distNumViewModel.typePtVenteExpanded

    val visCodeM = visitesDn.vIS_Code_M

    val codeM = visCodeM.ifEmpty { mainViewModel.codeM }


    
    BackHandler(enabled = true) {
       if(modifyVisite) distNumViewModel.onShowAlertDialogChange(true)
        else popBackStack()
    }

    LaunchedEffect(key1 = Unit) {
         visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.LigneVisitesDnChanged(ligneVisitesDn))

        val gouvernoratEvent = AddVisiteFormEvent.GouvernoratChanged(stateAddVisite.gouvernorat.ifEmpty { visitesDn.vIS_Gouvernorat?.ifEmpty { clientByCode.cltGouvernorat?: "" }?: "" } )
        visiteTextValidationViewModel.onAddVisiteEvent(gouvernoratEvent)

        val nomMagasinEvent = AddVisiteFormEvent.NomMagasinChanged(stateAddVisite.nomMagasin.ifEmpty { visitesDn.vIS_NomMagazin?.ifEmpty { clientByCode.cLtNomMagasin?: "" }?: "" } )
        visiteTextValidationViewModel.onAddVisiteEvent(nomMagasinEvent)



        visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.NomGerantChanged(stateAddVisite.nomGerant.ifEmpty { visitesDn.vIS_NomGerant }))


        val delegationEvent = AddVisiteFormEvent.DelegationChanged(stateAddVisite.delegation.ifEmpty { visitesDn.vIS_Delegations?.ifEmpty { clientByCode.cltVille?: "" }?: "" } )
        visiteTextValidationViewModel.onAddVisiteEvent(delegationEvent)





        val numTeleEvent = AddVisiteFormEvent.PhoneNumber1Changed(stateAddVisite.phone1.ifEmpty { visitesDn.vIS_NumTele?.ifEmpty { clientByCode.cLITel1?: clientByCode.cLITel2?: "" }?: "" } )
        visiteTextValidationViewModel.onAddVisiteEvent(numTeleEvent)

        val latitudeEvent = AddVisiteFormEvent.LatitudeChanged(stateAddVisite.latitude.ifEmpty { visitesDn.vIS_Latitude?.ifEmpty { clientByCode.cltLatitude?.toString()?: "" }?: "" } )
        visiteTextValidationViewModel.onAddVisiteEvent(latitudeEvent)



        val longitudeEvent = AddVisiteFormEvent.LongitudeChanged(stateAddVisite.longitude.ifEmpty { visitesDn.vIS_Longitude?.ifEmpty { clientByCode.cltLongitude?.toString()?: "" }?: "" } )
        visiteTextValidationViewModel.onAddVisiteEvent(longitudeEvent)


        val adresseEvent = AddVisiteFormEvent.AdresseChanged(stateAddVisite.adresse.ifEmpty { visitesDn.vIS_Adresse?.ifEmpty { clientByCode.cLIAdresse?: "" }?: "" } )
        visiteTextValidationViewModel.onAddVisiteEvent(adresseEvent)



        if (visitesDn.vIS_Superf != "") {
            visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.SuperficieChanged(
                if(stateAddVisite.superficie != SuperficieDn())
                    stateAddVisite.superficie
                    else
                superficieList.firstOrNull { it.codeSuperf == visitesDn.vIS_Superf }?: SuperficieDn()))
        }

        if (visitesDn.vIS_TypeServ != "") {
            visiteTextValidationViewModel.onAddVisiteEvent(
                AddVisiteFormEvent.TypeServiceChanged(
                    if(stateAddVisite.typeService != TypeServicesDn())
                        stateAddVisite.typeService
                        else
                    typeServiceList.firstOrNull { it.codeTypeSv == visitesDn.vIS_TypeServ }
                        ?: TypeServicesDn()
                )
            )
        }
        if (visitesDn.vIS_TypePV != "") {
            visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.TypePtVenteChanged(
                if(stateAddVisite.typePtVente != TypePointVenteDn())
                    stateAddVisite.typePtVente
                    else
                typePtVenteList.firstOrNull { it.codeTypePV == visitesDn.vIS_TypePV }?: TypePointVenteDn()
            ))
        }



    }



    LaunchedEffect(validationAddVisiteEvents) {
        if(validationAddVisiteEvents is ValidationAddVisiteEvent.AddVisite) {

            showToast(
                context = context,
                toaster = toaster,
                message = context.resources.getString(R.string.sucess_add_visit),
                type =  ToastType.Success,
            )
        }

       distNumViewModel.handleAddVisiteEvents(
           codeM = codeM,
            popBackStack = {
                mainViewModel.resetClientByCode()
                visiteTextValidationViewModel.restStateAddVisite()
                NavigationDrawerViewModel.proCaisseDrawerItems.find { it.id == ID.DISTRIBUTION_NUMERIQUE_ID }?.let { navigationDrawerViewModel.onSelectedMenuChange(it) }


                navigate(DigitalDistributionRoute())
                           },
            exerciceList = mainViewModel.exerciceList,
           client = clientByCode,
            utilisateur = utilisateur,
            visitesDn = visitesDn,
           validationAddVisiteEvents = validationAddVisiteEvents
        )
    }





    LaunchedEffect(key1 = location) {
        if(location.latitude != null && location.longitude != null){
            visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.LongitudeChanged(location.longitude.toString()))
            visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.LatitudeChanged(location.latitude.toString()))

        }


        if (location.adresse != null) {
            visiteTextValidationViewModel.onAddVisiteEvent(
                AddVisiteFormEvent.AdresseChanged(
                    "${location.adresse.knownName}," +
                        " ${location.adresse.city} ${location.adresse.state}, " +
                            location.adresse.country
                )
            )
            visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.DelegationChanged(location.adresse.city))
            visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.GouvernoratChanged(location.adresse.state))
        }


    }

    val title = if (visitesDn.status == ItemStatus.DELETED.status) {
        stringResource(id = R.string.visite_supprime, visitesDn.vIS_Num)
    } else {
        if (visitesDn.vIS_Num.isNotEmpty()) {
            if (!modifyVisite) {
                visitesDn.vIS_Num
            } else stringResource(id = R.string.modification, visitesDn.vIS_Num)
        } else stringResource(id = R.string.new_visite_for, clientByCode.cLINomPren)
    }

    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = {
                    if(modifyVisite) distNumViewModel.onShowAlertDialogChange(true)
                    else popBackStack()
                                    },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = title,
            )
        },
        //    containerColor = colorResource(id = R.color.black),
        floatingActionButton = {
            FloatingActionButton(
                onClick = {
                if (visitesDn.status == ItemStatus.DELETED.status) {
                    showToast(
                        context = context,
                        toaster = toaster,
                        message = context.resources.getString(R.string.visite_deleted),
                        type = ToastType.Success,
                    )
                    return@FloatingActionButton
                }

                    if (!modifyVisite) {
                        distNumViewModel.onModifyVisiteChange(true)
                    } else {
                        visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.SubmitAddVisite)
                    }


            }) {
                Icon(
                    imageVector = if (modifyVisite) Icons.TwoTone.Save else Icons.TwoTone.Edit,
                    contentDescription = stringResource(id = R.string.cd_addVisite_button)
                )
            }
        }
    ) { padding ->

        CustomAlertDialogue(
            title = stringResource(id = R.string.did_you_like_quit),
            msg = "",
            openDialog = distNumViewModel.showAlertDialog,
            setDialogueVisibility = {
                mainViewModel.onShowAlertDialogChange(it)
            },
            customAction = {
               visiteTextValidationViewModel.restStateAddVisite()
                distNumViewModel.onShowAlertDialogChange(false)
                popBackStack()
            },

            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non),
            negatifAction  = { distNumViewModel.onShowAlertDialogChange(false) }
        )

        LazyColumn(
            modifier = Modifier
                .padding(padding)
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            item {
                Column(
                    modifier = Modifier
                        .wrapContentSize(Alignment.Center)
                        .fillMaxWidth(),
                    verticalArrangement = Arrangement.Top,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    EditTextField(
                        text = stateAddVisite.nomGerant,
                        errorValue = stateAddVisite.nomGerantError?.asString(),
                        label = stringResource(R.string.nom_gerant),
                        onValueChange = { visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.NomGerantChanged(it)) },
                        readOnly = !modifyVisite,
                        enabled = true,
                        leadingIcon = Icons.Default.Home,
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    )

                    EditTextField(
                        text = stateAddVisite.nomMagasin,
                        errorValue = stateAddVisite.nomMagasinError?.asString(),
                        label = stringResource(R.string.Nom_magasin),
                        onValueChange = {
                            visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.NomMagasinChanged(it))
                        },
                        readOnly = !modifyVisite,
                        enabled = true,
                        leadingIcon = Icons.Default.Person,
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    )
                    EditTextField(
                        text = stateAddVisite.gouvernorat,
                        errorValue = stateAddVisite.gouvernoratError?.asString(),
                        label = stringResource(R.string.gouvernorats),
                        onValueChange = {
                            visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.GouvernoratChanged(it))
                        },
                        readOnly = !modifyVisite,
                        enabled = true,
                        leadingIcon = Icons.Default.Home,
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    )

                    EditTextField(
                        text = stateAddVisite.delegation,
                        errorValue = stateAddVisite.delegationError?.asString(),
                        label = stringResource(R.string.delegations),
                        onValueChange = {
                            visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.DelegationChanged(it))
                        },
                        readOnly = !modifyVisite,
                        enabled = true,
                        leadingIcon = Icons.Default.Home,
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    )

                    PhoneCountryPicker(
                        // .weight(1f),
                        enabled = modifyVisite,
                        countryCode = visiteTextValidationViewModel.stateAddVisite.countryData.countryCode.uppercase(),
                        errorValue = visiteTextValidationViewModel.stateAddVisite.phoneError,
                        onCountryChange = {
                            visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.CountryDataChanged(it))
                        },
                        stringId = R.string.phone1_field_title,
                        value = stateAddVisite.phone1,
                        onValueChange = { visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.PhoneNumber1Changed(it)) },
                        hint = getNumberHint(visiteTextValidationViewModel.stateAddVisite.countryData.countryCode),
                    )



                    GenericDropdownMenu (
                        modifier = Modifier.fillMaxWidth(0.95f),
                        label = stringResource(R.string.typepointdeVente),
                        designation = stateAddVisite.typePtVente.typePV,
                        readOnly = modifyVisite,
                        errorValue = stateAddVisite.typePtVenteError?.asString(),
                        itemList = typePtVenteList,
                        itemExpanded = distNumViewModel.typePtVenteExpanded,
                        selectedItem = stateAddVisite.typePtVente,
                        getItemDesignation = { it.typePV },
                        onClick = {
                            visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.TypePtVenteChanged(it))
                            distNumViewModel.onTypePtVenteExpandedChange(false)
                        },
                        onItemExpandedChange = { if (modifyVisite)  distNumViewModel.onTypePtVenteExpandedChange(it) },
                        lottieAnimEmpty = { LottieAnim(lotti = R.raw.emptystate) },
                        lottieAnimError = { LottieAnim(lotti = R.raw.connection_error, size = it) }
                    )


                    GenericDropdownMenu (
                        modifier = Modifier.fillMaxWidth(0.95f),
                        label = stringResource(R.string.type_service),
                        designation = stateAddVisite.typeService.typeSv,
                        errorValue = stateAddVisite.superficieError?.asString(),
                        itemList = typeServiceList,
                        readOnly = modifyVisite,
                        itemExpanded = distNumViewModel.typeServiceExpanded,
                        selectedItem = stateAddVisite.typeService,
                        getItemDesignation = {
                            it.typeSv
                        },
                        onClick = {
                            visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.TypeServiceChanged(it))
                            distNumViewModel.ontypeServiceExpandedChange(false)
                        },
                        onItemExpandedChange = {
                            if (modifyVisite)  distNumViewModel.ontypeServiceExpandedChange(it)
                        },
                        lottieAnimEmpty = {
                            LottieAnim(lotti = R.raw.emptystate)
                        },
                        lottieAnimError = {
                            LottieAnim(lotti = R.raw.connection_error, size = it)
                        }
                    )



                    GenericDropdownMenu (
                        modifier = Modifier.fillMaxWidth(0.95f),
                        label = stringResource(R.string.superficie),
                        designation = stateAddVisite.superficie.typeSuperf,
                        errorValue = stateAddVisite.superficieError?.asString(),
                        itemList = superficieList,
                        readOnly = modifyVisite,
                        itemExpanded = distNumViewModel.superficieExpanded,
                        selectedItem = stateAddVisite.superficie,
                        getItemDesignation = {
                            it.typeSuperf
                        },
                        onClick = {
                            visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.SuperficieChanged(it))
                            distNumViewModel.onSuperficieExpandedChange(false)
                        },
                        onItemExpandedChange = {
                            if (modifyVisite)  distNumViewModel.onSuperficieExpandedChange(it)
                        },
                        lottieAnimEmpty = {
                            LottieAnim(lotti = R.raw.emptystate)
                        },
                        lottieAnimError = {
                            LottieAnim(lotti = R.raw.connection_error, size = it)
                        }
                    )






                    EditTextField(
                        text = stateAddVisite.adresse,
                        errorValue = stateAddVisite.adresseError?.asString(),
                        label = stringResource(R.string.adresse_field_title),
                        onValueChange = {
                            visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.AdresseChanged(it))
                        },
                        readOnly = !modifyVisite,
                        enabled = true,
                        leadingIcon = Icons.Default.LocationOn,
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    )

                    AskPermission(
                        permission = listOf(
                            Manifest.permission.ACCESS_COARSE_LOCATION,
                            Manifest.permission.ACCESS_FINE_LOCATION
                        ),
                        permissionNotAvailableContent = { permissionState ->
                            Column(
                                modifier = Modifier
                                    .fillMaxSize(),
                                verticalArrangement = Arrangement.Center,
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                LottieAnim(lotti = R.raw.emptystate)
                                Spacer(modifier = Modifier.height(16.dp))

                                val textToShow = if (permissionState.shouldShowRationale) {
                                    stringResource(R.string.access_gps_request_permession)
                                } else {
                                    stringResource(R.string.gps_not_available)
                                }
                                Spacer(modifier = Modifier.height(16.dp))
                                Text(textToShow)
                                Spacer(modifier = Modifier.height(8.dp))
                                Button(onClick = { permissionState.launchMultiplePermissionRequest() }) {
                                    Text(stringResource(R.string.request_gps_auth))
                                }
                            }
                        },
                        content = {
                            Row(
                                modifier = Modifier
                                    .height(66.dp)
                                    .fillMaxWidth(0.95f),
                                horizontalArrangement = Arrangement.spacedBy(8.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                EditTextField(
                                    modifier = Modifier.fillMaxWidth(0.45f),
                                    text = stateAddVisite.longitude,
                                    errorValue = stateAddVisite.longitudeError?.asString(),
                                    label = stringResource(R.string.Longitude),
                                    onValueChange = {
                                        visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.LongitudeChanged(it))
                                    },
                                    readOnly = true,
                                    enabled = true,
                                    leadingIcon = Icons.Default.LocationOn,
                                    keyboardType = KeyboardType.Number,
                                    imeAction = ImeAction.Next
                                )

                                EditTextField(
                                    modifier = Modifier.fillMaxWidth(),
                                    text = stateAddVisite.latitude,
                                    errorValue = stateAddVisite.latitudeError?.asString(),
                                    label = stringResource(R.string.latitude),
                                    onValueChange = {
                                        visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.LatitudeChanged(it))
                                    },
                                    readOnly = true,
                                    enabled = true,
                                    leadingIcon = Icons.Default.LocationOn,
                                    keyboardType = KeyboardType.Number,
                                    imeAction = ImeAction.Done
                                )
                            }
                            locationViewModule.locationState.error?.let { error ->
                                Text(
                                    text = error.asString()?: "Unknown GPS error",
                                    color = Color.Red,
                                    textAlign = TextAlign.Center
                                )
                            }
                            if (modifyVisite) LottieAnim(
                                lotti = if (location.isLoading) R.raw.loading else R.raw.location_pin,
                                size = 30.dp,
                                onClick = {
                                    CheckLocationSetting.checkLocationSetting(
                                        context = context,
                                        onDisabled = { intentSenderRequest ->
                                            settingResultRequest.launch(intentSenderRequest)
                                        },
                                        onEnabled = { locationViewModule.getCurrentLocation() }
                                    )
                                }
                            )
                        }
                    )
                }
            }

            item {
                Spacer(modifier = Modifier.height(16.dp))
                if(stateAddVisite.ligneVisitesDnError != null && ligneVisitesDn.isEmpty()) {
                    Text(
                        modifier = Modifier.fillMaxWidth(),
                        //  text = "Famille Produits",
                        text = stringResource(R.string.famille_produit_list_empty),
                        color = MaterialTheme.colorScheme.error,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                }


                OutlinedButton(
                    onClick = { navigate(FamilleProduitDetailRoute) }) {
                    AnimatedVisibility(
                        visible = modifyVisite,
                        enter = fadeIn() + slideInVertically(),
                        exit = fadeOut() + slideOutVertically()
                    ) {
                        Icon(
                            imageVector = Icons.Outlined.Add,
                            contentDescription = stringResource(id = R.string.cd_addVisite_button)
                        )
                        Spacer(modifier = Modifier.width(9.dp))
                    }
                    Text(
                        text = (stateAddVisite.familleProduitError?.asString(context) ?: stringResource(R.string.famille_produit)) + " ("+ ligneVisitesDn.size+ ")",
                        color = if (stateAddVisite.familleProduitError != null) MaterialTheme.colorScheme.error else  MaterialTheme.colorScheme.primary,
                        textAlign = TextAlign.Center
                    )
                }



                Spacer(modifier = Modifier.height(30.dp))
            }
        }
    }
}




















