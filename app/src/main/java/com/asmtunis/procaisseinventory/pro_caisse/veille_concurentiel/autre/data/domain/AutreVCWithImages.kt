package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class AutreVCWithImages (
    @Embedded
    @SerialName("AutreVC")
    var autreVC: AutreVC? = null,

    @Relation(
        parentColumn = "Code_Mob",
        entityColumn = "Code_TypeVC"
    )
    @SerialName("ImagePieceJoint")
    var imageList: List<ImagePieceJoint>? = null,
)