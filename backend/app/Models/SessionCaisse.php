<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;


class SessionCaisse extends Model
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'SessionCaisse';

    /**
     * The primary key for the model.
     * 
     * @var string
     */
    protected $primaryKey = 'SC_IdSCaisse';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['SC_Caisse', 'SC_CodeUtilisateur', 'SC_DateHeureOuv', 'SC_ClotCaisse', 'SC_DateHeureClot', 'SC_FondCaisse', 'SC_TotTVA', 'SC_TotRemise', 'SC_BenificeTVA', 'SC_BenificeSansTVA', 'SC_TotBenifice', 'SC_TotalRecette', 'SC_TotalCaisse', 'SC_IdCarnet', 'SC_Etat', 'SC_Facturer', 'SC_NumFact', 'SC_Station', 'SC_User', 'SC_DateHeureCrea', 'SC_TotDepense', 'SC_export', 'SC_DDm', 'SC_TotBenificeRes', 'SC_TotalRecetteManuel', 'ddm', 'export'];

    /**
     * @return BelongsTo
     */
    public function utilisateur()
    {
        return $this->belongsTo('App\Models\Utilisateur', 'SC_CodeUtilisateur', 'Code_Ut');
    }

    /**
     * @return BelongsTo
     */
    public function carnet()
    {
        return $this->belongsTo('App\Carnet', 'SC_IdCarnet', 'CAR_IdCarnet');
    }

    /**
     * @return BelongsTo
     */
    public function caisse()
    {
        return $this->belongsTo('App\Models\Caisse', 'SC_Caisse', 'CAI_IdCaisse');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function monnaies()
    {
        return $this->hasMany('App\Monnaie', 'SessionCaisse', 'SC_IdSCaisse');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function tickets()
    {
        return $this->hasMany('App\Models\Ticket', 'TIK_IdSCaisse', 'SC_IdSCaisse');
    }
}
