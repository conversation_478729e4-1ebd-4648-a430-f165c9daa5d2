package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.inventaire

import android.util.Log
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.AccountBalance
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.Scaffold
import androidx.compose.material3.windowsizeclass.WindowWidthSizeClass
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.domaine.SelectedPatrimoine
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AjoutAffectationBatimentRoute
import com.asmtunis.procaisseinventory.core.navigation.InventaireBatimentRoute
import com.asmtunis.procaisseinventory.core.navigation.MainImageTiketRoute
import com.asmtunis.procaisseinventory.core.navigation.ZoneConsomationDetailRoute
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.nav_components.ID.INVENTAIRE_BATIMENT_ID
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel.Companion.proCaisseDrawerItems
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.Constants
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.SetNumSerieView
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePatrimoine
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.Constant.SOCIETE
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.shared_ui.ColumnView
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.shared_ui.ItemDetailData
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.shared_ui.RowView
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.ControleInventaire
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.updateInvPatQty
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.CameraViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.floatingActionButtonPosition
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.dialogues.CustomAlertDialogue
import java.util.*

@Composable
fun AddInventaireImmoScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    navDrawerViewModel: NavigationDrawerViewModel,
    mainViewModel: MainViewModel,
    barCodeViewModel: BarCodeViewModel,
    cameraViewModel: CameraViewModel,
    proCaisseViewModels: ProCaisseViewModels,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    settingViewModel: SettingViewModel,
    autoOpenDialog: Boolean = false,
    preFilledNumSerie: String = ""
) {
    val batimentViewModel = proCaisseViewModels.batimentViewModel
    val invPatViewModel = proCaisseViewModels.invPatViewModel
    val selectPatrimoineVM = proCaisseViewModels.selectPatrimoineVM
    val uiWindowState = settingViewModel.uiWindowState
    val windowSize = uiWindowState.windowSize!!
    val context = LocalContext.current
    val showFilterLine = selectPatrimoineVM.showFilterLine
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val fiterValue = selectPatrimoineVM.fiterValue
    val selectedPatrimoineList = selectPatrimoineVM.selectedPatrimoineList
    val selectedPatrimoine = selectPatrimoineVM.selectedPatrimoine
    val marqueList = mainViewModel.marqueList
    val invPatByNumSerie = selectPatrimoineVM.invPatByNumSerie
    val selectedZoneConsomation = batimentViewModel.selectedZoneConsomation
    val codeM = mainViewModel.codeM
    val density = LocalDensity.current
    val exerciceList = mainViewModel.exerciceList
    val patrimoineVerificationState = selectPatrimoineVM.patrimoineVerificationState
    val utilisateur = mainViewModel.utilisateur
    val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val selectedInvPatrimoine = invPatViewModel.selectedInvPatrimoine
    val listLigneBonCommande = invPatViewModel.selectedListLgInvPatrimoine

    val showAlertDialog = invPatViewModel.showAlertDialog

    val openVerticalalImagePagerDialog = cameraViewModel.openVerticalalImagePagerDialog

    val listImgeUri = cameraViewModel.listImgeUri
    val haveCamera = dataViewModel.getHaveCameraDevice()

    val currentImageUri = cameraViewModel.currentImageUri

    val isUpdate = selectedInvPatrimoine.status == ItemStatus.WAITING.status

    val imageList = mainViewModel.imageList
    val barCodeInfo = barCodeViewModel.barCodeInfo

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)
    LaunchedEffect(key1 = listImgeUri.size) {
        if (listImgeUri.isEmpty()) return@LaunchedEffect
        if (selectedPatrimoine.numSerie.isEmpty()) return@LaunchedEffect

        if (selectedPatrimoineList.firstOrNull {
                it.numSerie == selectedPatrimoine.numSerie
            }?.imageList?.any { it.imgUrl == currentImageUri.toString() } != false
        ) {
            return@LaunchedEffect
        }

        val combinedImageSet = mutableSetOf<ImagePieceJoint>()

        combinedImageSet.addAll(listImgeUri.filter { it.vcNumSerie == selectedPatrimoine.numSerie },)

        combinedImageSet.addAll(selectedPatrimoine.imageList)

        selectPatrimoineVM.addItemToSelectedPatrimoineList(selectedPatrimoine.copy(imageList = combinedImageSet.toList()))
    }

    // Auto-open dialog when returning from affectation
    LaunchedEffect(key1 = autoOpenDialog, key2 = preFilledNumSerie) {
        if (autoOpenDialog) {
            Log.d("AddInventaireImmo", "Auto-opening dialog with preFilledNumSerie: '$preFilledNumSerie'")
            selectPatrimoineVM.setSelectedPat(SelectedPatrimoine(numSerie = preFilledNumSerie))
            selectPatrimoineVM.onBackUpSelectedPatrimoineChange(SelectedPatrimoine())
            selectPatrimoineVM.resetPatrimoineVerificationState()
            selectPatrimoineVM.onShowSetNumeSerieChange(true)
        }
    }

    BackHandler(true) {
        mainViewModel.onShowDismissScreenAlertDialogChange(true)
    }

    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = {
                    mainViewModel.onShowDismissScreenAlertDialogChange(true)
                },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = codeM,
            )
        },
        floatingActionButtonPosition = floatingActionButtonPosition(windowSize = windowSize),
        floatingActionButton = {
            Column {
                FloatingActionButton(
                    onClick = {
                        selectPatrimoineVM.setSelectedPat(SelectedPatrimoine())
                        selectPatrimoineVM.onBackUpSelectedPatrimoineChange(SelectedPatrimoine())
                        selectPatrimoineVM.resetPatrimoineVerificationState()
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    },
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = stringResource(id = R.string.add),
                    )
                }
                Spacer(modifier = Modifier.height(12.dp))
                AnimatedVisibility(
                    visible = selectedPatrimoineList.isNotEmpty(),
                    enter =
                        slideInVertically {
                            with(density) { 40.dp.roundToPx() }
                        } + fadeIn(),
                    exit =
                        fadeOut(
                            animationSpec =
                                keyframes {
                                    this.durationMillis = 120
                                },
                        ),
                ) {
                    FloatingActionButton(
                        onClick = { mainViewModel.onShowAlertDialogChange(true) },
                    ) {
                        Icon(
                            imageVector = Icons.Default.Save,
                            contentDescription = stringResource(id = R.string.add),
                        )
                    }
                }
            }
        },
    ) { padding ->
        CustomAlertDialogue(
            title = context.getString(R.string.info),
            msg = context.getString(R.string.confirm_close_screen),
            openDialog = mainViewModel.showDismissScreenAlertDialog,
            setDialogueVisibility = {
                mainViewModel.onShowDismissScreenAlertDialogChange(it)
            },
            customAction = {
                //   navigate(ZoneConsomationDetailRoute)
                navigateTo(
                    navigate = { navigate(it) },
                    isUpdate = isUpdate,
                    navDrawerViewModel = navDrawerViewModel
                )

            },
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non)

        )
        CustomAlertDialogue(
            title = context.getString(R.string.delete),
            msg = context.getString(R.string.confirm_remove_ligne_data),
            openDialog = showAlertDialog.first,
            setDialogueVisibility = {
                invPatViewModel.onShowAlertDialogChange(Pair(it, SelectedPatrimoine()))
            },
            customAction = {
                selectPatrimoineVM.deleteItemToSelectedPatrimoineList(
                    value = showAlertDialog.second,
                    isUpdate = isUpdate,
                    selectedInvPatrimoine = selectedInvPatrimoine
                )

            },
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non)
        )

        CustomAlertDialogue(
            title = stringResource(id = R.string.voulez_vous),
            msg = "",
            openDialog = mainViewModel.showAlertDialog,
            setDialogueVisibility = {
                mainViewModel.onShowAlertDialogChange(it)
            },
            customAction = {
                val invStationOrigineIsFromUtil =
                    dataViewModel.getInventaireStationOrigineIsFromUtilisateur()
                val societe =
                    batimentViewModel.immobilisationTreeList.firstOrNull { it.tyEmpImNom == SOCIETE }


                val station = mainViewModel.stationList.firstOrNull {
                    it.sTATDesg.lowercase(Locale.ROOT) == societe?.cLINomPren?.lowercase(Locale.ROOT)
                }

                invPatViewModel.saveInvPat(
                    articleMapByBarCode = articleMapByBarCode,
                    idStationOrigine = if (invStationOrigineIsFromUtil) utilisateur.Station else station?.sTATCode?: "",
                    deleteLgAndImages = isUpdate,
                    status = ItemStatus.INSERTED.status,
                    codeM = codeM,
                    listSelectedPatrimoine = selectedPatrimoineList,
                    exercice = exerciceList.first().exerciceCode,
                    utilisateur = utilisateur,
                    typeInv = TypePatrimoine.INVENTAIRE.typePat,
                    devEtat = Constants.IMMOBILISATION,
                    selectedZoneConsomation = selectedZoneConsomation,
                    onComplete = {
                        navigateTo(
                            navigate = { navigate(it) },
                            isUpdate = isUpdate,
                            navDrawerViewModel = navDrawerViewModel
                        )
                    }
                )
                //  navigateUp()
                //  popBackStack()
            },

            confirmText = stringResource(id = R.string.save_and_sync),
            cancelText = stringResource(id = R.string.save),
            negatifAction = {
                invPatViewModel.saveInvPat(
                    articleMapByBarCode = articleMapByBarCode,
                    deleteLgAndImages = isUpdate,
                    status = ItemStatus.WAITING.status,
                    codeM = codeM,
                    listSelectedPatrimoine = selectedPatrimoineList,
                    exercice = exerciceList.first().exerciceCode,
                    utilisateur = utilisateur,
                    typeInv = TypePatrimoine.INVENTAIRE.typePat,
                    devEtat = Constants.IMMOBILISATION,
                    selectedZoneConsomation = selectedZoneConsomation,
                    onComplete = {
                        navigateTo(
                            navigate = { navigate(it) },
                            isUpdate = isUpdate,
                            navDrawerViewModel = navDrawerViewModel
                        )
                    }
                )
                //  navigateUp()
                //    popBackStack()

            }
        )

        if (selectPatrimoineVM.showSetNumeSerie) {
            SetNumSerieView(
                marqueList = marqueList,
                articleMapByBarCode = articleMapByBarCode,
                haveCamera = haveCamera,
                selectedPatrimoine = selectedPatrimoine,
                selectedPatrimoineList = selectedPatrimoineList,
                barCodeInfo = barCodeInfo,
                patrimoineVerificationState = patrimoineVerificationState,
                showDropDownMenuComposable = false,
                onNumSerieChange = {
                    selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(numSerie = it))
                },
                onDismiss = {
                    barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                    selectPatrimoineVM.resetPatrimoineVerificationState()
                    selectPatrimoineVM.setSelectedPat(SelectedPatrimoine())
                    selectPatrimoineVM.onBackUpSelectedPatrimoineChange(SelectedPatrimoine())
                    selectPatrimoineVM.onShowSetNumeSerieChange(false)
                },
                onConfirm = {

                    val controlInvPat =
                        ControleInventaire(
                            LG_DEV_NumSerie = selectedPatrimoine.numSerie,
                            DEV_CodeClient = selectedZoneConsomation.cLICode,
                            DEV_info3 = TypePatrimoine.INVENTAIRE.typePat,
                        )
                    selectPatrimoineVM.patrimoineVerification(
                        baseConfig = mainViewModel.selectedBaseconfig,
                        controlPatrimoine = controlInvPat,
                    )
                },
                onAddInvPat = {
                    val codeArt =
                        if (invPatByNumSerie.isNotEmpty()) {
                            invPatByNumSerie.entries.first().value.first().lGDEVCodeArt
                        } else {
                            "N/A"
                        }

                    updateInvPatQty(
                        imageList = imageList,
                        invPatByNumSerie = invPatByNumSerie,
                        articleCode = articleMapByBarCode[codeArt]?.aRTCode?: "",
                        numeSerie = selectedPatrimoine.numSerie,
                        patrimoineVerificationState = patrimoineVerificationState,
                        selectedPatrimoineList = selectedPatrimoineList,
                        addItemToSelectedPatrimoineList = {
                            selectPatrimoineVM.deleteSelectedPatrimoine(selectPatrimoineVM.backUpSelectedPatrimoine)// delete current line to insert a new one
                            selectPatrimoineVM.addItemToSelectedPatrimoineList(it)
                        },
                        marque =
                            if (invPatByNumSerie.isNotEmpty()) {
                                marqueList.firstOrNull {
                                    it.mARCode == (invPatByNumSerie.entries.first().value.first().lGDEVCMarq ?: "")
                                } ?: Marque()
                            } else {
                                Marque()
                            },
                        note = selectedPatrimoine.note,
                    )

                    //   cameraViewModel.addListImageUri(imageList = invPatByNumSerie.entries.flatMap { value -> value.value.flatMap { it.imageList ?: emptyList() } })

                },
                onBareCodeScan = {
                    openBareCodeScanner(
                        navigate = { navigate(it) },
                        onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) },
                    )
                },
                onAffect = {

                    invPatViewModel.saveInvPat(
                        articleMapByBarCode = articleMapByBarCode,
                        deleteLgAndImages = true,
                        status = ItemStatus.WAITING.status,
                        codeM = codeM,
                        listSelectedPatrimoine = selectedPatrimoineList,
                        exercice = exerciceList.first().exerciceCode,
                        utilisateur = utilisateur,
                        typeInv = TypePatrimoine.INVENTAIRE.typePat,
                        devEtat = Constants.IMMOBILISATION,
                        selectedZoneConsomation = selectedZoneConsomation,
                        onComplete = {

                            val codM = if(selectedPatrimoineList.isNotEmpty()) codeM else ""
                            selectPatrimoineVM.clearSelectedPatrimoineList()
                            mainViewModel.generateCodeM(
                                utilisateur = utilisateur,
                                prefix = "Aff"
                            )

                            selectPatrimoineVM.resetPatrimoineVerificationState()
                            //   selectPatrimoineVM.setSelectedPat(SelectedPatrimoine())
                            selectPatrimoineVM.onKeepTypedNumSerieChange(true)
                            selectPatrimoineVM.onShowSetNumeSerieChange(false)

                            navigate(AjoutAffectationBatimentRoute(numSerie = selectedPatrimoine.numSerie, codeInventaire = codM))



                        }
                    )

                },
                onNoteChange = {
                    selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(note = it))
                },
            )
        }

        when (windowSize.widthSizeClass) {
            WindowWidthSizeClass.Compact -> {

                ColumnView(
                    articleMapByBarCode = articleMapByBarCode,
                    marqueList = marqueList,
                    haveCamera = haveCamera,
                    selectedPatrimoine = selectedPatrimoine,
                    firstItemDetail = ItemDetailData(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = stringResource(id = R.string.zone_Consomation),
                        dataText = selectedZoneConsomation.cLINomPren,
                        icon = Icons.Default.AccountBalance,
                        tint = LocalContentColor.current
                    ),
                    secondItemDetail = null,
                    padding = padding,
                    selectedPatrimoineList = selectedPatrimoineList,
                    fiterValue = fiterValue,
                    showFilterLine = showFilterLine,
                    openVerticalalImagePagerDialog = openVerticalalImagePagerDialog,
                    onOpenVerticalalImagePagerDialogChange = {
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(it)
                    },
                    onPress = {
                        selectPatrimoineVM.onBackUpSelectedPatrimoineChange(it)
                        selectPatrimoineVM.setSelectedPat(it)
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    },
                    onShowFilterLineChange = {
                        selectPatrimoineVM.onShowFilterLineChange(it)
                    },
                    onFilterValueChange = {
                        selectPatrimoineVM.onFilterValueChange(it)
                    },
                    onLongPress = {
                        invPatViewModel.onShowAlertDialogChange(it)
                    },
                    onDeleteClick = {
                        selectPatrimoineVM.deleteImage(images = it)
                        if (it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                    },
                    onPressTakeImage = {
                        selectPatrimoineVM.setSelectedPat(it)

                        cameraViewModel.onNumChange(value = it.numSerie)
                        navigate(MainImageTiketRoute)
                    },
                    onPressSeeImage = {
                        selectPatrimoineVM.setSelectedPat(it)
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(true)
                    }


                )
            }

            WindowWidthSizeClass.Expanded,
            WindowWidthSizeClass.Medium -> {
                RowView(
                    articleMapByBarCode = articleMapByBarCode,
                    marqueList = marqueList,
                    haveCamera = haveCamera,
                    selectedPatrimoine = selectedPatrimoine,
                    padding = padding,
                    firstItemDetail = ItemDetailData(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = stringResource(id = R.string.zone_Consomation),
                        dataText = selectedZoneConsomation.cLINomPren,
                        icon = Icons.Default.AccountBalance,
                        tint = LocalContentColor.current
                    ),
                    secondItemDetail = null,
                    selectedPatrimoineList = selectedPatrimoineList,
                    fiterValue = fiterValue,
                    showFilterLine = showFilterLine,
                    openVerticalalImagePagerDialog = openVerticalalImagePagerDialog,
                    onOpenVerticalalImagePagerDialogChange = {
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(it)
                    },
                    onPress = {
                        selectPatrimoineVM.onBackUpSelectedPatrimoineChange(it)
                        selectPatrimoineVM.setSelectedPat(it)
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    },
                    onShowFilterLineChange = {
                        selectPatrimoineVM.onShowFilterLineChange(it)
                    },
                    onFilterValueChange = {
                        selectPatrimoineVM.onFilterValueChange(it)
                    },
                    onLongPress = {
                        invPatViewModel.onShowAlertDialogChange(it)
                    },
                    onDeleteClick = {
                        selectPatrimoineVM.deleteImage(images = it)
                        if (it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                    },
                    onPressTakeImage = {
                        selectPatrimoineVM.setSelectedPat(it)

                        cameraViewModel.onNumChange(value = it.numSerie)
                        navigate(MainImageTiketRoute)
                    },
                    onPressSeeImage = {
                        selectPatrimoineVM.setSelectedPat(it)
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(true)
                    }


                )
            }

            else -> {
                ColumnView(
                    articleMapByBarCode = articleMapByBarCode,
                    marqueList = marqueList,
                    haveCamera = haveCamera,
                    selectedPatrimoine = selectedPatrimoine,
                    firstItemDetail = ItemDetailData(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = stringResource(id = R.string.zone_Consomation),
                        dataText = selectedZoneConsomation.cLINomPren,
                        icon = Icons.Default.AccountBalance,
                        tint = LocalContentColor.current
                    ),
                    secondItemDetail = null,
                    padding = padding,
                    selectedPatrimoineList = selectedPatrimoineList,
                    fiterValue = fiterValue,
                    showFilterLine = showFilterLine,
                    openVerticalalImagePagerDialog = openVerticalalImagePagerDialog,
                    onOpenVerticalalImagePagerDialogChange = { cameraViewModel.onOpenVerticalalImagePagerDialogChange(it) },
                    onPress = {
                        selectPatrimoineVM.onBackUpSelectedPatrimoineChange(it)
                        selectPatrimoineVM.setSelectedPat(it)
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    },
                    onShowFilterLineChange = { selectPatrimoineVM.onShowFilterLineChange(it) },
                    onFilterValueChange = { selectPatrimoineVM.onFilterValueChange(it) },
                    onLongPress = { invPatViewModel.onShowAlertDialogChange(it) },
                    onDeleteClick = {
                        selectPatrimoineVM.deleteImage(images = it)
                        if (it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                    },
                    onPressTakeImage = {
                        selectPatrimoineVM.setSelectedPat(it)

                        cameraViewModel.onNumChange(value = it.numSerie)
                        navigate(MainImageTiketRoute)
                    },
                    onPressSeeImage = {
                        selectPatrimoineVM.setSelectedPat(it)
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(true)
                    }


                )
            }
        }
    }
}


fun navigateTo(
    navigate: (route: Any) -> Unit,
    isUpdate: Boolean,
    navDrawerViewModel: NavigationDrawerViewModel
) {
    if (isUpdate) {
        proCaisseDrawerItems.find { it.id == INVENTAIRE_BATIMENT_ID }
            ?.let {
                navDrawerViewModel.onSelectedMenuChange(it)
            }
        navigate(InventaireBatimentRoute)
    }
    else navigate(ZoneConsomationDetailRoute)
}
