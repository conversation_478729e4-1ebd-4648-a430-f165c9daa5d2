@file:OptIn(ExperimentalMaterial3Api::class)

package com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.cash

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Money
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.OutlinedCard
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.simapps.ui_kit.custom_cards.ItemDetail

@Composable
fun CashPaymentView(cashValue : String,
                    onLongPress: () -> Unit,
                    onTap: () -> Unit
                    ) {
    OutlinedCard(
        modifier =
        Modifier
            .fillMaxWidth()
            .padding(start = 16.dp, end = 16.dp),

    ) {

        ItemDetail(
            title = stringResource(id = R.string.cash_title),
            modifier = Modifier.padding(top = 18.dp, bottom = 18.dp),
            dataText = StringUtils.convertStringToPriceFormat(cashValue),
            icon = Icons.Default.Money,
            onClick = {
                onTap()
            },
            onLongPress = {
                onLongPress()
            }
        )
    }

}