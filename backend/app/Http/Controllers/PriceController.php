<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class PriceController extends Controller
{

    public function getPricesByStation(request $request)
    {

        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Prix_Par_Station')->get());
    }


}
