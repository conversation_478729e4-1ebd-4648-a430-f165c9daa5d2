<?php

namespace App\Http\Controllers;

use App\Helpers\AppHelper;
use App\Helpers\DatabaseConnection;
use App\Http\Requests;
use Carbon\Carbon;
use Illuminate\Http\Request;

class TrakingController extends Controller
{

    public function InsertTraking(Request $request)
    {
        $f = false;
        try {
            $items = $request->json()->all();
            $connection = DatabaseConnection::setConnection($items);
            if (!empty($items)) {
                foreach ($items["object"] as $data) {
                    $item = [
                        'Trak_Id' => $data['Trak_Id'],
                        'Trak_Latitude' => $data['Trak_Latitude'],
                        'Trak_Longitude' => $data['Trak_Longitude'],
                        'Trak_dateMachine' => AppHelper::setDateFormat(),
                        'Trak_dateCamion' => AppHelper::setDateFormat($data['Trak_dateCamion']),
                        'Trak_station' => $data['Trak_station'],
                        'Trak_user' => $data['Trak_user'],
                        'Trak_session' => $data['Trak_session'],
                        'Trak_note' => $data['Trak_note'],
                        'Trak_CodeOrdre' => $data['Trak_CodeOrdre'],
                    ];
                    if (!$connection->table("Traking")
                        ->where("Trak_Id", $data['Trak_Id'])->first()) {
                        $f = $connection->table('Traking')->insert($item);
                    }
                }
                return response()->json($f);
            } else {
                return response()->json(false);
            }
        } catch (\Exception $e) {
            return response()->json($f);
        }


    }

}






