<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Symfony\Component\HttpFoundation\Response;

class M_DocumentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'object' => 'required|array',
            'object.*.id' => 'nullable|string',
            'object.*.idMobile' => 'nullable|string',
            'object.*.idTier' => 'nullable|string',
            'object.*.Id_Technicien' => 'nullable|string',
            'object.*.codePiece' => 'nullable|string',
            'object.*.concratisePar' => 'nullable|string',
            'object.*.dateDocument' => 'nullable|date',
            'object.*.dateDepot' => 'nullable|date',
            'object.*.idDevise' => 'nullable|string',
            'object.*.idEtatDocument' => 'nullable|string',
            'object.*.idRepresentant' => 'nullable|string',
            'object.*.id_Apporteur' => 'nullable|string',
            'object.*.isTierexonoTva' => 'nullable|boolean',
            'object.*.j_codeUser' => 'nullable|string',
            'object.*.mntBrutht' => 'nullable|numeric',
            'object.*.mntNetht' => 'nullable|numeric',
            'object.*.mntRemise' => 'nullable|numeric',
            'object.*.mntTtc' => 'nullable|numeric',
            'object.*.ancienmntTtc' => 'nullable|numeric',
            'object.*.mntTva' => 'nullable|numeric',
            'object.*.mntht' => 'nullable|numeric',
            'object.*.tauxChange' => 'nullable|numeric',
            'object.*.tauxRemise' => 'nullable|numeric',
            'object.*.timbre' => 'nullable|numeric',
            'object.*.idStation' => 'nullable|string',
            'object.*.note' => 'nullable|string',
            'object.*.lignes' => 'nullable|array',
            'object.*.lignes.*.idLigneDocumentPere' => 'nullable|string',
            'object.*.lignes.*.numOrdre' => 'nullable|integer',
            'object.*.lignes.*.idArticle' => 'nullable|string',
            'object.*.lignes.*.codeArticle' => 'nullable|string',
            'object.*.lignes.*.libelleArticle' => 'nullable|string',
            'object.*.lignes.*.qte' => 'nullable|numeric',
            'object.*.lignes.*.puht' => 'nullable|numeric',
            'object.*.lignes.*.mntht' => 'nullable|numeric',
            'object.*.lignes.*.mntBrutht' => 'nullable|numeric',
            'object.*.lignes.*.tauxRemise' => 'nullable|numeric',
            'object.*.lignes.*.mntRemise' => 'nullable|numeric',
            'object.*.lignes.*.mntNetht' => 'nullable|numeric',
            'object.*.lignes.*.fodec' => 'nullable|numeric',
            'object.*.lignes.*.dc' => 'nullable|numeric',
            'object.*.lignes.*.assietteTva' => 'nullable|numeric',
            'object.*.lignes.*.mntTva' => 'nullable|numeric',
            'object.*.lignes.*.puttc' => 'nullable|numeric',
            'object.*.lignes.*.mntttc' => 'nullable|numeric',
            'object.*.lignes.*.ancienQte' => 'nullable|numeric',
            'object.*.lignes.*.idDocumentBase' => 'nullable|string',
            'object.*.lignes.*.idLigneDocumentBase' => 'nullable|string',
            'object.*.lignes.*.numeroDeSerie' => 'nullable|string',
            'object.*.lignes.*.note' => 'nullable|string',
            'object.*.lignes.*.etatLigne' => 'nullable|string',
            'object.*.lignes.*.datevalidite' => 'nullable|date',
            'object.*.lignes.*.idUniteVente' => 'nullable|string',
            'object.*.lignes.*.idMobile' => 'nullable|string',

        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY)
        );
    }
}
