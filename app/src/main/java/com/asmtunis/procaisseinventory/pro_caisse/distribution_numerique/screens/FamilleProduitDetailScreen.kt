package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.twotone.Add
import androidx.compose.material.icons.twotone.Delete
import androidx.compose.material.icons.twotone.DeleteForever
import androidx.compose.material.icons.twotone.Edit
import androidx.compose.material.icons.twotone.LocationOn
import androidx.compose.material.icons.twotone.SaveAs
import androidx.compose.material3.AssistChip
import androidx.compose.material3.AssistChipDefaults
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.text_validation.AddVisiteFormEvent
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.text_validation.VisiteTextValidationViewModel
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.view_model.DistributionNumeriqueViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.simapps.ui_kit.dialogues.CustomAlertDialogue
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu
import com.simapps.ui_kit.edit_text.EditTextField


@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun FamilleProduitDetailScreen(
    navigate: (route: String) -> Unit,
    popBackStack: () -> Unit,
    settingViewModel: SettingViewModel,
    distNumViewModel: DistributionNumeriqueViewModel,
    mainViewModel: MainViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    visiteTextValidationViewModel: VisiteTextValidationViewModel = hiltViewModel(),
) {
    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val stateAddVisite = visiteTextValidationViewModel.stateAddVisite
    val context = LocalContext.current

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig




    val modifyVisite = distNumViewModel.modifyVisite
    val visitesDn = distNumViewModel.selectedVisite
    val ligneVisitesDn = distNumViewModel.selectedVisiteLines
    val selectedLigneVisiteDn = distNumViewModel.selectedLigneVisiteDn



    val familleList = mainViewModel.listFamilleDn
    val concurrentList = mainViewModel.listConcurentVC

    val codeM = visitesDn.vIS_Code_M.ifEmpty { mainViewModel.codeM }




    LaunchedEffect(key1 = ligneVisitesDn) {
        visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.LigneVisitesDnChanged(ligneVisitesDn))
    }




    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = { popBackStack() },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = stringResource(R.string.famille_produit),
            )
        },
        floatingActionButton = {
             FloatingActionButton(
                onClick = {
                    if (visitesDn.status == ItemStatus.DELETED.status) {
                        showToast(
                            context = context,
                            toaster = toaster,
                            message = context.resources.getString(R.string.visite_deleted),
                            type = ToastType.Success,
                        )
                        return@FloatingActionButton
                    }

                    if (!modifyVisite) {
                        mainViewModel.onSelectedClientChange(mainViewModel.clientList.firstOrNull { it.cLICode == visitesDn.vIS_CodeClient }?: Client(cLICode = visitesDn.vIS_CodeClient?: "N/A", cLINomPren = visitesDn.vIS_CodeClient?: "N/A"))
                        distNumViewModel.onModifyVisiteChange(true)
                    } else {
                        popBackStack()
                    }


                }) {
                Icon(
                    imageVector = if (modifyVisite) Icons.TwoTone.SaveAs else Icons.TwoTone.Edit,
                    contentDescription = stringResource(id = R.string.cd_addVisite_button)
                )
            }
        }
    ) { padding ->
        CustomAlertDialogue(
            title = stringResource(id = R.string.delete_confirmation_msg),
            msg = "",
            imageVector = Icons.TwoTone.DeleteForever,
            openDialog = distNumViewModel.showAlertDialog,
            setDialogueVisibility = {
                mainViewModel.onShowAlertDialogChange(it)
            },
            customAction = {
                distNumViewModel.onDeleteLigneVisitesDnChange(selectedLigneVisiteDn)

                distNumViewModel.onShowAlertDialogChange(false)
            },

            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non),
            negatifAction  = { distNumViewModel.onShowAlertDialogChange(false) }
        )
        LazyColumn(
            modifier = Modifier
                .padding(padding)
                .fillMaxSize()
        ) {


            item {
                Spacer(modifier = Modifier.height(16.dp))


                Spacer(modifier = Modifier.height(16.dp))


                if(stateAddVisite.ligneVisitesDnError != null && ligneVisitesDn.isEmpty()) {
                    Text(
                        modifier = Modifier.fillMaxWidth(),
                        //  text = "Famille Produits",
                        text = stateAddVisite.ligneVisitesDnError.asString()?: "Unknown Ligne VisitesDn Error",
                        color = MaterialTheme.colorScheme.error,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                }
                //  HorizontalDivider()
            }

            items(
                count = ligneVisitesDn.size,
                key = { ligneVisitesDn[it].lgVISFamille + ligneVisitesDn[it].lgVISTier }
            ) { index ->

                OutlinedCard(
                    modifier =  Modifier.fillMaxWidth().padding(start = 12.dp, end = 12.dp),
                    onClick = { /*TODO*/ }) {
                    Row(
                        modifier = Modifier.fillMaxWidth(0.95f).padding(top = 12.dp, bottom = 12.dp),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column(modifier =  Modifier.fillMaxWidth(0.75f)) {

                            Text(
                                text = stringResource(R.string.produit, mainViewModel.getFamille(ligneVisitesDn[index].lgVISFamille).desgFamille),
                                style = MaterialTheme.typography.titleMedium,
                                textAlign = TextAlign.Center
                            )




                            Text(
                                text = stringResource(R.string.Fournisseur, mainViewModel.getConcurentVC(ligneVisitesDn[index].lgVISTier).concurrent),
                                style = MaterialTheme.typography.titleMedium,
                                textAlign = TextAlign.Center
                            )







                       if(!ligneVisitesDn[index].lgVISInfo1.isNullOrEmpty()) {
                           Text(
                               text = ligneVisitesDn[index].lgVISInfo1?: "",
                               color = MaterialTheme.colorScheme.outline,
                               textAlign = TextAlign.Center
                           )
                       }

                        }

                        AnimatedVisibility(
                            visible = modifyVisite,
                            enter = fadeIn() + slideInVertically(),
                            exit = fadeOut() + slideOutVertically()
                        ) {
                            Icon(
                                imageVector = Icons.TwoTone.Delete,
                                contentDescription = stringResource(R.string.delete_famille),
                               modifier = Modifier
                                    .size(35.dp)
                                    .clickable {
                                        distNumViewModel.onSelectedLigneVisiteDnChange(ligneVisitesDn[index])
                                        distNumViewModel.onShowAlertDialogChange(true)

                                    }
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(6.dp))

                //HorizontalDivider()
            }

            item() {
                Spacer(modifier = Modifier.height(16.dp))
                AnimatedVisibility(
                    visible = modifyVisite,
                    enter = fadeIn() + slideInVertically(),
                    exit = fadeOut() + slideOutVertically()
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {

                        Row(
                            modifier = Modifier.fillMaxWidth(0.95f).height(66.dp),
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            GenericDropdownMenu (
                                modifier = Modifier.fillMaxWidth(0.45f),
                                label = stringResource(R.string.famille_produit),
                                designation = stateAddVisite.familleProduit.desgFamille,
                                errorValue = stateAddVisite.familleProduitError?.asString(),
                                itemList = familleList,
                                itemExpanded = distNumViewModel.familleProduitExpanded,
                                selectedItem = stateAddVisite.familleProduit,
                                getItemDesignation = { it.desgFamille },
                                onClick = {
                                    visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.FamilleProduitChanged(it))
                                    distNumViewModel.onFamilleProduitExpandedChange(false)
                                },
                                onItemExpandedChange = {
                                    if (modifyVisite)  distNumViewModel.onFamilleProduitExpandedChange(it)
                                },
                                lottieAnimEmpty = { LottieAnim(lotti = R.raw.emptystate) },
                                lottieAnimError = { LottieAnim(lotti = R.raw.connection_error, size = it) }
                            )




                            GenericDropdownMenu (
                                modifier = Modifier.fillMaxWidth(),
                                label = stringResource(R.string.fournisseur),
                                designation = stateAddVisite.fournisseur.concurrent,
                                errorValue = stateAddVisite.fournisseurError?.asString(),
                                itemList = concurrentList,
                                itemExpanded = distNumViewModel.concurrentVCExpanded,
                                selectedItem = stateAddVisite.fournisseur,
                                getItemDesignation = { it.concurrent },
                                onClick = {
                                    visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.FournisseurChanged(it))
                                    distNumViewModel.onConcurrentVCExpandedChange(false)
                                },
                                onItemExpandedChange = {
                                    if (modifyVisite)  distNumViewModel.onConcurrentVCExpandedChange(it)
                                },
                                lottieAnimEmpty = { LottieAnim(lotti = R.raw.emptystate) },
                                lottieAnimError = { LottieAnim(lotti = R.raw.connection_error, size = it) }
                            )


                        }



                        EditTextField(
                            modifier = Modifier.fillMaxWidth(0.95f),
                            text = stateAddVisite.remarque,
                           // errorValue = stateAddVisite.latitudeError?.asString(),
                            label = stringResource(R.string.remarque),
                            onValueChange = {
                                visiteTextValidationViewModel.onAddVisiteEvent(AddVisiteFormEvent.RemarqueChanged(it))
                            },
                            readOnly = false,
                            enabled = modifyVisite,
                            leadingIcon = Icons.TwoTone.LocationOn,
                            keyboardType = KeyboardType.Text,
                            imeAction = ImeAction.Done
                        )

                        AssistChip(
                            onClick = {
                                distNumViewModel.addFamilleItem(
                                    codeM = codeM,
                                    stateAddVisite = stateAddVisite,
                                    exerciceList = mainViewModel.exerciceList,
                                    onFinish = { visiteTextValidationViewModel.restStateAddVisiteFamilleProduit() },
                                    showToast = { message, type ->
                                        showToast(
                                            context = context,
                                            toaster = toaster,
                                            message = context.resources.getString(message),
                                            type = type,
                                        )
                                    }
                                )
                            },
                            label = { Text(context.resources.getString(R.string.addFamille)) },
                            leadingIcon = {
                                Icon(
                                    Icons.TwoTone.Add,
                                    contentDescription = context.resources.getString(R.string.addFamille),
                                    Modifier.size(AssistChipDefaults.IconSize)
                                )
                            }
                        )
                    }
                }

                Spacer(modifier = Modifier.height(75.dp))
            }
        }
    }
}