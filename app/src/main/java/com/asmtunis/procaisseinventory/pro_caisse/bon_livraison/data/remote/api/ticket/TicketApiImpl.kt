package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.remote.api.ticket

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate
import io.ktor.client.HttpClient
import io.ktor.utils.io.InternalAPI
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class TicketApiImpl(private val client: HttpClient) : TicketApi {


    override suspend fun getTicketsByCaisseId(
        baseConfig: String,
        ddm: String,
        exercice: String,
        archive: Boolean,
        zone: Boolean
    ): Flow<DataResult<List<Ticket>>> = flow {

        val queryParams = mapOf(
            "ddm" to ddm,
            "exercice" to exercice,
            "archive" to archive,
            "zone" to zone
        )
        val result = executePostApiCall<List<Ticket>>(
            client = client,
            queryParams = queryParams,
            endpoint = Urls.GET_TICKETS_BY_CAISSE_ID,
            baseConfig = baseConfig
        )

        emitAll(result)

    }

    override suspend fun getMaxNumTicket(
        baseConfig: String,
        idExerc: String,
        idCarnet: String
    ): Flow<DataResult<Int>> = flow {

        val queryParams = mapOf(
            "id_exerc" to idExerc,
            "id_carnet" to idCarnet
        )
        val result = executePostApiCall<Int>(
            client = client,
            queryParams = queryParams,
            endpoint = Urls.GET_MAX_NUM_TICKET,
            baseConfig = baseConfig
        )
        emitAll(result)
    }

    override suspend fun addBatchTicketWithLignesTicketAndPayment(
        baseConfig: String,
        autoFacture: Boolean
    ): Flow<DataResult<List<TicketUpdate>>> = flow {


        val queryParams = mapOf("auto_facture" to autoFacture)
        val result = executePostApiCall<List<TicketUpdate>>(
            client = client,
            queryParams = queryParams,
            endpoint = Urls.ADD_BATCH_TICKETS_WITH_LIGNES_TICKET_AND_PAYMENT,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun addBatchFactureWithLines(baseConfig: String): Flow<DataResult<List<TicketUpdate>>> = flow {

        val result = executePostApiCall<List<TicketUpdate>>(
            client = client,
            endpoint = Urls.ADD_BATCH_FACTURE_WITH_LINES,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
}