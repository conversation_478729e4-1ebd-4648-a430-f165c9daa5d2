package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


@Entity(tableName = ProCaisseConstants.NEW_PRODUCT_TABLE)
@Serializable
data class NewProductVC  (
    @PrimaryKey(autoGenerate = true)
    @Transient
    val id: Long = 0,
    @ColumnInfo(name = "CodeVCLanP")
    @SerialName("CodeVCLanP")
    val codeVCLanP: String = "",

    @ColumnInfo(name = "CodeVCLanPM")
    @SerialName("CodeVCLanPM")
    val codeVCLanPM: String = "",

    @ColumnInfo(name = "ProduitLanP")
    @SerialName("ProduitLanP")
    val produitLanP: String = "",

    @ColumnInfo(name = "DateOp")
    @SerialName("DateOp")
    val dateOp: String = "",

    @ColumnInfo(name = "CodeConcur")
    @SerialName("CodeConcur")
    val codeConcur: String = "",

    @ColumnInfo(name = "NoteOp")
    @SerialName("NoteOp")
    val noteOp: String? = "",

    @ColumnInfo(name = "PrixLanP")
    @SerialName("PrixLanP")
    val prixLanP: Double = 0.0,

    @ColumnInfo(name = "TauxPromo")
    @SerialName("TauxPromo")
    val tauxPromo: Double = 0.0,

    @ColumnInfo(name = "CodeUser")
    @SerialName("CodeUser")
    val codeUser: Int? = 0,

    @ColumnInfo(name = "InfoOp1")
    @SerialName("InfoOp1")
    val infoOp1: String? = "",

    @ColumnInfo(name = "CodeTypeCom")
    @SerialName("CodeTypeCom")
    val codeTypeCom: String = ""


    ): BaseModel()






