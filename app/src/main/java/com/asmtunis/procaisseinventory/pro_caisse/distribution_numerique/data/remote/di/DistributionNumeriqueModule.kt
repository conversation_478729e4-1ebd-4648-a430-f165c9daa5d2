package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.remote.di


import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.remote.api.DistributionNumeriqueApi
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.remote.api.DistributionNumeriqueApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DistributionNumeriqueModule {

    @Provides
    @Singleton
    fun provideDistributionNumeriqueApi(client: HttpClient): DistributionNumeriqueApi
    = DistributionNumeriqueApiImpl(client)

}
