<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property string $CodeAutre
 * @property string $Autre
 * @property string $AutreNote
 * @property string $DateOp
 * @property string $CodeConcur
 * @property string $NoteOp
 * @property int $CodeUser
 * @property string $InfoOp1
 * @property string $CodeTypeCom
 */
class VCAutre extends Model
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'VCAutre';

    /**
     * The primary key for the model.
     * 
     * @var string
     */
    protected $primaryKey = 'CodeAutre';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['Autre', 'AutreNote', 'DateOp', 'CodeConcur', 'NoteOp', 'CodeUser', 'InfoOp1', 'CodeTypeCom'];

    public function VC_Image()
    {
        $this->hasMany(VCImage::class);
    }
}
