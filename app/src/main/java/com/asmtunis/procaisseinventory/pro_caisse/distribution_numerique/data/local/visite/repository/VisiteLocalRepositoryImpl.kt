package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.visite.repository


import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.LigneVisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.VisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.visite.dao.LigneVisiteDAO
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.visite.dao.VisiteDAO
import kotlinx.coroutines.flow.Flow

class VisiteLocalRepositoryImpl(
    private val visiteDAO: VisiteDAO,
    private val ligneVisiteDAO: LigneVisiteDAO

) : VisiteLocalRepository {

    override fun loadVisiteAndLignesVisite(
        visNum: String,
        exercice: String
    ): Flow<Map<VisitesDn, List<LigneVisitesDn>>> =
        visiteDAO.loadVisiteAndLignesVisite(visNum, exercice)

    override fun allCount(): Flow<Int> = visiteDAO.allCount
    override fun getNotSyncedVisite(): Flow<Map<VisitesDn, List<LigneVisitesDn>>> = visiteDAO.noSyncedToAddOrUpdate()
    override fun getNoSyncedToDelete(): Flow<Map<VisitesDn, List<LigneVisitesDn>>> = visiteDAO.noSyncedToDelete()
    override fun updateLgVisNum(code: String, codeMobile: String) = ligneVisiteDAO.updateLgVisNum(code, codeMobile)
    override fun updateVisite(code: String, codeMobile: String) = visiteDAO.updateVisite(code, codeMobile)
    override fun setIsDeletedVisite(code: String, exercice: String) =
        visiteDAO.setIsDeletedVisite(code = code, exercice = exercice)
    override fun restDeletedVisite(code: String, exercice: String,status : String, isSync:Boolean) =
        visiteDAO.restDeletedVisite(code = code, exercice = exercice,
            status = status, isSync = isSync)





    override fun upsertVisite(value: VisitesDn) = visiteDAO.insert(value)

    override fun upsertLigneVisite(value: LigneVisitesDn) = ligneVisiteDAO.insert(value)

    override fun upsertVisiteAll(value: List<VisitesDn>) = visiteDAO.insertAll(value)

    override fun upsertLigneVisiteAll(value: List<LigneVisitesDn>) = ligneVisiteDAO.insertAll(value)
    override fun deleteVisiteByVisNum(visCode: String, exercice: String) =
        visiteDAO.deleteVisiteByVisNum(visCode, exercice)

    override fun deleteVisiteAll() = visiteDAO.deleteAll()

    override fun deleteLigneVisiteAll() = ligneVisiteDAO.deleteAll()

    override fun deleteVisite(visitesDn: VisitesDn) = visiteDAO.delete(visitesDn)

    override fun deleteLigneVisite(dnLigneVisite: LigneVisitesDn) =
        ligneVisiteDAO.delete(dnLigneVisite)

    override fun deleteLigneVisiteByVisNum(numVis: String, exercice: String) =
        ligneVisiteDAO.deleteByVisNum(numVis, exercice)

    override fun getLigneVisiteAll(): Flow<List<LigneVisitesDn>> = ligneVisiteDAO.all

    override fun getVisiteAll(): Flow<List<VisitesDn>> = visiteDAO.all
    override fun getVisiteListByClient(codeClient: String): Flow<Map<VisitesDn, List<LigneVisitesDn>>> =
        visiteDAO.getVisiteListByClient(codeClient)

    override fun getAllFiltred(
        isAsc: Int?,
        sortBy: String?,
        codeClient: String
    ): Flow<Map<VisitesDn, List<LigneVisitesDn>>> = visiteDAO.getAllFiltred(
        isAsc,
        sortBy,
        codeClient
    )

    override fun filterByNomGerant(
        filterString: String,
        sortBy: String?,
        isAsc: Int?,
        codeClient: String
    ): Flow<Map<VisitesDn, List<LigneVisitesDn>>> = visiteDAO.filterByNomGerant(
        filterString,
        sortBy,
        isAsc,
        codeClient
    )

    override fun filterByNumVisite(
        filterString: String,
        sortBy: String?,
        isAsc: Int?,
        codeClient: String
    ): Flow<Map<VisitesDn, List<LigneVisitesDn>>> = visiteDAO.filterByNumVisite(
        filterString,
        sortBy,
        isAsc,
        codeClient
    )

    override fun filterByProspect(
        filterString: String,
        sortBy: String?,
        isAsc: Int?,
        codeClient: String
    ): Flow<Map<VisitesDn, List<LigneVisitesDn>>> = visiteDAO.filterByProspect(
        filterString,
        sortBy,
        isAsc,
        codeClient
    )
}
