package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.screens

import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.twotone.PersonOutline
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedButton
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.authorizations.AuthorizationFunction
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.PaymentListRoute
import com.asmtunis.procaisseinventory.core.navigation.SelectArticlesScreensCalculRoute
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertDoubleToDoubleFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToDoubleFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.removeTrailingZeroInDouble
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.ui.PriceTableFooter
import com.asmtunis.procaisseinventory.pro_caisse.ui.SetArticleDialogue
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AddViewBottomAppBar
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.shared_ui_components.tables.TableTextUtils
import com.asmtunis.procaisseinventory.shared_ui_components.tables.five_column.FiveColumnTable
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.custom_cards.ItemDetail
import com.simapps.ui_kit.dialogues.CustomAlertDialogue
import com.simapps.ui_kit.edit_text.EditTextField


@Composable
fun AddBonLivraisonScreen(
    navigateTo: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    clientId: String,
    settingViewModel: SettingViewModel,
    selectArtMobilityVM: SelectArticleCalculViewModel,
    proCaisseViewModels: ProCaisseViewModels,

    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    barCodeViewModel: BarCodeViewModel,
    mainViewModel: MainViewModel,
    navDrawerVM: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel
) {
    val context = LocalContext.current

  val bonLivraisonVM = proCaisseViewModels.bonLivraisonViewModel
   val bonCommandeVM = proCaisseViewModels.bonCommandeViewModel
  val utilisateur = mainViewModel.utilisateur

    val hasPromo = mainViewModel.hasPromo(mainViewModel.stationList.firstOrNull { it.sTATCode == utilisateur.Station })
    val selectedArticle = selectArtMobilityVM.selectedArticle
    val selectedArticleList = selectArtMobilityVM.selectedArticleList

    val scrollState = rememberScrollState()

    val selectedBonCommandeWithClient = bonCommandeVM.selectedBonCommandeWithClient
    val bonCommande = selectedBonCommandeWithClient.bonCommande
    //if from bon commande (bc to bl) get bonCommande!!.dEVNum else get bl codeM
    val codeM = bonCommande?.dEVNum?.ifEmpty { bonLivraisonVM.codeM } ?: bonLivraisonVM.codeM


    val clientFromBonCommande = selectedBonCommandeWithClient.client

    val clientList = mainViewModel.clientList
    val tvaList = mainViewModel.tvaList

    val client = clientList.firstOrNull { it.cLICode == clientId }?: Client()
    val clientByCode = clientFromBonCommande?: client


    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val proCaisseAuthorization = getProCaisseDataViewModel.authorizationList

    val haveAutoBLTimbreEnabledAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.BL_TIMBRE }

    val haveTimber = bonLivraisonVM.haveTimbre(
        client = clientByCode,
        autoBLTimbreEnabled = haveAutoBLTimbreEnabledAuthorisation
    )


    //  val isFromBonCommande = codeM.contains(BCC)
    val isFromBonCommande = selectedBonCommandeWithClient.bonCommande !=null


  //  val currentSelectdArt = selectArtMobilityVM.getCurrentSelectdArt(article = selectedArticle.article, tvaList = tvaList)

    val selectedPaymentMode = bonLivraisonVM.selectedPaymentMode

    val haveFixedDiscountAuthorisation = proCaisseAuthorization.firstOrNull { it.AutoCodeAu == AuthorizationValuesProCaisse.FIXED_DISCOUNT }
    val haveControlQteAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.ART_ZERO_STOCK }

    val totPriceTTCWithDicount = CalculationsUtils.totalPriceTTC(selectedArticleList)
    val totPriceWithoutDicount = selectArtMobilityVM.totPriceWithoutDicount
    val totalPriceAfterDiscountChange = selectArtMobilityVM.totalPriceAfterDiscountChange

    val selectedArtList = selectArtMobilityVM.selectedArticleList

    val isAutoScanMode = mainViewModel.isAutoScanMode
    val barCodeInfo = barCodeViewModel.barCodeInfo

    val articleMapByBarCode = mainViewModel.articleMapByBarCode

    val selectedBonLivraison = bonLivraisonVM.selectedBonLivraisonWithFactureAndPayments.ticket ?: Ticket()

    val totalDiscountError = selectArtMobilityVM.totalDiscountError


    val totalDiscount = selectArtMobilityVM.totalDiscount
    val showPriceDetail = selectArtMobilityVM.showPriceDetail

    val customAdress = bonLivraisonVM.customAdress

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    LaunchedEffect(key1 = Unit) {
        /**
         * use haveControlQteAuthorisation if only test on authorisation else use selectArtMobilityVM.setControlQte(true) (only allow art when qte is positif)
         */
      //  selectArtMobilityVM.setControlQte(!haveControlQteAuthorisation)
        selectArtMobilityVM.setControlQte(true)
    }
    LaunchedEffect(key1 = totalDiscount) {
        if(haveFixedDiscountAuthorisation?.AutValues == null) return@LaunchedEffect

        val autValues = haveFixedDiscountAuthorisation.AutValues
        selectArtMobilityVM.onTotalDiscountErrorChange(
            autValues = autValues,
            errorMsg = "Remise maximale $autValues  %" //context.resources.getString(R.string.max_discount, autValues)
        )

    }

    LaunchedEffect(key1 = selectedArticleList.size) {
        if (selectedArticleList.isEmpty()){
            selectArtMobilityVM.onTotalPriceAfterDiscountChange(value = "")
            return@LaunchedEffect
        }
        selectArtMobilityVM.setTotalPrices()
    }






    LaunchedEffect(key1 = barCodeInfo) {
        if (barCodeInfo.value == "") return@LaunchedEffect

        selectArtMobilityVM.handleBareCodeResult(
            errorMessage = context.resources.getString(R.string.article_introvable, ""),
            onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) },
            barCodeInfo = barCodeInfo,
            isAutoScanMode = isAutoScanMode,
            articleMapByBarCode = articleMapByBarCode,
            useSalePrice = true,
            tvaList = tvaList,
            showToast = { message, type ->
                showToast(
                    context = context,
                    toaster = toaster,
                    message = message,
                    type =  type,
                )
            }
        )
    }



/*
    BackHandler(enabled = true) {

        navController.popBackStack()
    }*/

    BackHandler(true) {
        mainViewModel.onShowDismissScreenAlertDialogChange(true)
    }
    Scaffold(
        topBar = {
            AppBar(
                onNavigationClick = { mainViewModel.onShowDismissScreenAlertDialogChange(true) },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = selectedBonLivraison.tIKNumTicketM.ifEmpty { codeM },
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected
            )
        },
        bottomBar = {

            Column {
                PriceTableFooter(
                    listIsEmpty = selectedArtList.isEmpty(),
                    canEdit = !isFromBonCommande, /** if bonCommande?.dEVNum != null then it's bc to bl *** so cant modify prices */
                    haveDiscountAuth = AuthorizationFunction.haveDiscountAuth(proCaisseAuthorization = proCaisseAuthorization),
                    totalDiscountError = totalDiscountError,
                    totalDiscountChange = totalDiscount,
                    totalPriceWithoutDiscountTTC = removeTrailingZeroInDouble(convertDoubleToDoubleFormat(totPriceWithoutDicount)),
                    totalPriceWithDiscount = removeTrailingZeroInDouble(convertDoubleToDoubleFormat(totalPriceAfterDiscountChange)),
                    onTotalDiscountChange = {
                        //   if(!canModify) return@PriceTableFooter
                        selectArtMobilityVM.onTotalDiscountChange(it)
                        selectArtMobilityVM.changeTotPriceAfterDiscount()
                        selectArtMobilityVM.updateDiscountInEveryLine()
                    },
                    onTotalPriceWithDiscountChange = {
                        //      if(!canModify) return@PriceTableFooter
                        selectArtMobilityVM.onTotalPriceAfterDiscountChange(value = it)
                        selectArtMobilityVM.setTotalDiscount(totalPriceAfterDiscount = stringToDouble(it))
                        selectArtMobilityVM.updateDiscountInEveryLine()
                    },
                    isVisible = showPriceDetail && AuthorizationFunction.haveDiscountAuth(proCaisseAuthorization = proCaisseAuthorization) && selectedArticleList.isNotEmpty(),
                    onExpandClick = { selectArtMobilityVM.onShowPriceDetailChange(!showPriceDetail) }
                    )


                AddViewBottomAppBar(
                    haveCameraDevice = dataViewModel.getHaveCameraDevice(),
                    toaster = toaster,
                    showSaveBtn = selectedArticleList.isNotEmpty() && totalDiscountError == null,
                    showAddArticleBtn = !isFromBonCommande,
                    showAutoScanModeBtn = !isFromBonCommande,
                    showBareCodeScannerBtn = !isFromBonCommande && barCodeViewModel.haveCameraDevice,
                    isAutoScanMode = isAutoScanMode,
                    onSaveClick = {
                        selectArtMobilityVM.onTotPriceTTCWithDicountAndTimberChange(haveTimber = haveTimber,  listActifTimber = mainViewModel.listActifTimber)
                        if (bonLivraisonVM.isPassagerClient(client = clientByCode)) {
                            bonLivraisonVM.onShowTikPaymentViewChange(true)
                        }
                        else navigateTo(PaymentListRoute(clientId = clientId))
                    },
                    onClickAddArticle = {
                        mainViewModel.setAddNewProductDialogueVisibility(false)

                        selectArtMobilityVM.onShowTvaChange(false)

                       navigateTo(SelectArticlesScreensCalculRoute)
                    },
                    setAutoScanMode = { mainViewModel.setAutoAddMode(!isAutoScanMode) },
                    openBareCodeScanner = {
                        openBareCodeScanner(
                            navigate = { navigateTo(it) },
                            onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode =  it) }
                        )
                    }
                    )
            }




        }
    ) { padding ->

        CustomAlertDialogue(
            title = context.getString(R.string.info),
            msg = context.getString(R.string.confirm_close_screen),
            openDialog = mainViewModel.showDismissScreenAlertDialog,
            setDialogueVisibility = {
                mainViewModel.onShowDismissScreenAlertDialogChange(it)
            },
            customAction = {
                bonLivraisonVM.onCustomAdressChange("")
                popBackStack()
            },
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non),

        )

        if (bonLivraisonVM.showTikPaymentView) {
            Dialog(
                onDismissRequest = { bonLivraisonVM.onShowTikPaymentViewChange(false) },
                properties = DialogProperties(usePlatformDefaultWidth = true),
                content = {
                    Card(
                        elevation = CardDefaults.cardElevation(),
                        shape = RoundedCornerShape(15.dp),
                     //   modifier = Modifier.padding(12.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(12.dp),
                            verticalArrangement = Arrangement.Center,
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                        Text(
                            modifier = Modifier.fillMaxWidth(0.9f),
                            text = stringResource(R.string.passager_type, clientByCode.cLINomPren)
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        EditTextField(
                            modifier = Modifier.fillMaxWidth(0.9f),
                            text = customAdress,
                            errorValue = null,
                            label = stringResource(R.string.adresse_field_title),
                            onValueChange = { bonLivraisonVM.onCustomAdressChange(it) },
                            readOnly = false,
                            enabled = true,
                            //showTrailingIcon = false,
                            // leadingIcon = Icons.Default.Home,
                            //keyboardType = KeyboardType.Password,
                            imeAction = ImeAction.Done
                        )



                        Spacer(modifier = Modifier.height(16.dp))

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.End,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            AnimatedVisibility(
                                visible = customAdress.isNotBlank(),
                                enter = fadeIn() + slideInVertically(),
                                exit = fadeOut() + slideOutVertically()
                            ) {
                                ElevatedButton(
                                    onClick = {
                                        bonLivraisonVM.onShowTikPaymentViewChange(false)
                                        navigateTo(PaymentListRoute(clientId = clientId))

                                    }
                                ) {
                                    Text(
                                        modifier = Modifier.wrapContentWidth(),
                                        text = stringResource(R.string.confirm),
                                        textAlign = TextAlign.End
                                    )
                                }
                            }


         Spacer(modifier = Modifier.width(12.dp))

                            OutlinedButton(
                                onClick = { bonLivraisonVM.onShowTikPaymentViewChange(false) }
                            ) {
                                Text(
                                    modifier = Modifier.wrapContentWidth(),
                                    text = stringResource(R.string.quitter),
                                    textAlign = TextAlign.End
                                )
                            }
                        }
                    }
                    }

                }
            )
        }

        if(selectArtMobilityVM.showSetArticle) {
            SetArticleDialogue(
                toaster = toaster,
                onConfirm = {
                    if(stringToDouble(selectedArticle.quantity) <= 0.0 || stringToDouble(selectedArticle.lTMtBrutHT) <= 0.0) {
                        selectArtMobilityVM.deleteItemToSelectedArticleMobilityList(selectedArticle.article)

                        return@SetArticleDialogue
                    }
                    selectArtMobilityVM.setTotalPrices()
                },

                onReset = {
                    selectArtMobilityVM.deleteItemToSelectedArticleMobilityList(selectedArticle.article)
                },
                hasPromo = hasPromo,
                proCaisseAuthorization = proCaisseAuthorization,
                setShowPriceCategoryChange = {
                    selectArtMobilityVM.setShowPriceCategoryChange(it)
                },
                onShowSetArticleChange= {
                    selectArtMobilityVM.onShowSetArticleChange(it)
                },
                setShowPriceCategorySingleArticleChange={
                    selectArtMobilityVM.setShowPriceCategorySingleArticleChange(it)
                },
                getSingleArticlePrice = {
                    selectArtMobilityVM.getPrice(it.article)
                },
                onSelectedPriceCategorieChange = {
                    selectArtMobilityVM.onSelectedPriceCategoryChange(it)

                    selectArtMobilityVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        quantiti = selectedArticle.quantity,
                        useSalePrice = true
                    )
                },
                selectedArticle = selectedArticle,
                priceCategoryList = selectArtMobilityVM.priceCategoryList,
                updateQty= {
                    selectArtMobilityVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        //  context = context,
                        operation = Globals.NO_OPERATION,
                        // barCodeViewModel = barCodeViewModel,
                        quantiti = it,
                        typeOperation = "qty",
                        useSalePrice = true
                    )
                },
                onRemiseChange = {
                    selectArtMobilityVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        //  barCodeViewModel = barCodeViewModel,
                        remise = it,
                        typeOperation = "remise",
                        useSalePrice = true
                    )
                },
                onPrixCaisseChange = {
                    selectArtMobilityVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        //     context = context,
                        operation = Globals.NO_OPERATION,
                        //   barCodeViewModel = barCodeViewModel,
                        typeOperation = "prixCaisse",
                        prixCaisse = it,
                        useSalePrice = true
                    )
                },
                onPrixTotalChange = {
                    selectArtMobilityVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = hasPromo,
                        //    context = context,
                        operation = Globals.NO_OPERATION,
                        //   barCodeViewModel = barCodeViewModel,
                        typeOperation = "totalPrice",
                        prixtotal = it,
                        useSalePrice = true
                    )
                },
                onPrixHTChange = {
                    selectArtMobilityVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = false,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        //  barCodeViewModel = barCodeViewModel,
                        typeOperation = "prixAchatHT",
                        prixHT = it,
                        useSalePrice = true
                    )
                },
                showPriceCategorySingleArticle =  selectArtMobilityVM.showPriceCategorySingleArticle,

                //showTva = showTva,
               // tvaList = mainViewModel.tvaList,
             //   selectedTva = selectedArticle.tva,
                onSelectedTvaChange = {
                    //TOdo add calcul
                    //  mainViewModel.onSelectedTvaChange(it)
                    //selectArtMobilityVM.setSelectedArticlTva(it)
                    selectArtMobilityVM.addNewLigneSelectedMobilityArtcle(
                        hasPromo = false,
                        // context = context,
                        operation = Globals.NO_OPERATION,
                        //  barCodeViewModel = barCodeViewModel,
                        tva = it,
                        typeOperation = "tva",
                        useSalePrice = true
                    )
                },
                onTvaExpandedChange = {
                    mainViewModel.onTvaExpandedChange(it)
                },
                tvaExpanded = mainViewModel.tvaExpand,
                onPrixVenteChange = {
                    selectArtMobilityVM.setSelectedArticlPrixVente(it)
                }

            )
        }



        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(padding)
        ) {

            /*   TableHeader(
                   onClickShowCalendar = {
                   },
                   date = DateUtils.getCurrentDateTime(),
                   canModify = true,
                   selectedDateTime = mainViewModel.getSelectedDateTime(),
                   showAddDate = false
               )*/

            ItemDetail(
                modifier = Modifier.fillMaxWidth(0.85f),
                title = stringResource(id = R.string.client_field_title),
                dataText = clientByCode.cLINomPren,
                icon = Icons.TwoTone.PersonOutline
            )
            Spacer(modifier = Modifier.height(9.dp))
            FiveColumnTable(
                selectedListArticle = selectedArticleList,
                canModify = !isFromBonCommande,
                onPress = { selectArtMobilityVM.setSelectedArticl(article = it.article, tvaList = tvaList) },
                onLongPress = {
                    selectArtMobilityVM.setSelectedArticl(article = it.article, tvaList = tvaList)
                    mainViewModel.onShowAlertDialogChange(true)
                },
                onSwipeToDelete = { selectArtMobilityVM.deleteItemToSelectedArticleMobilityList(it.article) },
                onTap = { selectArtMobilityVM.onShowSetArticleChange(true) },
                firstColumn = { item->
                    articleMapByBarCode[item.article.aRTCode]?.aRTDesignation?: context.resources.getString(R.string.article_introvable, " ("+ item.article.aRTCode+")")
                },
                secondColumn = {
                    //StringUtils.convertStringToPriceFormat(it.prixCaisse)
                   // removeTrailingZeroInDouble(
                        convertStringToDoubleFormat(it.prixCaisse)
                 //   )
                },
                thirdColumn = { removeTrailingZeroInDouble(it.quantity) },
                forthColumn = {
                        convertStringToDoubleFormat(
                        CalculationsUtils.totalPriceArticle(
                            price = it.prixCaisse,
                            quantity = it.quantity
                        )
                    )
                },
                infoText = { TableTextUtils.infoText(selectedArticle = it) }
            )






        }


    }
}


























