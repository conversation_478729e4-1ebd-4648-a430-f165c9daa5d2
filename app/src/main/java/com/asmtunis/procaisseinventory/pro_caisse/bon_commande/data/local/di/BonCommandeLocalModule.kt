package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.bon_commande.dao.BonCommandeDAO
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.bon_commande.repository.BonCommandeLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.bon_commande.repository.BonCommandeLocalRepositoryImpl
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.ligne_bon_commande.dao.LigneBonCommandeDAO
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.ligne_bon_commande.repository.LigneBonCommandeLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.ligne_bon_commande.repository.LigneBonCommandeLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton




    @Module
    @InstallIn(SingletonComponent::class)
    class BonCommandeLocalModule {

        @Provides
        @Singleton
        fun provideBonCommandeDao(
            proCaisseDataBase: ProCaisseDataBase
        ) = proCaisseDataBase.bonCommandeDAO()

        @Provides
        @Singleton
        @Named("BonCommande")
        fun provideBonCommandeRepository(
            bonCommandeDAO: BonCommandeDAO
        ): BonCommandeLocalRepository = BonCommandeLocalRepositoryImpl(bonCommandeDAO = bonCommandeDAO)



        @Provides
        @Singleton
        fun provideLigneBonCommandeDao(
            proCaisseDataBase: ProCaisseDataBase
        ) = proCaisseDataBase.ligneBonCommandeDAO()

        @Provides
        @Singleton
        @Named("LigneBonCommande")
        fun provideLigneBonCommandeRepository(
            ligneBonCommandeDAO: LigneBonCommandeDAO
        ): LigneBonCommandeLocalRepository = LigneBonCommandeLocalRepositoryImpl(ligneBonCommandeDAO = ligneBonCommandeDAO)



    }