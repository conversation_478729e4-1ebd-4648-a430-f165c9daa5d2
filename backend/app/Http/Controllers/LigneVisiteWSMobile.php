<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;

class LigneVisiteWSMobile extends Controller
{
    	
	
		
	/*fonction get data from database*/
	 
	public function getLigneVisite(Request $request)
	 {
	 	 $data = $request->json()->all();

	 	$connection = DatabaseConnection::setConnection($data);
	 	return response()->json($connection->table('LigneVisite')->get());
	 }
	
	public function getLigneVisiteByCarnetId(Request $request)
	 {
	 	 $data = $request->json()->all();

	 	$connection = DatabaseConnection::setConnection($data);
	 	return response()->json($connection->table('LigneVisite')->where('LT_IdCarnet', $data["object"])->get());

	 }


public function getLigneVisiteByVisite(Request $request)
	 {
	 	 $data = $request->json()->all();

	 	$connection = DatabaseConnection::setConnection($data);
	 	return response()->json($connection->table('LigneVisite')
	 		->where('LT_NumTicket', $data["object"]["TIK_NumTicket"])
	 		->where('LT_IdCarnet', $data["object"]["TIK_IdCarnet"])
	 		->where('LT_Exerc', $data["object"]["TIK_Exerc"])
	 		->get());

	 }

public function getLigneVisiteByVisites(Request $request)
	 {
	 	 $data = $request->json()->all();

	 	$connection = DatabaseConnection::setConnection($data);
	 /*	return response()->json($connection->table('LigneTicket')
	 		->where('LT_NumTicket', $data["object"]["TIK_NumTicket"])
	 		->where('LT_IdCarnet', $data["object"]["TIK_IdCarnet"])
	 		->where('LT_Exerc', $data["object"]["TIK_Exerc"])
	 		->get());*/
   $items=null;

    foreach ($data["object"] as &$item) {
         	  $items[] =   $connection->table('LigneVisite')
	 		->where('LT_NumTicket', $item["TIK_NumTicket"])
	 		->where('LT_IdCarnet', $item["TIK_IdCarnet"])
	 		->where('LT_Exerc', $item["TIK_Exerc"])
	->get();



         }


	return response()->json($items);
	 }





	
	 
	 public function addLigneVisite(Request $request)
    {
              $data = $request->json()->all();

	 	$connection = DatabaseConnection::setConnection($data);
		
		if ( !empty ( $data ) ) {
		$t = 	$connection->table('LigneVisite')->insert($data);
		
			return response()->json($t);
			
			}
		else{
			return(false);
		}
		
    }
	 

	public function updateLigneVisite(Request $request )
    {
      $data = $request->json()->all();

	 	$connection = DatabaseConnection::setConnection($data);

		if ( !empty ( $data ) ) {
      
	$response =	$connection->table('LigneTicket')->where('LT_NumTicket', $data["LT_NumTicket"])->where('LT_IdCarnet', $data["LT_IdCarnet"])->where('LT_Exerc', $data["LT_Exerc"])->where('LT_CodArt', $data["LT_CodArt"])->where('LT_Unite', $data["LT_Unite"])->update($data);


	return response()->json(($response == 1) ? true: false);

			}
		else{
			return response()->json(false);
		}
    
		
    }


	
	public function deleteLigneVisite(Request $request)
    {
         $data = $request->json()->all();

	 	$connection = DatabaseConnection::setConnection($data);
		if ( !empty ( $data ) ) {
        $data["LT_Annuler"] = true;

	$response =	$connection->table('LigneTicket')->where('LT_NumTicket', $data["LT_NumTicket"])->where('LT_IdCarnet', $data["LT_IdCarnet"])->where('LT_Exerc', $data["LT_Exerc"])->where('LT_CodArt', $data["LT_CodArt"])->where('LT_Unite', $data["LT_Unite"])->update($data);


	return response()->json(($response == 1) ? true: false);

			}
		else{
			return response()->json(false);
		}    }

	
}





