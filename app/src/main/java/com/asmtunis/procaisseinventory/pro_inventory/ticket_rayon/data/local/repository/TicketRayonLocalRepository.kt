package com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.local.repository

import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.domaine.TicketRayon
import kotlinx.coroutines.flow.Flow


interface TicketRayonLocalRepository {
        fun upsertAll(value: List<TicketRayon>)
        fun upsert(value: TicketRayon)
        fun deleteAll()
      fun delete(value: TicketRayon)

    fun getAll(): Flow<List<TicketRayon>>
    fun notSync(): Flow<List<TicketRayon>>

    fun updateSyncTicketRayon(code: String)
}