<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;

class BanqueWSMobile extends Controller
{
    	
	
	
	public function getBanque(Request $request)
	 {  $data = $request->json()->all();

	 	$connection = DatabaseConnection::setConnection($data);
		 
	return response()->json($connection->table('banque')->get());		

	 }
	


	
}





