package com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.remote.api.ligne_inv

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire
import kotlinx.coroutines.flow.Flow


interface LigneInventaireApi {

        companion object {

         //   var URL_GET_LIGNE_INVENTAIRE_BY_CODE = "${Globals.BASE_URL}/LigneInventaire/getLigneInventaires"

        }

        suspend fun getLigneInventaires(baseConfig: String): Flow<DataResult<List<LigneInventaire>>>
        suspend fun getLigneInventairesByCode(baseConfig: String,code:String): Flow<DataResult<List<LigneInventaire>>>
}