package com.asmtunis.procaisseinventory.pro_inventory.update

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.twotone.ArrowBack
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.core.utils.UpdateLoadingStateUtils
import com.asmtunis.procaisseinventory.core.utils.UpdateLoadingStateUtils.handleInventoryClick
import com.asmtunis.procaisseinventory.core.utils.UpdateLoadingStateUtils.isInventoryUpdateLoading
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import kotlinx.coroutines.launch


@Composable
fun ProInventoryUpdateDataScreen(
    navigate: (route: String) -> Unit,
    navigatePopUpTo: (route: String, popUpTo: String, isInclusive: Boolean) -> Unit,
    popBackStack: () -> Unit,
    navigateUp: () -> Unit,
    settingViewModel: SettingViewModel,
    navDrawerViewModel: NavigationDrawerViewModel,
    mainViewModel: MainViewModel,
    dataViewModel: DataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    networkViewModel: NetworkViewModel,
) {
    val uiWindowState = settingViewModel.uiWindowState
    val utilisateur = mainViewModel.utilisateur
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val scope = rememberCoroutineScope()
    val isConnected = networkViewModel.isConnected // use collectAsState for better lifecycle handling
    val listUpdate = InventoryUpdateDataType.entries // use enum values for typesafe usage

    val isLoading = UpdateLoadingStateUtils.isLoadingInventoryData(getProInventoryDataViewModel = getProInventoryDataViewModel)
            || UpdateLoadingStateUtils.isLoadingSharedData(getSharedDataViewModel = getSharedDataViewModel)

    Scaffold(
        topBar = {
            AppBar(
                showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                baseConfig = selectedBaseconfig,
                isConnected = isConnected,
                onNavigationClick = { popBackStack() },
                navIcon = Icons.AutoMirrored.TwoTone.ArrowBack,
                title = stringResource(id = R.string.sync_title),
            )
        }
    ) { padding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            OutlinedButton(
                modifier = Modifier.fillMaxWidth().padding(horizontal = 16.dp),
                shape = MaterialTheme.shapes.medium,
                enabled = isConnected && !isLoading,
                onClick = {
                    scope.launch {
                        launch { getProInventoryDataViewModel.getProInventoryData(
                            baseConfig = selectedBaseconfig,
                            utilisateur = utilisateur
                        )}
                        launch {
                            getSharedDataViewModel.getSharedData(
                            baseConfig = selectedBaseconfig,
                            utilisateur = utilisateur
                        )
                            getSharedDataViewModel.getCommenSharedData(baseConfig = selectedBaseconfig)
                        }

                    }
                }
            ) {
                Text(text = stringResource(id = R.string.update_db_title))
                if(isLoading) LottieAnim(lotti = R.raw.loading, size = 25.dp)
            }

            Spacer(modifier = Modifier.height(12.dp))

            LazyVerticalStaggeredGrid(
                columns = StaggeredGridCells.Fixed(2),
                modifier = Modifier.padding(horizontal = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalItemSpacing = 16.dp,
            ) {
                items(
                    count = listUpdate.size,
                    key = { it }
                ) { index ->
                    val item = listUpdate[index]
                    val isNotLoading = !isInventoryUpdateLoading(
                        item = item,
                        getProInventoryDataViewModel = getProInventoryDataViewModel,
                        getSharedDataViewModel = getSharedDataViewModel
                    )
                    OutlinedButton(
                        onClick = {
                            handleInventoryClick(
                                item = item,
                                baseConfig = selectedBaseconfig,
                                utilisateur = utilisateur,
                                getProInventoryDataViewModel = getProInventoryDataViewModel,
                                getSharedDataViewModel = getSharedDataViewModel
                            )
                        },
                        shape = MaterialTheme.shapes.medium,
                        enabled = isConnected && isNotLoading
                    ) {
                         Text(text = item.displayName)
                        if (!isNotLoading) LottieAnim(lotti = R.raw.loading, size = 25.dp)

                    }
                }
            }
        }
    }
}






