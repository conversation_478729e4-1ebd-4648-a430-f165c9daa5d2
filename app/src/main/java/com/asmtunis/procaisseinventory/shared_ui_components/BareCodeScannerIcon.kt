package com.asmtunis.procaisseinventory.shared_ui_components

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.dokar.sonner.ToastType
import com.dokar.sonner.ToasterState
import com.google.accompanist.permissions.ExperimentalPermissionsApi

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun BareCodeScannerIcon(haveCameraDevice: Boolean, toaster: ToasterState, onClick: () -> Unit) {
    val context = LocalContext.current
    AskPermission(
        permission = listOf(
            android.Manifest.permission.CAMERA
        ),
        permissionNotAvailableContent = { permissionState ->
            LottieAnim(
                lotti = R.raw.barcode_scanner,
                size = 30.dp,
                onClick = {
                    permissionState.launchMultiplePermissionRequest()
                }
            )

        },
        content = {
            LottieAnim(
                lotti = R.raw.barcode_scanner,
                size = 30.dp,
                onClick = {
                     if(haveCameraDevice) {
                         onClick()
                     } else {
                         showToast(
                             context = context,
                             toaster = toaster,
                             message = context.resources.getString(R.string.device_with_no_camera),
                             type = ToastType.Info,
                         )
                     }

                }
            )
        }
    )
}