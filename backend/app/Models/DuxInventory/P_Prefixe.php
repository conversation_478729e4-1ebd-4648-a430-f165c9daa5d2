<?php

namespace App\Models\DuxInventory;



class P_Prefixe extends BaseModel
{
    protected $connection = 'onthefly';
    protected $table = 'P_Prefixe';
    public $incrementing = false;
    public $timestamps = false;
    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->useCode = false;
    }

    public static function getCode($idClasse)
    {
       $cc = P_Prefixe::where('idClasse', $idClasse)->get();

        if (sizeof($cc) == 1) {
            $prefixe = $cc[0]->prefixe;

            $compteur = $cc[0]->compt;

            $longeur_cmp = strlen((string)$compteur);

            $longeur = $cc[0]->longeurCode;

            // $longeur = $cc[0]->longeurCode;

            if ($longeur == $longeur_cmp) {
                $idtable = $prefixe . $compteur;

            } else if ($longeur_cmp < $longeur) {
                $dif = (int)$longeur - $longeur_cmp;
                $null = "";

                for ($i = 1; $i <= $dif; $i++) {
                    $null = $null . "0";
                }
                $idtable = $prefixe . $null . $compteur;

            }
        } else {
            $idtable = null;
        }
        return $idtable;

    }

    public function updateCompteur($idClasse)
    {
        $compteur = 1;
        $cc = P_Prefixe::where('idClasse', $idClasse)->get();

        $compteur += $cc[0]->compt;

        $cmp = array(
            'compt' => $compteur,
        );
        $P_Prefixe_id = P_Prefixe::where('idClasse', $idClasse)->update($cmp);
    }
}
