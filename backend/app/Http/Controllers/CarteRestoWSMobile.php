<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;

class CarteRestoWSMobile extends Controller
{


    public function getCarteResto(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Carte_Resto')->get());

    }


    public function getCarteRestoByCode(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Carte_Resto')->where('Code', $data["object"])->first());

    }

}





