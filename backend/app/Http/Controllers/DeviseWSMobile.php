<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use App\Http\Requests;
use Illuminate\Http\Request;

class DeviseWSMobile extends Controller
{


    /*fonction get data from database*/

    public function getDevise(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
      $query =  $connection->table('Devise');


        return response()->json($query->get());
    }


    public function getDeviseByX(Request $request)
    {
        return response()->json(DB::table('Devise')->where($request->field, $request->value)->get());

    }

    public function getDeviseByActive(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Devise')->where('Principale', 1)->where('Activite', 1)->get()->first());

    }


}





