package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.ligne_bn_livraison.repository

import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraison
import kotlinx.coroutines.flow.Flow


interface LigneBonLivraisonLocalRepository {
    fun upsertAll(value: List<LigneBonLivraison>)
    fun upsert(value: LigneBonLivraison)
    fun deleteAll()

    fun deleteList(bonLivraisonList: List<LigneBonLivraison>)
    fun updateState(code: String, codeM:String, exercice : String)
    fun deleteByCodeAndCodeArticle(num: String, exercice: String, codeArticle : String)
    fun getByCode(num: String, exercice: String): Flow<List<LigneBonLivraison>>

}
