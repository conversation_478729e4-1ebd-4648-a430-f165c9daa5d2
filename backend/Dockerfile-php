FROM registry.asmtechtn.com/docker-images/php:latest

RUN curl -L https://www.openssl.org/source/openssl-1.1.1p.tar.gz --output openssl.tar.gz
RUN tar -zxvf openssl.tar.gz
RUN rm openssl.tar.gz
RUN mv openssl* openssl
WORKDIR /var/www/openssl
RUN ls -lai
RUN sh config
RUN make
RUN make install
RUN ldconfig



#WORKDIR /var/www/openssl
#RUN ls -lai
#RUN sh config
#RUN make
#RUN make install
#RUN ldconfig

# Set working directory
WORKDIR /var/www

# Add user for laravel application
RUN groupadd -g 1000 www
RUN useradd -u 1000 -ms /bin/bash -g www www
RUN chown -R www:www /var/www

# Copy existing application directory permissions
COPY --chown=www:www . /var/www
COPY ./docker/command.sh /var/www/command.sh

RUN composer config --no-plugins allow-plugins.php-http/discovery false
#RUN composer update
RUN composer install
RUN composer dump-autoload

RUN php artisan version:timestamp
# Expose port 9000 and start php-fpm server
EXPOSE 9000

CMD bash /var/www/command.sh

RUN sed -i 's/\r$//' /var/www/command.sh
