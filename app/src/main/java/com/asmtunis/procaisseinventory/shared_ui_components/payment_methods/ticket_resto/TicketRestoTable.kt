@file:OptIn(ExperimentalMaterial3Api::class, ExperimentalMaterial3Api::class)

package com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.ticket_resto

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.data.carte_resto.domaine.CarteResto
import com.asmtunis.procaisseinventory.data.ticket_resto.domaine.TraiteCaisse
import com.simapps.ui_kit.ModifiersUtils.detectTapGestures
import kotlinx.coroutines.launch


@Composable
fun TicketRestoTable(
    listTraiteCaisse : List<TraiteCaisse>,
    onTap: (index : Int, carteResto : CarteResto) -> Unit,
    onLongPress: (Int) -> Unit,
    onShowTicketRestoTableChange: (Boolean) -> Unit,
    carteResto :(Int)-> CarteResto
) {
    val context = LocalContext.current
    val rowTitls = context.resources.getStringArray(R.array.ticketRestoTable_array).toList()

    val listState = rememberLazyListState()

    val scope = rememberCoroutineScope()
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    ModalBottomSheet(
        sheetState = sheetState,
        onDismissRequest = {
            scope.launch {
                sheetState.hide()
            }
            onShowTicketRestoTableChange(false)
        },
    ) {
        Column(
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            HorizontalDivider(
                thickness = 2.dp,
                color = MaterialTheme.colorScheme.outline
            )
            TicketRestoTableHeader(rowTitls = rowTitls)
            HorizontalDivider(
                thickness = 2.dp,
                color = MaterialTheme.colorScheme.outline
            )



            LazyColumn(// modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
                state = listState
            ) {
                items(
                    count = listTraiteCaisse.size,
                    /* key = {
                         ligneBonEntree[it].id
                     }*/
                ) { index ->

                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        modifier = Modifier
                            .padding(start = 3.dp, end = 3.dp)
                            .heightIn(40.dp, 150.dp)
                            .fillMaxWidth()
                            .detectTapGestures(
                                key1 = carteResto(index),
                                onPress = {


                                },
                                onDoubleTap = {

                                },
                                onLongPress = {

                                    onLongPress(index)
                                },
                                onTap = {
                                    onTap(index, carteResto(index))
                                }
                            )

                    ) {
                        Text(
                            text = listTraiteCaisse[index].tRAITType?:"N/A",
                            textAlign = TextAlign.Center,
                            fontSize = MaterialTheme.typography.bodySmall.fontSize,
                            modifier = Modifier.fillMaxWidth(0.4f)
                        )


                        Text(
                            text = listTraiteCaisse[index].nbrTicket.toString()+" x "+ StringUtils.convertStringToPriceFormat(listTraiteCaisse[index].montantInitial.toString())+" = "+ StringUtils.convertStringToPriceFormat(listTraiteCaisse[index].tRAITMontant.toString()) +" ("+listTraiteCaisse[index].taux + "% )",
                            fontSize = MaterialTheme.typography.bodySmall.fontSize,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth(0.6f)
                        )



                    }



                    HorizontalDivider(color = MaterialTheme.colorScheme.outline)
                }
            }

            Spacer(modifier = Modifier.height(55.dp))

        }
    }




}

@Composable
fun TicketRestoTableHeader(rowTitls : List<String>) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceEvenly,
        modifier = Modifier
            .heightIn(45.dp)
            .fillMaxWidth()
            //  .background(color = MaterialTheme.colorScheme.scrim)
            .clickable {
            }
    ) {

        Text(
            text = rowTitls.first(),
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth(0.4f)
        )
        Text(
            text = rowTitls[1],
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth(0.6f)
        )


    }
}