package com.asmtunis.procaisseinventory.pro_caisse.global_data_class

import com.asmtunis.procaisseinventory.articles.data.article.remote.api.ArticlesApi
import com.asmtunis.procaisseinventory.articles.data.priceperstation.remote.api.PricePerStationApi
import com.asmtunis.procaisseinventory.articles.data.unite_article.remote.api.UniteArticlesApi
import com.asmtunis.procaisseinventory.auth.base_config.data.remote.api.BaseConfigApi
import com.asmtunis.procaisseinventory.auth.login.data.remote.api.LoginApi
import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.remote.api.LicenceApi
import com.asmtunis.procaisseinventory.data.banques.remote.api.BanqueApi
import com.asmtunis.procaisseinventory.data.carte_resto.remote.api.CarteRestoApi
import com.asmtunis.procaisseinventory.data.cheque_caisse.remote.api.ChequeCaisseApi
import com.asmtunis.procaisseinventory.data.devise.remote.api.DeviseApi
import com.asmtunis.procaisseinventory.data.etablisement.remote.api.EtablissementApi
import com.asmtunis.procaisseinventory.data.exercice.remote.api.ExerciceApi
import com.asmtunis.procaisseinventory.data.facture.remote.api.FactureApi
import com.asmtunis.procaisseinventory.data.parametrages.remote.api.ParametragesApi
import com.asmtunis.procaisseinventory.data.prefixe.remote.api.PrefixApi
import com.asmtunis.procaisseinventory.data.sessioncaisse.remote.api.SessionCaisseApi
import com.asmtunis.procaisseinventory.data.statistiques.remote.api.StatisticsApi
import com.asmtunis.procaisseinventory.data.ticket_resto.remote.api.TraiteCaisseApi
import com.asmtunis.procaisseinventory.data.timbre.remote.api.TimbreApi
import com.asmtunis.procaisseinventory.data.ville.remote.api.VilleApi
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.api.bon_commande.BonCommandeApi
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.api.ligne_bon_commande.LigneBonCommandeApi
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.remote.api.ligne_ticket.LigneTicketApi
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.remote.api.ticket.TicketApi
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.remote.api.bon_retour.BonRetourApi
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.remote.api.ligne_bon_retour.LigneBonRetourApi
import com.asmtunis.procaisseinventory.pro_caisse.client.data.remote.api.ClientsApi
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.remote.api.DistributionNumeriqueApi
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.remote.api.DeplacementOutByUserApi
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.remote.api.ImmobilisationApi
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.remote.api.InventaireBatimentApi
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.remote.api.InventairePatrimoineApi
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.remote.api.ReglementCaisseApi
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.remote.api.OrdreMissionApi
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.remote.api.VeilleConcurentielApi
import javax.inject.Inject


data class ProCaisseRemote @Inject constructor(
    val baseConfig: BaseConfigApi,
    val immobilisation: ImmobilisationApi,
    val login: LoginApi,
    val clients: ClientsApi,
    val licence: LicenceApi,
    val articles: ArticlesApi,
    val uniteArticles: UniteArticlesApi,
    val etablissement: EtablissementApi,
    val banque: BanqueApi,
    val facture: FactureApi,
    val devise: DeviseApi,
    val carteResto: CarteRestoApi,
    val chequeCaisse: ChequeCaisseApi,
    val exercice: ExerciceApi,
    val statistics: StatisticsApi,
    val ticketResto: TraiteCaisseApi,
    val timbre: TimbreApi,
    val parametrages: ParametragesApi,
    val prefix: PrefixApi,
    val pricePerStation: PricePerStationApi,
    val sessionCaisse: SessionCaisseApi,
    val distributionNumerique: DistributionNumeriqueApi,
    val veilleConcurentiel: VeilleConcurentielApi,
    val reglementCaisse: ReglementCaisseApi,
    val ville: VilleApi,

    //INVENTAIRE PATRIMOINE
    val inventairePatrimoine: InventairePatrimoineApi,

    //INVENTAIRE BATIMENT
    val inventaireBatiment: InventaireBatimentApi,
    val provideDeplacementOutByUserApi: DeplacementOutByUserApi,

    // BON COMMANDE
    val bonCommande: BonCommandeApi,
    val ligneBonCommande: LigneBonCommandeApi,
   // BON LIVRAISON
    val ticket: TicketApi,
    val ligneTicket: LigneTicketApi,
   //BON RETOUR
    val bonRetour: BonRetourApi,
    val ligneBonRetour: LigneBonRetourApi,

    //ORDRE MISSION ************** TOURNEE
    val ordreMission: OrdreMissionApi,
)