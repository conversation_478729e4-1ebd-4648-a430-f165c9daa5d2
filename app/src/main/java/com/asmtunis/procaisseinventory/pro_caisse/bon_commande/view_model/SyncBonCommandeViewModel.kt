package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.model.NestedItem
import com.asmtunis.procaisseinventory.core.sync.SyncEntity
import com.asmtunis.procaisseinventory.core.sync.SyncManager
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.InvPatBatchResponse
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject


@HiltViewModel
class SyncBonCommandeViewModel @Inject constructor(
    private val syncManager: SyncManager,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
) : ViewModel() {

    // Sync state from SyncManager
    val syncState = syncManager.syncState

    init {
        observeUnsyncedBonCommande()
    }

    /**
     * Observe unsynchronized BonCommande from the database
     */
    private fun observeUnsyncedBonCommande() {
        viewModelScope.launch {
            proCaisseLocalDb.bonCommande.notSynced()
                .distinctUntilChanged()
                .collectLatest { bonCommandes: Map<BonCommande, List<LigneBonCommande>>? ->
                    bonCommandeNotSync = bonCommandes ?: emptyMap()
                }
        }
    }

    /**
     * Trigger manual sync for BonCommande entities
     */
    fun triggerManualSync() {
        syncBonCommande()
    }

    /**
     * Check if sync is currently in progress
     */
    val isSyncing: Boolean
        get() = responseAddBonCommandeState.loading

    /**
     * Get the current sync error if any
     */
    val syncError: String?
        get() = responseAddBonCommandeState.error

    /**
     * Get the count of unsynchronized items
     */
    val unsyncedCount: Int
        get() = bonCommandeNotSync.size


    var responseAddBonCommandeState: RemoteResponseState<List<InvPatBatchResponse>>  by mutableStateOf(RemoteResponseState())
        private set


    var bonCommandeNotSync: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
        private set
    var notSyncBonCommandeObj : String by mutableStateOf("")
        private set






    fun syncBonCommande(selectedBonCommande: BonCommande = BonCommande()) {
        viewModelScope.launch {
            try {
                responseAddBonCommandeState = RemoteResponseState(data = null, loading = true, error = null)

                if (selectedBonCommande == BonCommande() && bonCommandeNotSync.isEmpty()) {
                    // Use SyncManager for bulk sync
                    val result = syncManager.syncEntity(SyncEntity.BON_COMMANDE)

                    if (result.isSuccess) {
                        responseAddBonCommandeState = RemoteResponseState(
                            data = emptyList(),
                            loading = false,
                            error = null
                        )
                    } else {
                        responseAddBonCommandeState = RemoteResponseState(
                            data = null,
                            loading = false,
                            error = result.error ?: "Sync failed"
                        )
                    }
                } else {
                    // Use legacy method for specific items
                    syncSpecificBonCommande(selectedBonCommande)
                }
            } catch (exception: Exception) {
                responseAddBonCommandeState = RemoteResponseState(
                    data = null,
                    loading = false,
                    error = exception.message ?: "Unknown error"
                )
            }
        }
    }

    private suspend fun syncSpecificBonCommande(selectedBonCommande: BonCommande) {
        // Check network connectivity
        if (!syncState.value.isNetworkConnected) {
            responseAddBonCommandeState = RemoteResponseState(
                data = null,
                loading = false,
                error = "No network connection available."
            )
            return
        }

        viewModelScope.launch(dispatcherIO) {
            val listVisiteWithLinesDn = ArrayList<NestedItem<BonCommande, List<LigneBonCommande>>>()

            bonCommandeNotSync.forEach { (key, value) ->
                run {
                    listVisiteWithLinesDn.add(
                        NestedItem(
                            parent  = key,
                            children  = value
                        )
                    )
                }
            }

            if(selectedBonCommande != BonCommande()) {
                listVisiteWithLinesDn.removeIf { it.parent?.devCodeM != selectedBonCommande.devCodeM }
            }

            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                    ?.let { Json.decodeFromString(it) }?: BaseConfig(),
                Json.encodeToJsonElement(listVisiteWithLinesDn)
            )

            notSyncBonCommandeObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.bonCommande.addBatchBonCommande(notSyncBonCommandeObj).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        //val ddm =  DateUtils.getCurrentDateTime() TODO MAYBE UPDATE DDM ALSO §
                        //"message":"Bon Commande Existe Deja","code":10701
                        for (i in result.data!!.indices) {
                            proCaisseLocalDb.bonCommande.setSynced(
                                bonCommandeNum = result.data[i].dEVNum,
                                bonCommandeNumM = result.data[i].dEVCodeM
                            )

                            proCaisseLocalDb.ligneBonCommande.setSynced(
                                newNum = result.data[i].dEVNum,
                                oldNum = result.data[i].dEVCodeM
                            )
                        }

                        responseAddBonCommandeState = RemoteResponseState(data = result.data, loading = false, error = null)
                    }

                    is DataResult.Loading -> {
                        responseAddBonCommandeState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        responseAddBonCommandeState = RemoteResponseState(data = null, loading = false, error = result.message, message = selectedBonCommande.dEVNum)
                    }

                    else -> {
                        responseAddBonCommandeState = RemoteResponseState(data = null, loading = false, error = "Unknow Error")

                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }
}


