<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use App\Helpers\Enum;
use App\Models\Ticket;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class FactureMobileController extends Controller
{
    //get all data

    public function getFacture(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);

        $data = $connection->table('facture');
        if ($request->hasHeader('user')) {

            $data = $data->where("FACT_user", "=", $request->header('user'));
        }


        $data = $data->get();


        return response()->json($data);
    }

    // Add Facture
    public function addBatchFactureWithLines(Request $request)
    {
        $result = [];
        // get all data
        $items = $request->json()->all();

        $date = Carbon::now();
        $connection = DatabaseConnection::setConnection($items);
        $TicketController = new TicketWSMobile();

        if (!empty ($items)) {
            //choose the connection
            $ligneFactureMobile = new LigneFactureMobileController();
            foreach ($items['object'] as $data) {
                $connection->beginTransaction();
                try {

                    //check if this facture exit or not
                    $year = Carbon::now()->format('y');
                    $month = Carbon::now()->format('m');
                    //Genérer Un Nouveau Prefix Facture
                    $num_fact = (new PrefixWSMobile())->getFacturePrefix($connection, $year, $month);
                    $testNum_Fact = $connection->table('facture')->where("FACT_Num", $num_fact)->first();
                    // verifier si FACT_Num existe dans table facture
                    if (!empty($testNum_Fact)) {
                        $soldeClient = Ticket::SoldeClient($connection, $data['ticket']['TIK_CodClt']);
                        $result[] = $this->setResult($data['ticket']['TIK_NumTicket'], $data['ticket']['TIK_IdCarnet'],
                            $data['ticket']['TIK_Exerc'], $data['ticket']['TIK_NumTicket_M'],
                            $num_fact, $data['ticket']['TIK_CodClt'], $soldeClient['SoldeClient'], $soldeClient['Debit'], $soldeClient['Credit'], null, "FACT_Num existe", Enum::Error_FACT_Num_EXIST
                        );
                        Log::error("FACT_Num Existe", $result);
                        throw new Exception("", Enum::Error_FACT_Num_EXIST);
                    }

                    $exist = $connection->table('ticket')
                        ->whereNull('TIK_NumeroBL');

                    $exist = $exist->where('TIK_NumTicket_M', '=', $data['ticket']['TIK_NumTicket_M'])
                        ->where('TIK_IdCarnet', '=', $data['ticket']["TIK_IdCarnet"])
                        ->where('TIK_Exerc', '=', $data['ticket']['TIK_Exerc'])
                        ->first();

                    if ($exist) {

                        // get client
                        $client = $connection->table('client')
                            ->where('CLI_Code', '=', $data['ticket']['TIK_CodClt'])->first();
                        //timbre
                        if (!!$data['ticket']['TIK_Timbre']) {
                            $timbre = intval($data['ticket']['TIK_Timbre']);
                            $timbre_value = $connection->table('timbre')->where('TIMB_Code', '=', $timbre)->pluck('TIMB_Value')->first();
                            $data['ticket']['TIK_Timbre'] = (double)$timbre_value;
                        }
                        //insert Facture


                        $dataInserted = [
                            'FACT_Num' => $num_fact,
                            'FACT_Exerc' => $exist->TIK_Exerc,
                            'FACT_CodeClient' => $exist->TIK_CodClt,
                            'FACT_Date' => $date,
                            'FACT_DDm' => $date,
                            'FACT_NomPrenomCli' => $client->CLI_NomPren,
                            'FACT_AdresseCli' => $client->CLI_Adresse,
                            'FACT_MatFisc' => $client->CLI_MatFisc,
                            'FACT_ExoNum' => '',
                            'FACT_ExoVal' => '',
                            'FACT_MntHT' => $exist->TIK_MtHT + $exist->TIK_MtRemise,
                            'FACT_TauxRemise' => $exist->TIK_TauxRemise,
                            'FACT_MntRemise' => $exist->TIK_MtRemise,
                            'FACT_MntNetHT' => $exist->TIK_MtHT,
                            'FACT_MntTva' => $exist->TIK_MtTVA,
                            'FACT_Timbre' => $data['ticket']['TIK_Timbre'],
                            'FACT_MntTTC' => $data['ticket']['TIK_MtTTC'],
                            'FACT_Etat' => $exist->TIK_Etat,
                            'FACT_Type' => 'Client',
                            'FACT_Regler' => '',
                            'FACT_Exonoration' => 0,
                            'FACT_TypeReg' => '',
                            'FACT_ValiditeTraite' => '',
                            'FACT_RetSource' => 0,
                            'FACT_NumBC' => $exist->TIK_NumTicket,
                            'FACT_user' => $exist->TIK_user,
                            'FACT_station' => $exist->TIK_station,
                            'FACT_export' => $exist->TIK_export,
                            'FACT_FactFournisseur' => '',
                            'FACT_Total_HTVA' => 0,
                            'FACT_Total_HDC' => 0,
                            'FACT_Comptabiliser' => '',
                            'FACT_Reg' => '',
                        ];

                        if (Schema::connection("onthefly")->hasColumn('facture', 'FACT_MntRevImp')) {

                            $dataInserted['FACT_MntRevImp'] = $data['ticket']['Mnt_RevImp'] ?? null;

                        }


                        $inserted = $connection->table('facture')->insert($dataInserted);


                        //Get lignes de cette Ticket
                        $lignesTickets = $connection->table('LigneTicket')
                            ->where('LT_Exerc', '=', $exist->TIK_Exerc)
                            ->where('LT_IdCarnet', '=', $exist->TIK_IdCarnet)
                            ->where('LT_NumTicket', '=', $exist->TIK_NumTicket)
                            ->get();


                        // insert ligne Facture
                        if (!$inserted) {
                            $soldeClient = Ticket::SoldeClient($connection, $data['ticket']['TIK_CodClt']);
                            $result[] = $this->setResult((integer)$exist->Tik_NumTicket,
                                $exist->TIK_IdCarnet,
                                $exist->TIK_Exerc, $exist->TIK_NumTicket_M,
                                $num_fact, $data['ticket']['TIK_CodClt'], $soldeClient['SoldeClient'], $soldeClient['Debit'], $soldeClient['Credit'], null,
                                "Erreur dans l'insertion Facture", Enum::Error_Insert_Facture
                            );
                            Log::error("Erreur dans l'insertion Facture", $result);
                            throw new Exception("", Enum::Error_Insert_Facture);
                        } else {

                            $error_code = $ligneFactureMobile->addBatchLigneFacturesData($connection, $lignesTickets, $num_fact, $exist);
                            //Update Ticket
                            $connection->table('Ticket')
                                ->where('TIK_NumTicket_M', $data['ticket']['TIK_NumTicket_M'])
                                ->where('TIK_IdCarnet', '=', $data['ticket']["TIK_IdCarnet"])
                                ->where('TIK_Exerc', '=', $data['ticket']['TIK_Exerc'])
                                ->update([
                                    'TIK_NumeroBL' => $num_fact
                                ]);
                            $ticketFacture = $connection->table('Ticket')
                                ->where('TIK_NumTicket_M', '=', $data['ticket']['TIK_NumTicket_M'])
                                ->where('TIK_IdCarnet', '=', $data['ticket']["TIK_IdCarnet"])
                                ->where('TIK_Exerc', '=', $data['ticket']['TIK_Exerc'])
                                ->first();
                            if ($error_code == Enum::Success_Code) {
                                $soldeClient = Ticket::SoldeClient($connection, $data['ticket']['TIK_CodClt']);
                                $observation = $TicketController->verif_bc_exite($exist, $connection);
                                $factureInserted = $connection->table('facture')
                                    ->where('FACT_Num', $num_fact)->first();
                                $result[] = $this->setResultFacture((integer)$ticketFacture->TIK_NumTicket,
                                    $ticketFacture->TIK_IdCarnet, $ticketFacture->TIK_Exerc, $ticketFacture->TIK_NumTicket_M,
                                    $ticketFacture->TIK_NumeroBL, $data['ticket']['TIK_CodClt'],
                                    $soldeClient['SoldeClient'], $soldeClient['Debit'], $soldeClient['Credit'], $observation, "INSERTED", Enum::Success_Code
                                    , $factureInserted);

                            } else {
                                $soldeClient = Ticket::SoldeClient($connection, $data['ticket']['TIK_CodClt']);
                                $result[] = $this->setResult((integer)$num_fact, $data['ticket']['TIK_IdCarnet'],
                                    $data['ticket']['TIK_Exerc'], $data['ticket']['TIK_NumTicket_M'],
                                    null, $data['ticket']['TIK_CodClt'], $soldeClient['SoldeClient'], $soldeClient['Debit'], $soldeClient['Credit'], null,
                                    "Error au niveau d'insertion des lignes facture", Enum::Error_Insert_Facture
                                );
                                Log::error("Error au niveau d'insertion des lignes facture", $result);
                                throw new Exception("", Enum::Error_Insert_Facture);

                            }

                        }


                    } else {

                        $ticket = $connection->table('ticket')
                            ->whereNotNull('TIK_NumeroBL')
                            ->where('TIK_NumTicket_M', '=', $data['ticket']['TIK_NumTicket_M'])
                            ->where('TIK_IdCarnet', '=', $data['ticket']["TIK_IdCarnet"])
                            ->where('TIK_Exerc', '=', $data['ticket']['TIK_Exerc'])
                            ->first();

                        if (!!$ticket) {
                            //get this facture
                            $facture = $connection->table('facture')
                                ->where('FACT_Num', '=', $ticket->TIK_NumeroBL)->first();

                            // if this facture exist return it
                            if (!!$facture) {
                                $observation = $TicketController->verif_bc_exite($ticket, $connection);
                                $soldeClient = Ticket::SoldeClient($connection, $data['ticket']['TIK_CodClt']);
                                $result[] = $this->setResult($ticket->TIK_NumTicket,
                                    $ticket->TIK_IdCarnet, $ticket->TIK_Exerc, $ticket->TIK_NumTicket_M,
                                    $ticket->TIK_NumeroBL, $data['ticket']['TIK_CodClt'], $soldeClient['SoldeClient'], $soldeClient['Debit'], $soldeClient['Credit'], $observation,
                                    "TICKET FACTURE DEJA", Enum::Success_Code
                                );

                            }
                        } else {
                            $soldeClient = Ticket::SoldeClient($connection, $data['ticket']['TIK_CodClt']);
                            $result[] = $this->setResult((integer)$data['ticket']['TIK_NumTicket'],
                                $data['ticket']['TIK_IdCarnet'],
                                $data['ticket']['TIK_Exerc'], $data['ticket']['TIK_NumTicket_M'],
                                null, $data['ticket']['TIK_CodClt'], $soldeClient['SoldeClient'], $soldeClient['Debit'], $soldeClient['Credit'], null,
                                "Ticket n'existe pas", Enum::Error_Insert_Facture
                            );

                        }
                    }
                    $connection->commit();
                } catch (\Exception|\Throwable $e) {

                    $connection->rollBack();
                    return $e->getMessage();
                }

            }

            if ($request->has('auto_facture') && !!$request->input('auto_facture') && $request->input('auto_facture') == 'true') {
                return $result;
            } else {
                return response()->json($result);
            }
        }


    }
}
