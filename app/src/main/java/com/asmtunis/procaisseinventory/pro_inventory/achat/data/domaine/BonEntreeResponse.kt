package com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class BonEntreeResponse(
    @SerialName("BON_ENT_Exer")
    val bONENTExer: String,
    @SerialName("BON_ENT_Num")
    val bONENTNum: String,
    @SerialName("BON_ENT_Num_M")
    val bONENTNumM: String,
    @SerialName("code")
    val code: Int,
    @SerialName("errors")
    val errors: List<Errors>,
   //val errors: Errors,
    @SerialName("message")
    val message: String
)

