package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.local.repository


import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain.AutreVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain.AutreVCWithImages
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.local.dao.AutreVCDAO
import kotlinx.coroutines.flow.Flow




class AutreVCLocalRepositoryImpl(
    private val autreVCDAO: AutreVCDAO
) : AutreVCLocalRepository {


    override fun upsertAll(value: List<AutreVC>) = autreVCDAO.insertAll(value)
    override fun upsert(value: AutreVC) = autreVCDAO.insert(value)
    override fun updateCloudCode(code: String, codeM: String) =
        autreVCDAO.updateCloudCode(code = code, codeM= codeM)


    override fun deleteAll() = autreVCDAO.deleteAll()
    override fun deleteByCode(codeAutre: String) = autreVCDAO.deleteByCode(codeAutre)

    override fun getAll(): Flow<List<AutreVC>> = autreVCDAO.all
    override fun setDeleted(code: String, codeMobile: String)  =
        autreVCDAO.setDeleted(code, codeMobile)

    override fun restDeleted(code: String,status : String, isSync:Boolean)  = autreVCDAO.restDeleted(code,status, isSync)

    override fun noSyncedToAddOrUpdate(): Flow<List<AutreVC>?>  =
        autreVCDAO.noSyncedToAddOrUpdate

    override fun noSyncedToDelete(): Flow<List<AutreVC>?> =
        autreVCDAO.noSyncedToDelete


    override fun filterByNum(
        searchString: String,
        filterByTypComm: String,
        filterByConcurrent: String,
        sortBy: String,
        isAsc: Int
    ): Flow<List<AutreVCWithImages>> = autreVCDAO.filterByNum(
        searchString = searchString,
        filterByTypComm = filterByTypComm,
        filterByConcurrent = filterByConcurrent,
        sortBy = sortBy,
        isAsc = isAsc
    )

    override fun filterByAutre(
        searchString: String,
        filterByTypComm: String,
        filterByConcurrent: String,
        sortBy: String,
        isAsc: Int
    ): Flow<List<AutreVCWithImages>> = autreVCDAO.filterByAutre(
        searchString = searchString,
        filterByTypComm = filterByTypComm,
        filterByConcurrent = filterByConcurrent,
        sortBy = sortBy,
        isAsc = isAsc
    )

    override fun filterByAutreNote(
        searchString: String,
        filterByTypComm: String,
        filterByConcurrent: String,
        sortBy: String,
        isAsc: Int
    ): Flow<List<AutreVCWithImages>> = autreVCDAO.filterByAutreNote(
    searchString = searchString,
    filterByTypComm = filterByTypComm,
    filterByConcurrent = filterByConcurrent,
    sortBy = sortBy,
    isAsc = isAsc
    )

    override fun getAllFiltred(
        isAsc: Int,
        filterByTypComm: String,
        filterByConcurrent: String,
        sortBy: String
    ): Flow<List<AutreVCWithImages>> = autreVCDAO.getAllFiltred(
        filterByTypComm = filterByTypComm,
        filterByConcurrent = filterByConcurrent,
        sortBy = sortBy,
        isAsc = isAsc
    )

}